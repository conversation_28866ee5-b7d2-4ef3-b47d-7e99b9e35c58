-- Fix Phone Login - Create database function for secure phone lookup
-- Run this in Supabase SQL Editor

-- Create function to get user email by phone for login
CREATE OR REPLACE FUNCTION get_user_email_by_phone(phone_number text)
RETURNS TABLE(email text, phone_verified boolean)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Return user email if phone is verified
  RETURN QUERY
  SELECT p.email, p.phone_verified
  FROM profiles p
  WHERE p.phone = phone_number
    AND (p.phone_verified = true OR p.phone_verified::text = 'true')
  LIMIT 1;
END;
$$;

-- Grant execute permission to anon and authenticated users
GRANT EXECUTE ON FUNCTION get_user_email_by_phone(text) TO anon;
GRANT EXECUTE ON FUNCTION get_user_email_by_phone(text) TO authenticated;

-- Test the function
SELECT * FROM get_user_email_by_phone('+918448609110');

-- Alternative: Check RLS policies on profiles table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename = 'profiles';

-- Alternative: Temporarily disable <PERSON><PERSON> for testing (ONLY FOR DEBUGGING)
-- ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Alternative: Create a more permissive RLS policy for phone login
CREATE POLICY "Allow phone lookup for login" ON profiles
FOR SELECT
TO anon
USING (phone_verified = true OR phone_verified::text = 'true');
