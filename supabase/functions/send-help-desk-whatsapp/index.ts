import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 WhatsApp configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211848599'

    if (!authKey) {
      console.error('MSG91 configuration missing')
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const { helpRequestId } = await req.json()

    if (!helpRequestId) {
      return new Response(
        JSON.stringify({ error: 'Missing required field: helpRequestId' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Processing help desk WhatsApp notification for request:', helpRequestId)

    // Get help request details first
    const { data: helpRequestData, error: helpRequestError } = await supabaseAdmin
      .from('help_requests')
      .select(`
        id,
        user_id,
        subject,
        status,
        ticket_number,
        category,
        venue_id,
        created_at
      `)
      .eq('id', helpRequestId)
      .single()

    if (helpRequestError || !helpRequestData) {
      console.error('Error fetching help request:', helpRequestError)
      return new Response(
        JSON.stringify({ error: 'Help request not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get user information separately
    const { data: userData, error: userError } = await supabaseAdmin
      .from('profiles')
      .select('full_name, email, phone')
      .eq('id', helpRequestData.user_id)
      .single()

    if (userError || !userData) {
      console.error('Error fetching user data:', userError)
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get venue information if venue_id exists
    let venueData: { name: string } | null = null
    if (helpRequestData.venue_id) {
      const { data: venue, error: venueError } = await supabaseAdmin
        .from('venues')
        .select('name')
        .eq('id', helpRequestData.venue_id)
        .single()

      if (!venueError && venue) {
        venueData = venue
      }
    }

    // Get all super admin user IDs first
    const { data: superAdminRoles, error: superAdminError } = await supabaseAdmin
      .from('user_roles')
      .select('user_id')
      .eq('role', 'super_admin')

    if (superAdminError || !superAdminRoles || superAdminRoles.length === 0) {
      console.error('Error fetching super admins:', superAdminError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch super admin users' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get profiles for super admin users
    const superAdminUserIds = superAdminRoles.map(role => role.user_id)
    const { data: superAdminProfiles, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('id, full_name, phone')
      .in('id', superAdminUserIds)
      .not('phone', 'is', null)
      .neq('phone', '')
      .neq('phone', '000000000') // Exclude test phone numbers

    if (profilesError || !superAdminProfiles || superAdminProfiles.length === 0) {
      console.log('No super admin users with valid phone numbers found')
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No super admin users with valid phone numbers found',
          sent_count: 0
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Prepare template variables
    const customerName = userData.full_name || 'Unknown User'
    const subject = helpRequestData.subject || 'General inquiry'
    const ticketNumber = helpRequestData.ticket_number
    const category = getCategoryDisplayName(helpRequestData.category)
    const venueName = venueData?.name || 'General Support'

    // Truncate subject if too long (max 50 chars for WhatsApp template)
    const truncatedSubject = subject.length > 50 ? subject.substring(0, 47) + '...' : subject

    console.log('Template variables:', {
      customerName,
      subject: truncatedSubject,
      ticketNumber,
      category,
      venueName
    })

    // Extract phone numbers for WhatsApp
    const phoneNumbers = superAdminProfiles.map(admin => {
      let phone = admin.phone
      // Ensure phone number is in international format
      if (phone.startsWith('0')) {
        phone = '+91' + phone.substring(1)
      } else if (!phone.startsWith('+')) {
        phone = '+91' + phone
      }
      return phone
    })

    console.log('Sending WhatsApp notifications to super admins:', phoneNumbers)

    // Prepare MSG91 WhatsApp API payload
    // Create separate to_and_components entry for each phone number to prevent duplicates
    const toAndComponents = phoneNumbers.map(phoneNumber => ({
      to: [phoneNumber], // Each entry should have a single phone number in array
      components: {
        body_1: {
          type: "text",
          value: customerName
        },
        body_2: {
          type: "text",
          value: truncatedSubject
        },
        body_3: {
          type: "text",
          value: ticketNumber
        },
        body_4: {
          type: "text",
          value: category
        },
        body_5: {
          type: "text",
          value: venueName
        }
      }
    }))

    const msg91Payload = {
      integrated_number: integratedNumber,
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: "grid2play_help_admin",
          language: {
            code: "en",
            policy: "deterministic"
          },
          namespace: "380c0d5c_8b3e_43ac_a4a3_183fea1845af",
          to_and_components: toAndComponents
        }
      }
    }

    console.log('Sending WhatsApp notification via MSG91:', { 
      template: 'grid2play_help_admin',
      recipients: phoneNumbers.length,
      ticketNumber 
    })

    // Send WhatsApp notification via MSG91 API
    const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('MSG91 WhatsApp API error:', errorText)
      
      return new Response(
        JSON.stringify({ 
          success: false,
          error: `WhatsApp delivery failed: ${response.status} ${response.statusText}`,
          details: errorText 
        }),
        { 
          status: response.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const responseData = await response.json()
    console.log('WhatsApp help desk notification sent successfully:', responseData)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Help desk WhatsApp notifications sent successfully',
        sent_count: phoneNumbers.length,
        ticket_number: ticketNumber,
        recipients: phoneNumbers
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in send-help-desk-whatsapp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Helper function to convert category codes to display names
function getCategoryDisplayName(category: string): string {
  const categoryMap: { [key: string]: string } = {
    'booking_issues': 'Booking Issues',
    'facility_questions': 'Facility Questions',
    'payment_problems': 'Payment Problems',
    'general': 'General Support'
  }
  
  return categoryMap[category] || 'General Support'
}
