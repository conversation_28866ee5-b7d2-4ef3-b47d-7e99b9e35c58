
-- Function to update settlement status and details (Super Admin only)
CREATE OR REPLACE FUNCTION public.update_settlement_details(
  p_settlement_id UUID,
  p_new_status TEXT,
  p_notes TEXT DEFAULT NULL,
  p_expected_settlement_date DATE DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_status TEXT;
  v_settlement_exists BOOLEAN := FALSE;
  v_user_role TEXT;
  v_admin_name TEXT;
  v_result JSON;
BEGIN
  -- Check if user is super admin
  SELECT CASE 
    WHEN EXISTS(SELECT 1 FROM user_roles WHERE user_id = auth.uid() AND role = 'super_admin') THEN 'super_admin'
    ELSE 'unauthorized'
  END INTO v_user_role;
  
  IF v_user_role != 'super_admin' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Only super administrators can update settlements'
    );
  END IF;
  
  -- Get admin name for tracking
  SELECT full_name INTO v_admin_name
  FROM profiles 
  WHERE id = auth.uid();
  
  -- Check if settlement exists and get current status
  SELECT status INTO v_current_status
  FROM settlements
  WHERE id = p_settlement_id;
  
  IF v_current_status IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Settlement not found'
    );
  END IF;
  
  -- Validate status transition
  IF p_new_status NOT IN ('pending', 'processed', 'settled') THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid status. Must be pending, processed, or settled'
    );
  END IF;
  
  -- Update settlement based on new status
  IF p_new_status = 'processed' AND v_current_status = 'pending' THEN
    UPDATE settlements SET
      status = p_new_status,
      notes = COALESCE(p_notes, notes),
      expected_settlement_date = COALESCE(p_expected_settlement_date, expected_settlement_date),
      processed_at = NOW(),
      processed_by = auth.uid(),
      updated_at = NOW()
    WHERE id = p_settlement_id;
    
  ELSIF p_new_status = 'settled' AND v_current_status IN ('pending', 'processed') THEN
    UPDATE settlements SET
      status = p_new_status,
      notes = COALESCE(p_notes, notes),
      expected_settlement_date = COALESCE(p_expected_settlement_date, expected_settlement_date),
      settled_at = NOW(),
      settled_by = auth.uid(),
      processed_at = CASE WHEN processed_at IS NULL THEN NOW() ELSE processed_at END,
      processed_by = CASE WHEN processed_by IS NULL THEN auth.uid() ELSE processed_by END,
      updated_at = NOW()
    WHERE id = p_settlement_id;
    
  ELSIF p_new_status = v_current_status THEN
    -- Just update notes and expected date if status is same
    UPDATE settlements SET
      notes = COALESCE(p_notes, notes),
      expected_settlement_date = COALESCE(p_expected_settlement_date, expected_settlement_date),
      updated_at = NOW()
    WHERE id = p_settlement_id;
    
  ELSE
    RETURN json_build_object(
      'success', false,
      'error', 'Invalid status transition from ' || v_current_status || ' to ' || p_new_status
    );
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'message', 'Settlement updated successfully',
    'settlement_id', p_settlement_id,
    'old_status', v_current_status,
    'new_status', p_new_status,
    'updated_by', v_admin_name
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Database error: ' || SQLERRM
    );
END;
$$;
