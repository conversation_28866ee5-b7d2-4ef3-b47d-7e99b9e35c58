import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone, otp, action, purpose } = await req.json()

    // Validate input
    if (!phone) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number is required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Format phone number
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (action === 'send') {
      // Send OTP for login or phone verification
      const isPhoneVerification = purpose === 'phone_verification'
      console.log(`Sending WhatsApp ${isPhoneVerification ? 'verification' : 'login'} OTP to:`, formattedPhone)

      if (isPhoneVerification) {
        // For phone verification, get current authenticated user
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Authentication required for phone verification'
            }),
            {
              status: 401,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Get user from auth token
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

        if (authError || !user) {
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Invalid authentication'
            }),
            {
              status: 401,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Check if phone is already verified by another user
        const { data: existingProfile, error: existingError } = await supabaseAdmin
          .from('profiles')
          .select('id, phone_verified')
          .eq('phone', formattedPhone)
          .eq('phone_verified', true)
          .neq('id', user.id)
          .maybeSingle()

        if (existingError) {
          console.error('Error checking existing profile:', existingError)
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Database error'
            }),
            {
              status: 500,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        if (existingProfile) {
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Phone number already verified by another user'
            }),
            {
              status: 409,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Get current user profile
        const { data: currentProfile, error: profileError } = await supabaseAdmin
          .from('profiles')
          .select('id, full_name, email')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile lookup error:', profileError)
          return new Response(
            JSON.stringify({
              success: false,
              error: 'User profile not found'
            }),
            {
              status: 404,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Use existing send-whatsapp-otp function for phone verification
        const { data, error } = await supabaseAdmin.functions.invoke('send-whatsapp-otp', {
          body: {
            phone: formattedPhone,
            full_name: currentProfile.full_name || 'User',
            password: 'temp_verification_password', // Not used for verification
            isPhoneVerification: true,
            userId: user.id
          }
        })

        if (error || !data?.success) {
          console.error('Failed to send WhatsApp verification OTP:', error || data?.error)
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Failed to send WhatsApp OTP'
            }),
            {
              status: 500,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        return new Response(
          JSON.stringify({
            success: true,
            message: 'WhatsApp verification OTP sent successfully',
            phone: formattedPhone,
            expires_in: 300
          }),
          {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      } else {
        // Original login flow - check if user exists with verified phone
        const { data: profiles, error: profileError } = await supabaseAdmin
          .from('profiles')
          .select('id, phone_verified, full_name, email')
          .eq('phone', formattedPhone)
          .eq('phone_verified', true)

      if (profileError) {
        console.error('Profile lookup error:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

        if (!profiles || profiles.length === 0) {
          console.error('No verified profile found for phone:', formattedPhone)
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Phone number not found or not verified. Please register first.'
            }),
            {
              status: 404,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Use the first (and should be only) profile
        const profile = profiles[0]

        console.log('Found verified profile for login:', profile.id)

        // Use existing send-whatsapp-otp function
        const { data, error } = await supabaseAdmin.functions.invoke('send-whatsapp-otp', {
          body: {
            phone: formattedPhone,
            full_name: profile.full_name || 'User',
            password: 'temp_login_password', // Not used for login OTP
            isLogin: true
          }
        })

        if (error || !data?.success) {
          console.error('Failed to send WhatsApp OTP:', error || data?.error)
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Failed to send WhatsApp OTP'
            }),
            {
              status: 500,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        return new Response(
          JSON.stringify({
            success: true,
            message: 'WhatsApp OTP sent successfully'
          }),
          {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

    } else if (action === 'verify') {
      // Verify OTP for login or phone verification
      if (!otp) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP is required'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const isPhoneVerification = purpose === 'phone_verification'
      console.log(`Verifying WhatsApp ${isPhoneVerification ? 'verification' : 'login'} OTP for:`, formattedPhone)

      // Validate OTP using database function
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_whatsapp_otp', { 
          phone_number: formattedPhone, 
          otp_input: otp 
        })

      if (validationError) {
        console.error('OTP validation error:', validationError)
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'OTP validation failed' 
          }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      const validation = validationResult[0]
      if (!validation || !validation.is_valid) {
        return new Response(
          JSON.stringify({
            success: false,
            error: validation?.error_message || 'Invalid OTP'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Handle phone verification
      if (isPhoneVerification) {
        // Get current authenticated user
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Authentication required for phone verification'
            }),
            {
              status: 401,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Get user from auth token
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

        if (authError || !user) {
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Invalid authentication'
            }),
            {
              status: 401,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Update user profile to mark phone as verified
        const { error: profileError } = await supabaseAdmin
          .from('profiles')
          .update({
            phone: formattedPhone,
            phone_verified: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id)

        if (profileError) {
          console.error('Error updating profile:', profileError)
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Failed to update profile'
            }),
            {
              status: 500,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }

        // Update auth.users metadata
        const { error: authUpdateError } = await supabaseAdmin.auth.admin.updateUserById(
          user.id,
          {
            phone: formattedPhone,
            phone_confirm: true,
            user_metadata: {
              ...user.user_metadata,
              phone_verified: true,
              phone: formattedPhone
            }
          }
        )

        if (authUpdateError) {
          console.error('Error updating auth user:', authUpdateError)
          // Don't fail the request if auth update fails, profile is already updated
        }

        // Clean up OTP data
        await supabaseAdmin
          .from('pending_whatsapp_users')
          .delete()
          .eq('phone', formattedPhone)

        console.log('Phone verification completed successfully for user:', user.id)
        return new Response(
          JSON.stringify({
            success: true,
            message: 'Phone number verified successfully',
            user: {
              id: user.id,
              phone: formattedPhone,
              phone_verified: true
            }
          }),
          {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // OTP is valid, get user profile
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)

      if (profileError) {
        console.error('Profile lookup error after OTP validation:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!profiles || profiles.length === 0) {
        console.error('No verified profile found for phone after OTP validation:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use the first (and should be only) profile
      const profile = profiles[0]

      // Get user from auth.users
      const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(profile.id)

      if (authError || !authUser.user) {
        console.error('Auth user lookup error:', authError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Authentication failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Create session using the user's email (temp or real)
      const userEmail = authUser.user.email
      
      if (!userEmail) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User authentication method not found'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Generate magic link for session
      const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'magiclink',
        email: userEmail,
        options: {
          redirectTo: `${req.headers.get('origin') || 'https://grid2play.com'}/dashboard`
        }
      })

      if (sessionError) {
        console.error('Error generating session:', sessionError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to create session'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Clean up OTP data
      await supabaseAdmin
        .from('whatsapp_otps')
        .delete()
        .eq('phone', formattedPhone)

      console.log('WhatsApp OTP login successful for user:', profile.id)

      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: profile.id,
            phone: profile.phone,
            full_name: profile.full_name,
            email: profile.email?.includes('@temp.grid2play.com') ? null : profile.email,
            phone_verified: profile.phone_verified,
            email_verified: profile.email_verified
          },
          session_url: sessionData?.properties?.action_link || null
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid action. Use "send" or "verify"'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Simple WhatsApp login error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
