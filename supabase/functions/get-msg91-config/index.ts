import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // For MSG91 configuration, we allow unauthenticated access
    // since we need to send emails during registration before users are authenticated
    // In production, you might want to add additional security measures like:
    // - Rate limiting
    // - API key validation
    // - Origin checking

    console.log('MSG91 config requested from:', req.headers.get('origin'))

    // Get MSG91 configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const domain = Deno.env.get('MSG91_DOMAIN')

    if (!authKey || !domain) {
      console.error('MSG91 configuration missing:', { authKey: !!authKey, domain: !!domain })
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    return new Response(
      JSON.stringify({
        authKey,
        domain
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in get-msg91-config function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
