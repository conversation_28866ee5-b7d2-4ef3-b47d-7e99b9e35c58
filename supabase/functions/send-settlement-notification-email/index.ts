import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🏦 Settlement notification email request received')

    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const domain = Deno.env.get('MSG91_DOMAIN')

    if (!authKey || !domain) {
      console.error('MSG91 configuration missing:', { authKey: !!authKey, domain: !!domain })
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const { settlementId } = await req.json()

    if (!settlementId) {
      return new Response(
        JSON.stringify({ error: 'Missing required field: settlementId' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('📊 Getting settlement data for ID:', settlementId)

    // Get settlement data using the database function
    const { data: settlementData, error: settlementError } = await supabaseClient
      .rpc('get_settlement_email_data', { p_settlement_id: settlementId })

    if (settlementError) {
      console.error('❌ Error getting settlement data:', settlementError)
      return new Response(
        JSON.stringify({ error: `Failed to get settlement data: ${settlementError.message}` }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!settlementData || settlementData.length === 0) {
      console.error('❌ No settlement data found for ID:', settlementId)
      return new Response(
        JSON.stringify({ error: 'Settlement not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const settlement = settlementData[0]
    console.log('✅ Settlement data retrieved:', {
      reference: settlement.settlement_reference,
      venue: settlement.venue_name,
      admin: settlement.venue_admin_name,
      email: settlement.venue_admin_email
    })

    // Check if email was already sent
    if (settlement.settlement_email_sent) {
      console.log('⚠️ Settlement email already sent for:', settlement.settlement_reference)
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Settlement email already sent',
          alreadySent: true 
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create dashboard link
    const dashboardLink = `https://grid2play.com/admin/settlements-mobile?settlement=${settlement.settlement_reference}`

    // Prepare template variables for MSG91
    const templateVariables = {
      venueAdminName: settlement.venue_admin_name || 'Admin',
      venueName: settlement.venue_name,
      settlementReference: settlement.settlement_reference,
      settlementPeriod: settlement.settlement_period,
      totalBookings: settlement.total_bookings.toString(),
      grossRevenue: settlement.gross_revenue,
      platformFees: settlement.platform_fee_amount,
      netRevenue: settlement.net_revenue,
      dashboardLink: dashboardLink
    }

    console.log('📧 Preparing email with template variables:', templateVariables)

    // Prepare MSG91 API request payload
    const msg91Payload = {
      recipients: [
        {
          to: [
            {
              email: settlement.venue_admin_email,
              name: settlement.venue_admin_name || 'Admin'
            }
          ],
          variables: templateVariables
        }
      ],
      from: {
        email: `no-reply@${domain}`,
        name: "Grid2Play"
      },
      domain: domain,
      template_id: 'settlement_notification_grid2play2'
    }

    console.log('📤 Sending settlement notification email via MSG91:', {
      to: settlement.venue_admin_email,
      templateId: 'settlement_notification_grid2play2',
      settlementReference: settlement.settlement_reference
    })

    // Send email via MSG91 API
    const response = await fetch('https://control.msg91.com/api/v5/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    const responseData = await response.text()
    console.log('📬 MSG91 API response:', { status: response.status, data: responseData })

    if (!response.ok) {
      console.error('❌ MSG91 API error:', { status: response.status, response: responseData })
      
      // Update settlement with email error
      await supabaseClient
        .from('settlements')
        .update({ 
          settlement_email_error: `MSG91 API error: ${response.status} - ${responseData}` 
        })
        .eq('id', settlementId)

      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `MSG91 API error: ${response.status}`,
          details: responseData 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Update settlement with successful email status
    const { error: updateError } = await supabaseClient
      .from('settlements')
      .update({ 
        settlement_email_sent: true,
        settlement_email_sent_at: new Date().toISOString(),
        settlement_email_error: null
      })
      .eq('id', settlementId)

    if (updateError) {
      console.error('⚠️ Failed to update settlement email status:', updateError)
      // Don't fail the request since email was sent successfully
    }

    console.log('✅ Settlement notification email sent successfully!')

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Settlement notification email sent successfully',
        settlementReference: settlement.settlement_reference,
        sentTo: settlement.venue_admin_email
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('💥 Error in send-settlement-notification-email function:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
