import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { email, userId, userName } = await req.json()

    // Validate input
    if (!email || !userId || !userName) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Email, userId, and userName are required' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Creating email verification for WhatsApp user:', { email, userId })

    // Generate a unique verification token
    const verificationToken = crypto.randomUUID()

    // Store the verification token in the database
    const { error: tokenError } = await supabaseAdmin
      .from('email_verification_tokens')
      .insert({
        user_id: userId,
        email: email.toLowerCase().trim(),
        token: verificationToken,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        created_at: new Date().toISOString()
      })

    if (tokenError) {
      console.error('Error storing verification token:', tokenError)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Failed to create verification token' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get MSG91 configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const domain = Deno.env.get('MSG91_DOMAIN') || 'grid2play.com'

    if (!authKey) {
      console.error('MSG91_AUTH_KEY not found in environment variables')
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Email service configuration error - missing auth key'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Create verification link with token
    const baseUrl = req.headers.get('origin') || 'https://grid2play.com'
    const verificationLink = `${baseUrl}/verify-email-token?token=${verificationToken}`

    // Prepare MSG91 email payload
    const msg91Payload = {
      recipients: [
        {
          to: [
            {
              email: email.toLowerCase().trim(),
              name: userName
            }
          ],
          variables: {
            firstName: userName,
            verificationLink: verificationLink
          }
        }
      ],
      from: {
        email: `no-reply@${domain}`,
        name: "Grid2Play"
      },
      domain: domain,
      template_id: 'authentication_grid2play'
    }

    console.log('Sending verification email via MSG91:', { 
      to: email, 
      verificationLink: verificationLink.substring(0, 50) + '...' 
    })

    // Send email via MSG91
    const response = await fetch('https://control.msg91.com/api/v5/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    const responseData = await response.json()

    if (!response.ok) {
      console.error('MSG91 API error:', responseData)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Failed to send verification email' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Verification email sent successfully:', responseData)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Verification email sent successfully',
        tokenId: verificationToken
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in send-whatsapp-email-verification:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
