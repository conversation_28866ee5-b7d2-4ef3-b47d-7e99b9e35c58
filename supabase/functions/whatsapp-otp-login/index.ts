import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone, otp, action } = await req.json()

    // Validate input
    if (!phone || (!otp && action !== 'send')) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number and OTP are required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Format phone number
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (action === 'send') {
      // Send OTP for login
      console.log('Sending WhatsApp login OTP to:', formattedPhone)

      // Check if user exists
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found or not verified. Please register first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use existing WhatsApp OTP system
      const { data, error } = await supabaseAdmin.functions.invoke('send-whatsapp-otp', {
        body: {
          phone: formattedPhone,
          isLogin: true
        }
      })

      if (error || !data.success) {
        console.error('Failed to send WhatsApp OTP:', error || data.error)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send WhatsApp OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'WhatsApp OTP sent successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      // Verify OTP and login
      console.log('Verifying WhatsApp login OTP for:', formattedPhone)

      // Validate OTP using database function
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_whatsapp_otp', { 
          phone_number: formattedPhone, 
          otp_input: otp 
        })

      if (validationError) {
        console.error('OTP validation error:', validationError)
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'OTP validation failed' 
          }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      const validation = validationResult[0]
      if (!validation || !validation.is_valid) {
        return new Response(
          JSON.stringify({ 
            success: false,
            error: validation?.error_message || 'Invalid OTP' 
          }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // OTP is valid, get user profile
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Generate session for the user
      const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'magiclink',
        email: profile.email || `${profile.id}@temp.grid2play.com`,
        options: {
          redirectTo: `${req.headers.get('origin') || 'https://grid2play.com'}/dashboard`
        }
      })

      if (sessionError) {
        console.error('Error generating session:', sessionError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to create session'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Clean up OTP data
      await supabaseAdmin
        .from('whatsapp_otps')
        .delete()
        .eq('phone', formattedPhone)

      console.log('WhatsApp OTP login successful for user:', profile.id)

      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: profile.id,
            phone: profile.phone,
            full_name: profile.full_name,
            email: profile.email?.includes('@temp.grid2play.com') ? null : profile.email,
            phone_verified: profile.phone_verified,
            email_verified: profile.email && !profile.email.includes('@temp.grid2play.com')
          },
          session_url: sessionData?.properties?.action_link || null
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('WhatsApp OTP login error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
