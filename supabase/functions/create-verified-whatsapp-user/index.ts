import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { phone, password, full_name } = await req.json()

    if (!phone || !password || !full_name) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: phone, password, full_name' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean phone number
    const cleanPhone = phone.replace(/[\s()-]/g, '')

    console.log('Creating verified WhatsApp user for phone:', cleanPhone)

    // Check if phone already exists in profiles
    const { data: existingProfile, error: profileCheckError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('phone', cleanPhone)
      .maybeSingle()

    if (profileCheckError) {
      console.error('Error checking existing profile:', profileCheckError)
      return new Response(
        JSON.stringify({ error: 'Database error' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (existingProfile) {
      return new Response(
        JSON.stringify({ error: 'Phone number already registered' }),
        { 
          status: 409, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create user in Supabase Auth with phone
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      phone: cleanPhone,
      password: password,
      user_metadata: {
        full_name: full_name.trim(),
        phone: cleanPhone,
        registration_method: 'whatsapp'
      },
      phone_confirm: true // Mark phone as confirmed since OTP was verified
    })

    if (authError) {
      console.error('Error creating Supabase user:', authError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user account',
          details: authError.message 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!authData.user) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'User creation failed - no user data returned' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('User created successfully:', authData.user.id)

    // Create/update profile with verification status
    const { error: profileError } = await supabaseAdmin.from('profiles').upsert({
      id: authData.user.id,
      full_name: full_name.trim(),
      phone: cleanPhone,
      email_verified: false,
      phone_verified: true, // Mark phone as verified
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })

    if (profileError) {
      console.error('Error creating profile:', profileError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user profile',
          details: profileError.message 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Profile created successfully with phone_verified: true')

    // Create default user role
    const { error: roleError } = await supabaseAdmin.from('user_roles').insert({
      user_id: authData.user.id,
      role: 'user'
    })

    if (roleError) {
      console.error('Error creating user role:', roleError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user role',
          details: roleError.message 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('User role created successfully')

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'WhatsApp user created successfully',
        user: {
          id: authData.user.id,
          phone: cleanPhone,
          full_name: full_name.trim(),
          phone_verified: true,
          email_verified: false
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in create-verified-whatsapp-user function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
