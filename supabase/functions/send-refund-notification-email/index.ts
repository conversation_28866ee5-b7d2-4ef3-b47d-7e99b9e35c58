import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🚀 Refund notification email function started')

    // Simplified and robust authentication check for database trigger calls
    // Following the same pattern as booking confirmation emails
    const authHeader = req.headers.get('authorization');
    const userAgent = req.headers.get('user-agent') || '';
    const contentType = req.headers.get('content-type') || '';

    // Primary detection: pg_net user agent (most reliable)
    const isPgNetCall = userAgent.includes('pg_net') || userAgent.startsWith('pg_net/');
    const hasValidContentType = contentType.includes('application/json');

    console.log('🔍 Authentication check:', {
      userAgent: userAgent,
      contentType: contentType,
      isPgNetCall: isPgNetCall,
      hasValidContentType: hasValidContentType,
      hasAuth: !!authHeader,
      host: req.headers.get('host')
    });

    // PRIORITY 1: Always allow pg_net calls (they come from our database)
    if (isPgNetCall) {
      console.log('✅ pg_net user agent detected - allowing database trigger request');
    }
    // PRIORITY 2: Allow unauthenticated calls with valid content type (fallback for triggers)
    else if (!authHeader && hasValidContentType) {
      console.log('✅ Unauthenticated call with valid content type - allowing as potential trigger');
    }
    // PRIORITY 3: Require authentication for all other calls
    else if (!authHeader) {
      console.log('❌ No authorization header and not recognized as database trigger');
      return new Response(
        JSON.stringify({
          error: 'Authorization required for non-trigger calls',
          debug: {
            userAgent: userAgent,
            contentType: contentType,
            isPgNetCall: isPgNetCall,
            hasValidContentType: hasValidContentType,
            message: 'Database triggers should use pg_net user agent'
          }
        }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Create Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ Supabase configuration missing')
      return new Response(
        JSON.stringify({ error: 'Supabase configuration not found' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get MSG91 configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const domain = Deno.env.get('MSG91_DOMAIN')

    if (!authKey || !domain) {
      console.error('❌ MSG91 configuration missing:', { authKey: !!authKey, domain: !!domain })
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const { cancellationId } = await req.json()

    if (!cancellationId) {
      console.error('❌ Missing cancellationId in request')
      return new Response(
        JSON.stringify({ error: 'Missing cancellationId' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('📧 Processing refund notification email for cancellation:', cancellationId)

    // Get cancellation details with booking and user information using raw SQL for better control
    const { data: cancellationData, error: cancellationError } = await supabase
      .rpc('get_refund_email_data', { cancellation_id_param: cancellationId })

    if (cancellationError) {
      console.error('❌ Error fetching cancellation data:', cancellationError)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to fetch cancellation data',
          details: cancellationError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!cancellationData || cancellationData.length === 0) {
      console.log('⚠️ No cancellation data found for ID:', cancellationId)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Cancellation not found or not eligible for email'
        }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const cancellation = cancellationData[0]
    console.log('📋 Cancellation data retrieved:', {
      cancellationId: cancellation.cancellation_id,
      recipientName: cancellation.user_name,
      recipientEmail: cancellation.user_email,
      refundStatus: cancellation.refund_status
    })

    // Check if user has verified email
    if (!cancellation.user_email) {
      console.log('⚠️ User has no email address, skipping email notification')
      return new Response(
        JSON.stringify({ success: true, message: 'No email address found, skipped email notification' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Determine the actual refund amount (admin amount takes precedence)
    const actualRefundAmount = cancellation.admin_refund_amount || cancellation.refund_amount || 0

    // Format processing date
    const processingDate = cancellation.refund_processed_at
      ? new Date(cancellation.refund_processed_at).toLocaleDateString('en-IN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          timeZone: 'Asia/Kolkata'
        })
      : new Date().toLocaleDateString('en-IN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          timeZone: 'Asia/Kolkata'
        })

    // Prepare template variables for MSG91
    const templateVariables = {
      userName: cancellation.user_name,
      bookingReference: cancellation.booking_reference,
      refundStatus: cancellation.refund_status,
      refundAmount: actualRefundAmount.toString(),
      refundId: cancellation.razorpay_refund_id || 'N/A',
      processingDate: processingDate,
      refundNotes: cancellation.refund_notes || '',
      // Conditional flags for template
      refundStatus_processed: cancellation.refund_status === 'processed',
      refundStatus_rejected: cancellation.refund_status === 'rejected',
      refundStatus_not_applicable: cancellation.refund_status === 'not_applicable'
    }

    console.log('📧 Preparing refund email with template variables:', {
      to: cancellation.user_email,
      userName: templateVariables.userName,
      bookingReference: templateVariables.bookingReference,
      refundStatus: templateVariables.refundStatus,
      refundAmount: templateVariables.refundAmount
    })

    // Prepare MSG91 API request payload
    const msg91Payload = {
      recipients: [
        {
          to: [
            {
              email: cancellation.user_email,
              name: cancellation.user_name
            }
          ],
          variables: templateVariables
        }
      ],
      from: {
        email: `no-reply@${domain}`,
        name: "Grid2Play"
      },
      domain: domain,
      template_id: 'refund_grid2play'
    }

    console.log('📤 Sending refund notification email via MSG91:', {
      to: cancellation.user_email,
      templateId: 'refund_grid2play',
      bookingReference: cancellation.booking_reference,
      refundStatus: cancellation.refund_status
    })

    // Send email via MSG91 API
    const response = await fetch('https://control.msg91.com/api/v5/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    const responseData = await response.text()
    console.log('📧 MSG91 API Response:', { status: response.status, data: responseData })

    if (!response.ok) {
      console.error('❌ MSG91 API error:', { status: response.status, response: responseData })
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `MSG91 API error: ${response.status}`,
          details: responseData 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('✅ Refund notification email sent successfully')
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Refund notification email sent successfully',
        recipient: cancellation.user_email,
        bookingReference: cancellation.booking_reference,
        data: responseData
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('❌ Error in send-refund-notification-email function:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
