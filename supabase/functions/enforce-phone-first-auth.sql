-- Enforce Phone-First Authentication for Grid2Play
-- This SQL script creates database triggers and functions to enforce phone verification requirements
-- Uses existing MSG91 WhatsApp integration and Edge Functions
-- Google OAuth has been removed for security and simplicity
-- Run this in Supabase SQL Editor

-- Create function to prevent profile updates that bypass phone verification
CREATE OR REPLACE FUNCTION public.enforce_phone_verification_immutability()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Prevent changing phone number if it's already verified
  IF OLD.phone_verified = true AND NEW.phone != OLD.phone THEN
    RAISE EXCEPTION 'Cannot change phone number once verified. Contact support for assistance.';
  END IF;
  
  -- Prevent changing email if it's already verified
  IF OLD.email_verified = true AND NEW.email != OLD.email THEN
    RAISE EXCEPTION 'Cannot change email address once verified. Contact support for assistance.';
  END IF;
  
  -- Prevent setting phone_verified to false if it's already true
  IF OLD.phone_verified = true AND NEW.phone_verified = false THEN
    RAISE EXCEPTION 'Cannot unverify phone number. Contact support for assistance.';
  END IF;
  
  -- Prevent setting email_verified to false if it's already true
  IF OLD.email_verified = true AND NEW.email_verified = false THEN
    RAISE EXCEPTION 'Cannot unverify email address. Contact support for assistance.';
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger for profile immutability enforcement
DROP TRIGGER IF EXISTS enforce_verification_immutability ON public.profiles;
CREATE TRIGGER enforce_verification_immutability
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.enforce_phone_verification_immutability();

-- Create function to check phone verification requirement for critical actions
CREATE OR REPLACE FUNCTION public.check_phone_verification_required(user_id_param uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  phone_verified_status boolean;
BEGIN
  SELECT phone_verified INTO phone_verified_status
  FROM public.profiles
  WHERE id = user_id_param;
  
  -- Return true if phone verification is required (phone_verified = false or null)
  RETURN COALESCE(phone_verified_status, false) = false;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.enforce_phone_verification_immutability() TO service_role;
GRANT EXECUTE ON FUNCTION public.check_phone_verification_required(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_phone_verification_required(uuid) TO anon;

-- Create RLS policy to ensure users must verify phone for bookings
CREATE POLICY "Users must verify phone for bookings" ON public.bookings
  FOR INSERT WITH CHECK (
    NOT public.check_phone_verification_required(auth.uid())
  );

-- Update existing RLS policies to enforce phone verification
-- Note: This will prevent unverified users from making bookings until they verify their phone

-- Test the functions (uncomment to test)
-- SELECT public.check_phone_verification_required('00000000-0000-0000-0000-000000000000'::uuid);

-- Display current verification status for all users
SELECT 
  p.id,
  p.full_name,
  p.email,
  p.phone,
  p.phone_verified,
  p.email_verified,
  CASE
    WHEN p.phone_verified = true THEN 'Phone Verified ✓'
    WHEN p.phone IS NOT NULL AND p.phone_verified = false THEN 'Phone Not Verified ⚠️'
    ELSE 'Registration Pending ⏳'
  END as verification_status
FROM public.profiles p
ORDER BY p.created_at DESC
LIMIT 10;
