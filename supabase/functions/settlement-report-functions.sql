
-- Function to get detailed settlement report data
CREATE OR REPLACE FUNCTION public.get_settlement_report_data(
  p_settlement_id UUID
)
RETURNS TABLE(
  booking_id UUID,
  booking_date DATE,
  customer_name TEXT,
  customer_phone TEXT,
  guest_name TEXT,
  guest_phone TEXT,
  court_name TEXT,
  sport_name TEXT,
  start_time TIME,
  end_time TIME,
  total_price NUMERIC,
  platform_fee NUMERIC,
  net_amount NUMERIC,
  payment_method TEXT,
  payment_status TEXT,
  booking_reference TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_settlement_start DATE;
  v_settlement_end DATE;
  v_venue_id UUID;
  v_platform_fee_percentage NUMERIC;
BEGIN
  -- Get settlement details
  SELECT 
    settlement_week_start,
    settlement_week_end,
    venue_id
  INTO 
    v_settlement_start,
    v_settlement_end,
    v_venue_id
  FROM settlements
  WHERE id = p_settlement_id;
  
  IF v_settlement_start IS NULL THEN
    RAISE EXCEPTION 'Settlement not found';
  END IF;
  
  -- Get venue platform fee percentage
  SELECT platform_fee_percentage INTO v_platform_fee_percentage
  FROM venues 
  WHERE id = v_venue_id;
  
  -- Return detailed booking data for the settlement period
  RETURN QUERY
  SELECT 
    b.id as booking_id,
    b.booking_date,
    p.full_name as customer_name,
    p.phone as customer_phone,
    b.guest_name,
    b.guest_phone,
    c.name as court_name,
    s.name as sport_name,
    b.start_time,
    b.end_time,
    b.total_price,
    ROUND(b.total_price * (COALESCE(v_platform_fee_percentage, 5.0) / 100), 2) as platform_fee,
    ROUND(b.total_price * (1 - COALESCE(v_platform_fee_percentage, 5.0) / 100), 2) as net_amount,
    b.payment_method,
    b.payment_status,
    b.booking_reference
  FROM bookings b
  LEFT JOIN profiles p ON b.user_id = p.id
  LEFT JOIN courts c ON b.court_id = c.id
  LEFT JOIN sports s ON c.sport_id = s.id
  WHERE c.venue_id = v_venue_id
    AND b.booking_date >= v_settlement_start
    AND b.booking_date <= v_settlement_end
    AND b.status = 'confirmed'
  ORDER BY b.booking_date ASC, b.start_time ASC;
  
END;
$$;
