import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { token } = await req.json()

    // Validate input
    if (!token) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Verification token is required' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Verifying email token for WhatsApp user')

    // Find the verification token
    const { data: tokenData, error: tokenError } = await supabaseAdmin
      .from('email_verification_tokens')
      .select('*')
      .eq('token', token)
      .eq('used', false)
      .gt('expires_at', new Date().toISOString())
      .single()

    if (tokenError || !tokenData) {
      console.error('Invalid or expired token:', tokenError)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid or expired verification token' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Valid token found for user:', tokenData.user_id)

    // Mark token as used
    const { error: markUsedError } = await supabaseAdmin
      .from('email_verification_tokens')
      .update({ 
        used: true, 
        used_at: new Date().toISOString() 
      })
      .eq('token', token)

    if (markUsedError) {
      console.error('Error marking token as used:', markUsedError)
    }

    // Update user's profile to set email and email_verified
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        email: tokenData.email,
        email_verified: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', tokenData.user_id)

    if (profileError) {
      console.error('Error updating profile:', profileError)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Failed to update user profile' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Update auth.users table to add email
    const { error: authUpdateError } = await supabaseAdmin.auth.admin.updateUserById(
      tokenData.user_id,
      {
        email: tokenData.email,
        email_confirm: true,
        user_metadata: {
          email_verified: true
        }
      }
    )

    if (authUpdateError) {
      console.error('Error updating auth user:', authUpdateError)
      // Don't fail the request if auth update fails, profile is already updated
      console.log('Profile updated successfully, auth update failed but continuing')
    }

    console.log('Email verification completed successfully for user:', tokenData.user_id)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Email verified successfully',
        user: {
          id: tokenData.user_id,
          email: tokenData.email,
          email_verified: true
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in verify-whatsapp-email-token:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
