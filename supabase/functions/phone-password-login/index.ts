import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Note: This is a login function, so we don't require authentication
    // The function itself will validate credentials
    const { phone, password } = await req.json()

    // Validate input
    if (!phone || !password) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number and password are required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Format phone number to ensure it matches database format
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`

    // Initialize Supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Initialize regular Supabase client for auth operations
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    )

    console.log('Phone login attempt for:', formattedPhone)

    // First, check if user exists with this phone number and is phone verified
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('id, phone, phone_verified, full_name')
      .eq('phone', formattedPhone)
      .eq('phone_verified', true)
      .single()

    if (profileError || !profile) {
      console.error('Profile lookup error:', profileError)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number not found or not verified. Please register first.'
        }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Found verified profile:', profile.id)

    // Get the user's email from auth.users table (profile.id is the same as auth.users.id)
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(profile.id)

    if (authError || !authUser.user) {
      console.error('Auth user lookup error:', authError)
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'User account not found' 
        }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const userEmail = authUser.user.email
    const userPhone = authUser.user.phone

    // For WhatsApp users, we need to handle authentication differently
    // Since Supabase phone auth might be disabled, we'll use a custom approach

    console.log('User auth details:', {
      email: userEmail,
      phone: userPhone,
      isTemporaryEmail: userEmail?.includes('@temp.grid2play.com')
    })

    // For WhatsApp users with temporary emails, we need to create a session manually
    if (userEmail && userEmail.includes('@temp.grid2play.com')) {
      console.log('WhatsApp user detected, creating custom session')

      // Verify password by attempting to sign in with the temporary email
      // This validates the password without requiring phone provider
      const { data: tempSignInData, error: tempSignInError } = await supabase.auth.signInWithPassword({
        email: userEmail,
        password: password
      })

      if (tempSignInError) {
        console.error('Password validation failed:', tempSignInError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid phone number or password'
          }),
          {
            status: 401,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Password is valid, use the session
      const signInData = tempSignInData

      console.log('Phone login successful for WhatsApp user:', signInData.user?.id)

      // Return success with user data
      return new Response(
        JSON.stringify({
          success: true,
          user: signInData.user,
          session: signInData.session
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else if (userEmail) {
      // User has a real email, use standard email authentication
      console.log('Attempting email/password auth for:', userEmail)

      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: userEmail,
        password: password
      })

      if (signInError) {
        console.error('Sign in error:', signInError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid phone number or password'
          }),
          {
            status: 401,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Phone login successful for user:', signInData.user?.id)

      // Return success with user data
      return new Response(
        JSON.stringify({
          success: true,
          user: signInData.user,
          session: signInData.session
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'User has no valid authentication method. Please contact support.'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Phone login error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
