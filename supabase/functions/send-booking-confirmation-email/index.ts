import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🚀 Booking confirmation email function started')

    // Simplified and robust authentication check for database trigger calls
    const authHeader = req.headers.get('authorization');
    const userAgent = req.headers.get('user-agent') || '';
    const contentType = req.headers.get('content-type') || '';

    // Primary detection: pg_net user agent (most reliable)
    const isPgNetCall = userAgent.includes('pg_net') || userAgent.startsWith('pg_net/');
    const hasValidContentType = contentType.includes('application/json');

    console.log('🔍 Authentication check:', {
      userAgent: userAgent,
      contentType: contentType,
      isPgNetCall: isPgNetCall,
      hasValidContentType: hasValidContentType,
      hasAuth: !!authHeader,
      host: req.headers.get('host')
    });

    // PRIORITY 1: Always allow pg_net calls (they come from our database)
    if (isPgNetCall) {
      console.log('✅ pg_net user agent detected - allowing database trigger request');
    }
    // PRIORITY 2: Allow unauthenticated calls with valid content type (fallback for triggers)
    else if (!authHeader && hasValidContentType) {
      console.log('✅ Unauthenticated call with valid content type - allowing as potential trigger');
    }
    // PRIORITY 3: Require authentication for all other calls
    else if (!authHeader) {
      console.log('❌ No authorization header and not recognized as database trigger');
      console.log('❌ Request details:', {
        method: req.method,
        userAgent: userAgent,
        contentType: contentType,
        hasValidContentType: hasValidContentType
      });
      return new Response(
        JSON.stringify({
          error: 'Authorization required for non-trigger calls',
          debug: {
            userAgent: userAgent,
            contentType: contentType,
            isPgNetCall: isPgNetCall,
            hasValidContentType: hasValidContentType,
            message: 'Database triggers should use pg_net user agent'
          }
        }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Parse request body
    const { bookingId } = await req.json()
    
    if (!bookingId) {
      console.error('❌ Missing bookingId in request')
      return new Response(
        JSON.stringify({ error: 'Missing bookingId parameter' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('📧 Processing booking confirmation email for:', bookingId)

    // Get MSG91 configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const domain = Deno.env.get('MSG91_DOMAIN')

    if (!authKey || !domain) {
      console.error('❌ MSG91 configuration missing:', { authKey: !!authKey, domain: !!domain })
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Initialize Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ Supabase configuration missing:', {
        hasUrl: !!supabaseUrl,
        hasKey: !!supabaseServiceKey
      })
      return new Response(
        JSON.stringify({
          error: 'Supabase configuration not found',
          details: 'Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get booking data using our security definer function
    console.log('📊 Fetching booking data...')
    const { data: bookingData, error: fetchError } = await supabase
      .rpc('get_booking_email_data', { booking_id_param: bookingId })

    if (fetchError) {
      console.error('❌ Error fetching booking data:', fetchError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to fetch booking data',
          details: fetchError.message 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!bookingData || bookingData.length === 0) {
      console.log('⚠️ No booking data found for ID:', bookingId)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Booking not found or not eligible for email' 
        }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const booking = bookingData[0]
    console.log('📋 Booking data retrieved:', {
      bookingId: booking.booking_id,
      recipientName: booking.recipient_name,
      recipientEmail: booking.user_email,
      emailSent: booking.confirmation_email_sent
    })

    // Check if email already sent
    if (booking.confirmation_email_sent) {
      console.log('✅ Email already sent for booking:', bookingId)
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Email already sent',
          alreadySent: true
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Only send to registered users with email addresses
    if (!booking.user_id || !booking.user_email) {
      console.log('⚠️ Booking not eligible for email:', {
        hasUserId: !!booking.user_id,
        hasEmail: !!booking.user_email
      })
      
      // Update booking to mark as not eligible (prevents retry)
      await supabase
        .from('bookings')
        .update({
          confirmation_email_sent: false,
          confirmation_email_error: 'No email address available - guest booking or missing email'
        })
        .eq('id', bookingId)

      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'No email address available for this booking',
          reason: 'guest_booking_or_missing_email'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Prepare MSG91 template variables
    const templateVariables = {
      userName: booking.recipient_name,
      bookingReference: booking.booking_reference,
      courtName: booking.court_name,
      venueName: booking.venue_name,
      bookingDate: booking.formatted_date,
      startTime: booking.formatted_start_time,
      endTime: booking.formatted_end_time,
      duration: booking.formatted_duration,
      venueAddress: booking.venue_address || 'Address not available',
      venuePhone: booking.venue_phone || 'Contact venue directly',
      viewBookingLink: `https://grid2play.com/bookings`,
      venueDirectionsLink: `https://maps.google.com/?q=${encodeURIComponent(booking.venue_address || booking.venue_name)}`
    }

    // Prepare MSG91 API request payload
    const msg91Payload = {
      recipients: [
        {
          to: [
            {
              email: booking.user_email,
              name: booking.recipient_name
            }
          ],
          variables: templateVariables
        }
      ],
      from: {
        email: `no-reply@${domain}`,
        name: "Grid2Play"
      },
      domain: domain,
      template_id: 'booking_confirmation_grid2play'
    }

    console.log('📤 Sending email via MSG91:', {
      to: booking.user_email,
      templateId: 'booking_confirmation_grid2play',
      bookingReference: booking.booking_reference
    })

    // Send email via MSG91 API
    const response = await fetch('https://control.msg91.com/api/v5/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    const emailResult = await response.json()
    console.log('📧 MSG91 API response:', { 
      status: response.status, 
      ok: response.ok,
      result: emailResult 
    })

    // Update booking record with email status
    const updateData = response.ok 
      ? {
          confirmation_email_sent: true,
          confirmation_email_sent_at: new Date().toISOString(),
          confirmation_email_error: null
        }
      : {
          confirmation_email_sent: false,
          confirmation_email_error: emailResult.message || `MSG91 API error: ${response.status}`
        }

    const { error: updateError } = await supabase
      .from('bookings')
      .update(updateData)
      .eq('id', bookingId)

    if (updateError) {
      console.error('❌ Failed to update booking email status:', updateError)
      // Don't fail the whole operation if status update fails
    } else {
      console.log('✅ Booking email status updated successfully')
    }

    // Return response
    if (response.ok) {
      console.log('🎉 Booking confirmation email sent successfully!')
      return new Response(
        JSON.stringify({ 
          success: true,
          message: 'Booking confirmation email sent successfully',
          bookingReference: booking.booking_reference,
          recipientEmail: booking.user_email,
          data: emailResult
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } else {
      console.error('❌ MSG91 API error:', emailResult)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: `MSG91 API error: ${response.status} ${response.statusText}`,
          details: emailResult
        }),
        { 
          status: response.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('💥 Error in send-booking-confirmation-email function:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
