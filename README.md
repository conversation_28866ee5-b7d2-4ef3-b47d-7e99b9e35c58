# Grid2play - Sports Venue Booking Platform

**Grid2play** (Sporty Slot Spot) is a comprehensive sports venue booking and management platform that enables users to discover, book, and manage sports facilities while providing venue administrators with powerful management tools.

## 🏆 Project Overview

**URL**: https://lovable.dev/projects/23afaaf0-a670-4c15-9768-765483b4c88d

### Key Features

- **🏟️ Venue Management**: Comprehensive venue, court, and sports management system
- **📅 Smart Booking**: Real-time slot booking with conflict prevention and availability tracking
- **🏆 Tournament System**: Host and participate in tournaments with fixture management
- **👥 Team Challenges**: Create teams, join challenges, and track performance
- **📱 Mobile-First Design**: Responsive design optimized for mobile and desktop
- **🔐 Multi-Role Authentication**: User, admin, and super_admin role management
- **💬 Real-Time Chat**: Integrated chat system with AI assistance
- **📊 Analytics Dashboard**: Comprehensive booking and venue analytics
- **🌍 Location Services**: GPS-based venue discovery and mapping
- **💳 Payment Integration**: Razorpay payment processing
- **📧 Email Notifications**: MSG91 integration for custom email templates

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for build tooling and development
- **Tailwind CSS** for styling
- **shadcn/ui** for component library
- **TanStack Query** for state management
- **React Router** for navigation
- **Framer Motion** for animations

### Backend & Database
- **Supabase** (PostgreSQL database)
- **Supabase Auth** for authentication
- **Supabase Realtime** for live updates
- **Supabase Edge Functions** for serverless functions
- **Row Level Security (RLS)** for data protection

### Integrations
- **MSG91** for email services
- **Razorpay** for payment processing
- **Geolocation APIs** for location services
- **AI Chat Assistant** for user support

## 🏗️ Architecture Overview

### Database Schema
The application uses a comprehensive PostgreSQL schema with the following key entities:

#### Core Entities
- **Users & Profiles**: User management with role-based access
- **Venues**: Sports facilities with location and capacity data
- **Courts**: Individual playing areas within venues
- **Sports**: Supported sports types and configurations
- **Bookings**: Time slot reservations with conflict prevention
- **Blocked Slots**: Administrative time blocking

#### Tournament System
- **Tournaments**: Tournament metadata and configuration
- **Tournament Registrations**: Team/player registrations
- **Tournament Fixtures**: Match scheduling and results
- **Teams**: Team management and member tracking

#### Communication & Support
- **Team Chats**: Real-time team communication
- **Help Requests**: User support ticket system
- **Reviews**: Venue and service reviews
- **Notifications**: System-wide notification management

### Component Architecture

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Base UI components (shadcn/ui)
│   ├── admin/           # Admin-specific components
│   ├── challenge/       # Challenge/tournament components
│   └── tournament/      # Tournament management components
├── pages/               # Route components
│   ├── admin/           # Admin dashboard pages
│   ├── challenge/       # Challenge pages
│   └── tournament/      # Tournament pages
├── context/             # React context providers
├── hooks/               # Custom React hooks
├── services/            # External service integrations
├── utils/               # Utility functions and helpers
├── types/               # TypeScript type definitions
└── integrations/        # Third-party integrations
    └── supabase/        # Supabase client and types
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm (install with [nvm](https://github.com/nvm-sh/nvm#installing-and-updating))
- Supabase account and project
- MSG91 account (for email services)
- Razorpay account (for payments)

### Local Development

```sh
# Clone the repository
git clone <YOUR_GIT_URL>
cd <YOUR_PROJECT_NAME>

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your Supabase and other service credentials

# Start development server
npm run dev
```

### Environment Variables
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_RAZORPAY_KEY_ID=your_razorpay_key
```

## 📝 Development Workflows

### Using Lovable
Visit the [Lovable Project](https://lovable.dev/projects/23afaaf0-a670-4c15-9768-765483b4c88d) for AI-assisted development. Changes made via Lovable are automatically committed to this repository.

### Using Your IDE
Make changes locally and push to the repository. Changes will be reflected in Lovable automatically.

### Using GitHub Codespaces
1. Navigate to the repository main page
2. Click "Code" → "Codespaces" → "New codespace"
3. Edit files directly in the browser-based IDE

## 🔒 Security Features

- **Row Level Security (RLS)** on all database tables
- **Role-based access control** (user/admin/super_admin)
- **Protected API routes** with authentication middleware
- **Input validation** and sanitization
- **Secure payment processing** with Razorpay
- **Email verification** with custom token system

## 📊 Key Business Logic

### Booking System
- **Conflict Prevention**: Database triggers prevent double bookings
- **Real-time Availability**: Live slot availability updates
- **Admin Booking**: Admins can book on behalf of customers
- **Cancellation Management**: Automated refund processing

### Tournament Management
- **Registration System**: Team/player registration with validation
- **Fixture Generation**: Automated tournament bracket creation
- **Result Tracking**: Live score updates and winner determination
- **Prize Distribution**: Automated prize calculation and distribution

### User Management
- **Multi-role Authentication**: Granular permission system
- **Profile Management**: Comprehensive user profile system
- **Activity Tracking**: User engagement and booking history
- **Notification System**: Real-time updates and alerts

## 🚀 Deployment

### Production Deployment
Deploy via [Lovable](https://lovable.dev/projects/23afaaf0-a670-4c15-9768-765483b4c88d):
1. Open the Lovable project
2. Click "Share" → "Publish"
3. Configure custom domain if needed

### Custom Domain Setup
1. Navigate to Project → Settings → Domains
2. Click "Connect Domain"
3. Follow the [custom domain guide](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

## 📚 Documentation

### 📋 Quick Start
- [📋 Project Index](./docs/PROJECT_INDEX.md) - **Start here** for complete project overview
- [🚀 Deployment Guide](./docs/DEPLOYMENT_GUIDE.md) - Setup and deployment instructions

### 🏗️ Architecture & Development
- [🏗️ Component Architecture](./docs/COMPONENT_ARCHITECTURE.md) - Component design patterns and structure
- [🗄️ Database Schema](./docs/DATABASE_SCHEMA.md) - Complete database structure and relationships
- [🔌 API Reference](./docs/API_REFERENCE.md) - API endpoints and integration guide

### 🔧 Feature Documentation
- [📧 MSG91 Integration](./docs/MSG91_INTEGRATION.md) - Email service integration guide
- [📬 Booking Email Integration](./docs/BOOKING_EMAIL_INTEGRATION.md) - Booking confirmation system

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.
