# 🚨 URGENT: Phone Login Fix for Profile Schema Issue

## ❌ **Problem Identified**
The Edge Function was looking for `user_id` column in the `profiles` table, but your schema uses `id` as the primary key that references `auth.users(id)`.

## ✅ **Fix Applied**
Updated the Edge Function to use the correct column structure:
- **Before**: `SELECT id, user_id, phone...` ❌
- **After**: `SELECT id, phone, phone_verified...` ✅
- **Auth Lookup**: Uses `profile.id` directly (not `profile.user_id`)

## 🚀 **Immediate Deployment Steps**

### **Step 1: Update Edge Function**
1. Go to **Supabase Dashboard** → **Functions**
2. Find `phone-password-login` function
3. **Replace the entire code** with the updated version from:
   ```
   supabase/functions/phone-password-login/index.ts
   ```
4. Click **"Deploy function"**

### **Step 2: Test with Your Data**
Your verified WhatsApp number: `919871067340`
Profile ID: `eadb4a72-70bc-44ff-859a-c2e395bc70fc`

**Test Steps**:
1. Go to Login page → WhatsApp tab → Password method
2. Country Code: `+91`
3. Phone: `9871067340`
4. Password: (your WhatsApp registration password)
5. Click "Sign In with Password"

## 🔍 **Database Verification**

Before testing, verify your data:

```sql
-- Check your profile exists with correct phone format
SELECT id, phone, phone_verified, full_name 
FROM profiles 
WHERE id = 'eadb4a72-70bc-44ff-859a-c2e395bc70fc';

-- Check phone format variations
SELECT id, phone, phone_verified 
FROM profiles 
WHERE phone IN ('+919871067340', '919871067340', '+91 9871067340');

-- Check auth user exists
SELECT id, email, phone 
FROM auth.users 
WHERE id = 'eadb4a72-70bc-44ff-859a-c2e395bc70fc';
```

## 🔧 **Key Changes Made**

### **1. Fixed Column Reference**
```typescript
// OLD (BROKEN)
.select('id, user_id, phone, phone_verified, full_name')

// NEW (FIXED)
.select('id, phone, phone_verified, full_name')
```

### **2. Fixed Auth User Lookup**
```typescript
// OLD (BROKEN)
const { data: authUser } = await supabaseAdmin.auth.admin.getUserById(profile.user_id)

// NEW (FIXED)
const { data: authUser } = await supabaseAdmin.auth.admin.getUserById(profile.id)
```

### **3. Added Phone Formatting**
```typescript
// Ensures phone format consistency
const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`
```

## 🎯 **Expected Results**

### **Before Fix**:
```json
{
  "success": false,
  "error": "Phone number not found or not verified. Please register first."
}
```

### **After Fix**:
```json
{
  "success": true,
  "user": { "id": "eadb4a72-70bc-44ff-859a-c2e395bc70fc", ... },
  "session": { "access_token": "...", ... }
}
```

## 🚨 **If Still Not Working**

### **Check Phone Format in Database**
Run this query to see exactly how your phone is stored:
```sql
SELECT phone, length(phone), phone_verified 
FROM profiles 
WHERE id = 'eadb4a72-70bc-44ff-859a-c2e395bc70fc';
```

### **Common Phone Format Issues**
- Database: `+919871067340` vs Input: `919871067340`
- Database: `919871067340` vs Input: `+919871067340`
- Database: `+91 9871067340` vs Input: `+919871067340`

### **Debug Edge Function**
Check the function logs in Supabase Dashboard:
1. Go to Functions → phone-password-login → Logs
2. Look for the console.log outputs
3. Check what phone format is being searched

## 🔄 **Quick Test Commands**

### **Test Phone Lookup Directly**
```sql
-- Test exact phone format
SELECT * FROM profiles WHERE phone = '+919871067340' AND phone_verified = true;
SELECT * FROM profiles WHERE phone = '919871067340' AND phone_verified = true;
SELECT * FROM profiles WHERE phone = '+91 9871067340' AND phone_verified = true;
```

### **Test Auth User Lookup**
```sql
-- Verify auth user exists
SELECT id, email, phone FROM auth.users WHERE id = 'eadb4a72-70bc-44ff-859a-c2e395bc70fc';
```

## 🎉 **Success Indicators**

1. **No Column Error**: No more "column profiles.user_id does not exist"
2. **Profile Found**: Edge function finds your verified profile
3. **Auth Success**: Successful authentication with email + password
4. **Session Created**: User logged in and redirected to dashboard

## 📞 **Your Test Data**
- **Phone**: `919871067340` (try with and without +91)
- **Profile ID**: `eadb4a72-70bc-44ff-859a-c2e395bc70fc`
- **Expected Format**: `+919871067340` (most likely)

Deploy the updated Edge Function and test immediately! The schema fix should resolve the login issue. 🚀
