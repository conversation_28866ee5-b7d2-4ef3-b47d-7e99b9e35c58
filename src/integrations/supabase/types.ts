export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_bookings: {
        Row: {
          admin_id: string
          amount_collected: number | null
          booking_id: string
          created_at: string
          customer_name: string
          customer_phone: string | null
          id: string
          notes: string | null
          payment_method: string
          payment_status: string | null
        }
        Insert: {
          admin_id: string
          amount_collected?: number | null
          booking_id: string
          created_at?: string
          customer_name: string
          customer_phone?: string | null
          id?: string
          notes?: string | null
          payment_method: string
          payment_status?: string | null
        }
        Update: {
          admin_id?: string
          amount_collected?: number | null
          booking_id?: string
          created_at?: string
          customer_name?: string
          customer_phone?: string | null
          id?: string
          notes?: string | null
          payment_method?: string
          payment_status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_bookings_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
        ]
      }
      blocked_slots: {
        Row: {
          court_id: string
          created_at: string
          created_by: string
          date: string
          end_time: string
          id: string
          reason: string | null
          start_time: string
        }
        Insert: {
          court_id: string
          created_at?: string
          created_by: string
          date: string
          end_time: string
          id?: string
          reason?: string | null
          start_time: string
        }
        Update: {
          court_id?: string
          created_at?: string
          created_by?: string
          date?: string
          end_time?: string
          id?: string
          reason?: string | null
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "blocked_slots_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
        ]
      }
      bookings: {
        Row: {
          booked_by_admin_id: string | null
          booking_date: string
          booking_reference: string | null
          cancellation_reason: string | null
          confirmation_email_error: string | null
          confirmation_email_sent: boolean | null
          confirmation_email_sent_at: string | null
          court_id: string
          created_at: string
          end_time: string
          guest_name: string | null
          guest_phone: string | null
          id: string
          payment_method: string | null
          payment_reference: string | null
          payment_status: string | null
          start_time: string
          status: Database["public"]["Enums"]["booking_status"]
          total_price: number
          updated_at: string
          user_id: string | null
        }
        Insert: {
          booked_by_admin_id?: string | null
          booking_date: string
          booking_reference?: string | null
          cancellation_reason?: string | null
          confirmation_email_error?: string | null
          confirmation_email_sent?: boolean | null
          confirmation_email_sent_at?: string | null
          court_id: string
          created_at?: string
          end_time: string
          guest_name?: string | null
          guest_phone?: string | null
          id?: string
          payment_method?: string | null
          payment_reference?: string | null
          payment_status?: string | null
          start_time: string
          status?: Database["public"]["Enums"]["booking_status"]
          total_price: number
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          booked_by_admin_id?: string | null
          booking_date?: string
          booking_reference?: string | null
          cancellation_reason?: string | null
          confirmation_email_error?: string | null
          confirmation_email_sent?: boolean | null
          confirmation_email_sent_at?: string | null
          court_id?: string
          created_at?: string
          end_time?: string
          guest_name?: string | null
          guest_phone?: string | null
          id?: string
          payment_method?: string | null
          payment_reference?: string | null
          payment_status?: string | null
          start_time?: string
          status?: Database["public"]["Enums"]["booking_status"]
          total_price?: number
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "bookings_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
        ]
      }
      cancellations: {
        Row: {
          booking_id: string
          cancellation_reason: string
          cancelled_by_role: string
          cancelled_by_user_id: string
          created_at: string
          id: string
          original_booking_data: Json
          razorpay_refund_id: string | null
          refund_amount: number | null
          refund_notes: string | null
          refund_processed_at: string | null
          refund_processed_by: string | null
          refund_status: string
          updated_at: string
        }
        Insert: {
          booking_id: string
          cancellation_reason: string
          cancelled_by_role: string
          cancelled_by_user_id: string
          created_at?: string
          id?: string
          original_booking_data: Json
          razorpay_refund_id?: string | null
          refund_amount?: number | null
          refund_notes?: string | null
          refund_processed_at?: string | null
          refund_processed_by?: string | null
          refund_status?: string
          updated_at?: string
        }
        Update: {
          booking_id?: string
          cancellation_reason?: string
          cancelled_by_role?: string
          cancelled_by_user_id?: string
          created_at?: string
          id?: string
          original_booking_data?: Json
          razorpay_refund_id?: string | null
          refund_amount?: number | null
          refund_notes?: string | null
          refund_processed_at?: string | null
          refund_processed_by?: string | null
          refund_status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "cancellations_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
        ]
      }
      court_groups: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_active: boolean
          name: string
          updated_at: string
          venue_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          name: string
          updated_at?: string
          venue_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          name?: string
          updated_at?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "court_groups_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      courts: {
        Row: {
          court_group_id: string | null
          created_at: string
          hourly_rate: number | null
          id: string
          is_active: boolean
          name: string
          sport_id: string
          updated_at: string
          "Venue name/ Sports name": string | null
          venue_id: string
        }
        Insert: {
          court_group_id?: string | null
          created_at?: string
          hourly_rate?: number | null
          id?: string
          is_active?: boolean
          name: string
          sport_id: string
          updated_at?: string
          "Venue name/ Sports name"?: string | null
          venue_id: string
        }
        Update: {
          court_group_id?: string | null
          created_at?: string
          hourly_rate?: number | null
          id?: string
          is_active?: boolean
          name?: string
          sport_id?: string
          updated_at?: string
          "Venue name/ Sports name"?: string | null
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "courts_court_group_id_fkey"
            columns: ["court_group_id"]
            isOneToOne: false
            referencedRelation: "court_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "courts_sport_id_fkey"
            columns: ["sport_id"]
            isOneToOne: false
            referencedRelation: "sports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "courts_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      daily_earnings: {
        Row: {
          admin_bookings: number | null
          cancelled_bookings: number | null
          confirmed_bookings: number | null
          created_at: string | null
          cycle_date: string
          cycle_day_name: string
          cycle_number: number
          gross_revenue: number | null
          id: string
          is_current_day: boolean | null
          is_frozen: boolean | null
          last_updated_at: string | null
          net_revenue: number | null
          platform_fee_amount: number | null
          platform_fee_percentage: number | null
          refund_count: number | null
          total_bookings: number | null
          total_refunds: number | null
          venue_id: string
        }
        Insert: {
          admin_bookings?: number | null
          cancelled_bookings?: number | null
          confirmed_bookings?: number | null
          created_at?: string | null
          cycle_date: string
          cycle_day_name: string
          cycle_number: number
          gross_revenue?: number | null
          id?: string
          is_current_day?: boolean | null
          is_frozen?: boolean | null
          last_updated_at?: string | null
          net_revenue?: number | null
          platform_fee_amount?: number | null
          platform_fee_percentage?: number | null
          refund_count?: number | null
          total_bookings?: number | null
          total_refunds?: number | null
          venue_id: string
        }
        Update: {
          admin_bookings?: number | null
          cancelled_bookings?: number | null
          confirmed_bookings?: number | null
          created_at?: string | null
          cycle_date?: string
          cycle_day_name?: string
          cycle_number?: number
          gross_revenue?: number | null
          id?: string
          is_current_day?: boolean | null
          is_frozen?: boolean | null
          last_updated_at?: string | null
          net_revenue?: number | null
          platform_fee_amount?: number | null
          platform_fee_percentage?: number | null
          refund_count?: number | null
          total_bookings?: number | null
          total_refunds?: number | null
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "daily_earnings_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      faqs: {
        Row: {
          answer: string
          created_at: string | null
          id: number
          is_active: boolean | null
          question: string
          tags: string[] | null
        }
        Insert: {
          answer: string
          created_at?: string | null
          id?: number
          is_active?: boolean | null
          question: string
          tags?: string[] | null
        }
        Update: {
          answer?: string
          created_at?: string | null
          id?: number
          is_active?: boolean | null
          question?: string
          tags?: string[] | null
        }
        Relationships: []
      }
      help_requests: {
        Row: {
          category: string | null
          created_at: string
          id: string
          last_message_at: string
          status: string
          subject: string
          ticket_number: string
          updated_at: string
          user_id: string
          venue_id: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string
          id?: string
          last_message_at?: string
          status?: string
          subject: string
          ticket_number: string
          updated_at?: string
          user_id: string
          venue_id?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string
          id?: string
          last_message_at?: string
          status?: string
          subject?: string
          ticket_number?: string
          updated_at?: string
          user_id?: string
          venue_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "help_requests_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      match_chats: {
        Row: {
          challenge_id: string
          content: string
          created_at: string
          id: string
          team_id: string
          user_id: string
        }
        Insert: {
          challenge_id: string
          content: string
          created_at?: string
          id?: string
          team_id: string
          user_id: string
        }
        Update: {
          challenge_id?: string
          content?: string
          created_at?: string
          id?: string
          team_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "match_chats_challenge_id_fkey"
            columns: ["challenge_id"]
            isOneToOne: false
            referencedRelation: "team_challenges"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "match_chats_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      match_results: {
        Row: {
          challenge_id: string
          completed_at: string
          created_at: string
          id: string
          team_a_score: number
          team_b_score: number
          updated_at: string
          winner_team_id: string | null
        }
        Insert: {
          challenge_id: string
          completed_at?: string
          created_at?: string
          id?: string
          team_a_score?: number
          team_b_score?: number
          updated_at?: string
          winner_team_id?: string | null
        }
        Update: {
          challenge_id?: string
          completed_at?: string
          created_at?: string
          id?: string
          team_a_score?: number
          team_b_score?: number
          updated_at?: string
          winner_team_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "match_results_challenge_id_fkey"
            columns: ["challenge_id"]
            isOneToOne: true
            referencedRelation: "team_challenges"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "match_results_winner_team_id_fkey"
            columns: ["winner_team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          content: string
          created_at: string
          id: string
          is_read: boolean | null
          sender_id: string
          updated_at: string
          user_id: string
          venue_id: string | null
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          is_read?: boolean | null
          sender_id?: string
          updated_at?: string
          user_id: string
          venue_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          is_read?: boolean | null
          sender_id?: string
          updated_at?: string
          user_id?: string
          venue_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          approved: boolean | null
          created_at: string
          id: string
          message: string
          metadata: Json | null
          read_status: boolean
          rejection_reason: string | null
          reviewed_at: string | null
          reviewed_by: string | null
          title: string
          type: string | null
          user_id: string | null
        }
        Insert: {
          approved?: boolean | null
          created_at?: string
          id?: string
          message: string
          metadata?: Json | null
          read_status?: boolean
          rejection_reason?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          title: string
          type?: string | null
          user_id?: string | null
        }
        Update: {
          approved?: boolean | null
          created_at?: string
          id?: string
          message?: string
          metadata?: Json | null
          read_status?: boolean
          rejection_reason?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          title?: string
          type?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      payment_logs: {
        Row: {
          amount: number | null
          booking_id: string | null
          created_at: string | null
          event_type: string
          id: string
          payload: Json | null
          payment_id: string
          razorpay_order_id: string | null
          razorpay_payment_id: string | null
          status: string
        }
        Insert: {
          amount?: number | null
          booking_id?: string | null
          created_at?: string | null
          event_type: string
          id?: string
          payload?: Json | null
          payment_id: string
          razorpay_order_id?: string | null
          razorpay_payment_id?: string | null
          status: string
        }
        Update: {
          amount?: number | null
          booking_id?: string | null
          created_at?: string | null
          event_type?: string
          id?: string
          payload?: Json | null
          payment_id?: string
          razorpay_order_id?: string | null
          razorpay_payment_id?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "payment_logs_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_orders: {
        Row: {
          amount: number
          created_at: string
          id: string
          metadata: Json | null
          payment_method: string | null
          razorpay_order_id: string
          razorpay_payment_id: string | null
          status: string
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          id?: string
          metadata?: Json | null
          payment_method?: string | null
          razorpay_order_id: string
          razorpay_payment_id?: string | null
          status?: string
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          id?: string
          metadata?: Json | null
          payment_method?: string | null
          razorpay_order_id?: string
          razorpay_payment_id?: string | null
          status?: string
          updated_at?: string
        }
        Relationships: []
      }
      pending_users: {
        Row: {
          created_at: string | null
          email: string
          expires_at: string
          full_name: string | null
          id: string
          password_hash: string
          phone: string | null
          updated_at: string | null
          verification_token: string
        }
        Insert: {
          created_at?: string | null
          email: string
          expires_at: string
          full_name?: string | null
          id?: string
          password_hash: string
          phone?: string | null
          updated_at?: string | null
          verification_token: string
        }
        Update: {
          created_at?: string | null
          email?: string
          expires_at?: string
          full_name?: string | null
          id?: string
          password_hash?: string
          phone?: string | null
          updated_at?: string | null
          verification_token?: string
        }
        Relationships: []
      }
      phone_otps: {
        Row: {
          attempts: number
          created_at: string
          expires_at: string
          id: string
          otp_hash: string
          phone: string
          user_id: string | null
          verified: boolean
        }
        Insert: {
          attempts?: number
          created_at?: string
          expires_at: string
          id?: string
          otp_hash: string
          phone: string
          user_id?: string | null
          verified?: boolean
        }
        Update: {
          attempts?: number
          created_at?: string
          expires_at?: string
          id?: string
          otp_hash?: string
          phone?: string
          user_id?: string | null
          verified?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "phone_otps_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      player_stats: {
        Row: {
          assists: number
          challenge_id: string
          created_at: string
          goals: number
          id: string
          is_mvp: boolean | null
          rating: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          assists?: number
          challenge_id: string
          created_at?: string
          goals?: number
          id?: string
          is_mvp?: boolean | null
          rating?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          assists?: number
          challenge_id?: string
          created_at?: string
          goals?: number
          id?: string
          is_mvp?: boolean | null
          rating?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "player_stats_challenge_id_fkey"
            columns: ["challenge_id"]
            isOneToOne: false
            referencedRelation: "team_challenges"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          draws: number
          email: string | null
          email_verified: boolean
          full_name: string | null
          id: string
          level: number
          losses: number
          phone: string | null
          phone_verified: boolean
          profile_name: string | null
          share_link: string | null
          updated_at: string
          wins: number
          xp: number
        }
        Insert: {
          created_at?: string
          draws?: number
          email?: string | null
          email_verified?: boolean
          full_name?: string | null
          id: string
          level?: number
          losses?: number
          phone?: string | null
          phone_verified?: boolean
          profile_name?: string | null
          share_link?: string | null
          updated_at?: string
          wins?: number
          xp?: number
        }
        Update: {
          created_at?: string
          draws?: number
          email?: string | null
          email_verified?: boolean
          full_name?: string | null
          id?: string
          level?: number
          losses?: number
          phone?: string | null
          phone_verified?: boolean
          profile_name?: string | null
          share_link?: string | null
          updated_at?: string
          wins?: number
          xp?: number
        }
        Relationships: []
      }
      reviews: {
        Row: {
          booking_id: string | null
          comment: string | null
          created_at: string | null
          id: string
          is_approved: boolean | null
          rating: number
          user_id: string
          venue_id: string
        }
        Insert: {
          booking_id?: string | null
          comment?: string | null
          created_at?: string | null
          id?: string
          is_approved?: boolean | null
          rating: number
          user_id: string
          venue_id: string
        }
        Update: {
          booking_id?: string | null
          comment?: string | null
          created_at?: string | null
          id?: string
          is_approved?: boolean | null
          rating?: number
          user_id?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reviews_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      settlement_details: {
        Row: {
          created_at: string | null
          daily_earnings_id: string
          id: string
          settlement_id: string
          snapshot_bookings_count: number
          snapshot_gross_revenue: number
          snapshot_net_revenue: number
          snapshot_platform_fees: number
        }
        Insert: {
          created_at?: string | null
          daily_earnings_id: string
          id?: string
          settlement_id: string
          snapshot_bookings_count: number
          snapshot_gross_revenue: number
          snapshot_net_revenue: number
          snapshot_platform_fees: number
        }
        Update: {
          created_at?: string | null
          daily_earnings_id?: string
          id?: string
          settlement_id?: string
          snapshot_bookings_count?: number
          snapshot_gross_revenue?: number
          snapshot_net_revenue?: number
          snapshot_platform_fees?: number
        }
        Relationships: [
          {
            foreignKeyName: "settlement_details_daily_earnings_id_fkey"
            columns: ["daily_earnings_id"]
            isOneToOne: true
            referencedRelation: "daily_earnings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "settlement_details_settlement_id_fkey"
            columns: ["settlement_id"]
            isOneToOne: false
            referencedRelation: "settlements"
            referencedColumns: ["id"]
          },
        ]
      }
      settlements: {
        Row: {
          created_at: string | null
          expected_settlement_date: string | null
          id: string
          notes: string | null
          processed_at: string | null
          processed_by: string | null
          settled_at: string | null
          settled_by: string | null
          settlement_reference: string
          settlement_week_end: string
          settlement_week_start: string
          status: string
          total_admin_bookings: number | null
          total_bookings: number | null
          total_cancelled_bookings: number | null
          total_confirmed_bookings: number | null
          total_gross_revenue: number | null
          total_net_revenue: number | null
          total_platform_fees: number | null
          total_refunds: number | null
          updated_at: string | null
          venue_id: string
        }
        Insert: {
          created_at?: string | null
          expected_settlement_date?: string | null
          id?: string
          notes?: string | null
          processed_at?: string | null
          processed_by?: string | null
          settled_at?: string | null
          settled_by?: string | null
          settlement_reference: string
          settlement_week_end: string
          settlement_week_start: string
          status?: string
          total_admin_bookings?: number | null
          total_bookings?: number | null
          total_cancelled_bookings?: number | null
          total_confirmed_bookings?: number | null
          total_gross_revenue?: number | null
          total_net_revenue?: number | null
          total_platform_fees?: number | null
          total_refunds?: number | null
          updated_at?: string | null
          venue_id: string
        }
        Update: {
          created_at?: string | null
          expected_settlement_date?: string | null
          id?: string
          notes?: string | null
          processed_at?: string | null
          processed_by?: string | null
          settled_at?: string | null
          settled_by?: string | null
          settlement_reference?: string
          settlement_week_end?: string
          settlement_week_start?: string
          status?: string
          total_admin_bookings?: number | null
          total_bookings?: number | null
          total_cancelled_bookings?: number | null
          total_confirmed_bookings?: number | null
          total_gross_revenue?: number | null
          total_net_revenue?: number | null
          total_platform_fees?: number | null
          total_refunds?: number | null
          updated_at?: string | null
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "settlements_processed_by_fkey"
            columns: ["processed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "settlements_settled_by_fkey"
            columns: ["settled_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "settlements_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      sports: {
        Row: {
          created_at: string
          description: string | null
          icon: string | null
          id: string
          image_url: string | null
          is_active: boolean
          name: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean
          name: string
        }
        Update: {
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          image_url?: string | null
          is_active?: boolean
          name?: string
        }
        Relationships: []
      }
      team_challenges: {
        Row: {
          booking_date: string
          challenger_team_id: string
          court_id: string
          created_at: string
          end_time: string
          format: string
          id: string
          opponent_team_id: string
          sport_id: string
          start_time: string
          status: string
          updated_at: string
          venue_id: string
        }
        Insert: {
          booking_date: string
          challenger_team_id: string
          court_id: string
          created_at?: string
          end_time: string
          format: string
          id?: string
          opponent_team_id: string
          sport_id: string
          start_time: string
          status: string
          updated_at?: string
          venue_id: string
        }
        Update: {
          booking_date?: string
          challenger_team_id?: string
          court_id?: string
          created_at?: string
          end_time?: string
          format?: string
          id?: string
          opponent_team_id?: string
          sport_id?: string
          start_time?: string
          status?: string
          updated_at?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_challenges_challenger_team_id_fkey"
            columns: ["challenger_team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_challenges_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_challenges_opponent_team_id_fkey"
            columns: ["opponent_team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_challenges_sport_id_fkey"
            columns: ["sport_id"]
            isOneToOne: false
            referencedRelation: "sports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_challenges_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      team_chats: {
        Row: {
          content: string
          created_at: string
          id: string
          team_id: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          team_id: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          team_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_chats_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      team_join_requests: {
        Row: {
          created_at: string
          id: string
          message: string | null
          status: string
          team_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          message?: string | null
          status?: string
          team_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          message?: string | null
          status?: string
          team_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_join_requests_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      team_members: {
        Row: {
          id: string
          joined_at: string
          role: string
          team_id: string
          user_id: string
        }
        Insert: {
          id?: string
          joined_at?: string
          role: string
          team_id: string
          user_id: string
        }
        Update: {
          id?: string
          joined_at?: string
          role?: string
          team_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      teams: {
        Row: {
          created_at: string
          creator_id: string
          description: string | null
          draws: number
          id: string
          logo_url: string | null
          losses: number
          name: string
          slug: string
          updated_at: string
          wins: number
          xp: number
        }
        Insert: {
          created_at?: string
          creator_id: string
          description?: string | null
          draws?: number
          id?: string
          logo_url?: string | null
          losses?: number
          name: string
          slug: string
          updated_at?: string
          wins?: number
          xp?: number
        }
        Update: {
          created_at?: string
          creator_id?: string
          description?: string | null
          draws?: number
          id?: string
          logo_url?: string | null
          losses?: number
          name?: string
          slug?: string
          updated_at?: string
          wins?: number
          xp?: number
        }
        Relationships: []
      }
      template_slots: {
        Row: {
          court_id: string
          created_at: string
          day_of_week: number
          end_time: string
          id: string
          is_available: boolean
          price: string
          start_time: string
          updated_at: string
        }
        Insert: {
          court_id: string
          created_at?: string
          day_of_week: number
          end_time: string
          id?: string
          is_available?: boolean
          price: string
          start_time: string
          updated_at?: string
        }
        Update: {
          court_id?: string
          created_at?: string
          day_of_week?: number
          end_time?: string
          id?: string
          is_available?: boolean
          price?: string
          start_time?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "template_slots_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
        ]
      }
      tournament_host_requests: {
        Row: {
          admin_notes: string | null
          contact_info: string
          created_at: string
          description: string | null
          end_date: string
          entry_fee: number | null
          id: string
          max_participants: number
          organizer_name: string
          sport_id: string
          start_date: string
          status: string
          tournament_name: string
          updated_at: string
          user_id: string
          venue_id: string
        }
        Insert: {
          admin_notes?: string | null
          contact_info: string
          created_at?: string
          description?: string | null
          end_date: string
          entry_fee?: number | null
          id?: string
          max_participants: number
          organizer_name: string
          sport_id: string
          start_date: string
          status?: string
          tournament_name: string
          updated_at?: string
          user_id: string
          venue_id: string
        }
        Update: {
          admin_notes?: string | null
          contact_info?: string
          created_at?: string
          description?: string | null
          end_date?: string
          entry_fee?: number | null
          id?: string
          max_participants?: number
          organizer_name?: string
          sport_id?: string
          start_date?: string
          status?: string
          tournament_name?: string
          updated_at?: string
          user_id?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tournament_host_requests_sport_id_fkey"
            columns: ["sport_id"]
            isOneToOne: false
            referencedRelation: "sports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tournament_host_requests_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      tournament_matches: {
        Row: {
          court_id: string
          created_at: string
          end_time: string | null
          id: string
          match_date: string | null
          match_number: number
          round: number
          start_time: string | null
          status: string
          team_a_id: string | null
          team_b_id: string | null
          tournament_id: string
          updated_at: string
          venue_id: string
        }
        Insert: {
          court_id: string
          created_at?: string
          end_time?: string | null
          id?: string
          match_date?: string | null
          match_number: number
          round: number
          start_time?: string | null
          status?: string
          team_a_id?: string | null
          team_b_id?: string | null
          tournament_id: string
          updated_at?: string
          venue_id: string
        }
        Update: {
          court_id?: string
          created_at?: string
          end_time?: string | null
          id?: string
          match_date?: string | null
          match_number?: number
          round?: number
          start_time?: string | null
          status?: string
          team_a_id?: string | null
          team_b_id?: string | null
          tournament_id?: string
          updated_at?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tournament_matches_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tournament_matches_team_a_id_fkey"
            columns: ["team_a_id"]
            isOneToOne: false
            referencedRelation: "tournament_registrations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tournament_matches_team_b_id_fkey"
            columns: ["team_b_id"]
            isOneToOne: false
            referencedRelation: "tournament_registrations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tournament_matches_tournament_id_fkey"
            columns: ["tournament_id"]
            isOneToOne: false
            referencedRelation: "tournaments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tournament_matches_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      tournament_registrations: {
        Row: {
          created_at: string
          id: string
          notes: string | null
          payment_reference: string | null
          payment_status: string | null
          player_count: number
          registration_date: string
          team_name: string
          tournament_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          notes?: string | null
          payment_reference?: string | null
          payment_status?: string | null
          player_count: number
          registration_date?: string
          team_name: string
          tournament_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          notes?: string | null
          payment_reference?: string | null
          payment_status?: string | null
          player_count?: number
          registration_date?: string
          team_name?: string
          tournament_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tournament_registrations_tournament_id_fkey"
            columns: ["tournament_id"]
            isOneToOne: false
            referencedRelation: "tournaments"
            referencedColumns: ["id"]
          },
        ]
      }
      tournament_results: {
        Row: {
          completed_at: string | null
          created_at: string
          id: string
          status: string
          team_a_score: number
          team_b_score: number
          tournament_match_id: string
          updated_at: string
          winner_id: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string
          id?: string
          status?: string
          team_a_score?: number
          team_b_score?: number
          tournament_match_id: string
          updated_at?: string
          winner_id?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string
          id?: string
          status?: string
          team_a_score?: number
          team_b_score?: number
          tournament_match_id?: string
          updated_at?: string
          winner_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tournament_results_tournament_match_id_fkey"
            columns: ["tournament_match_id"]
            isOneToOne: true
            referencedRelation: "tournament_matches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tournament_results_winner_id_fkey"
            columns: ["winner_id"]
            isOneToOne: false
            referencedRelation: "tournament_registrations"
            referencedColumns: ["id"]
          },
        ]
      }
      tournaments: {
        Row: {
          created_at: string
          description: string | null
          end_date: string
          entry_fee: number | null
          id: string
          max_participants: number
          name: string
          organizer_id: string
          registration_deadline: string
          rules: string | null
          slug: string
          sport_id: string
          start_date: string
          status: string
          updated_at: string
          venue_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          end_date: string
          entry_fee?: number | null
          id?: string
          max_participants: number
          name: string
          organizer_id: string
          registration_deadline: string
          rules?: string | null
          slug: string
          sport_id: string
          start_date: string
          status?: string
          updated_at?: string
          venue_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          end_date?: string
          entry_fee?: number | null
          id?: string
          max_participants?: number
          name?: string
          organizer_id?: string
          registration_deadline?: string
          rules?: string | null
          slug?: string
          sport_id?: string
          start_date?: string
          status?: string
          updated_at?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tournaments_sport_id_fkey"
            columns: ["sport_id"]
            isOneToOne: false
            referencedRelation: "sports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tournaments_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
      venue_admins: {
        Row: {
          created_at: string
          id: string
          user_id: string
          venue_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          user_id: string
          venue_id: string
        }
        Update: {
          created_at?: string
          id?: string
          user_id?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "venue_admins_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      venue_faqs: {
        Row: {
          answer: string
          category: string | null
          created_at: string
          id: string
          is_active: boolean | null
          order_index: number | null
          question: string
          updated_at: string
          venue_id: string
        }
        Insert: {
          answer: string
          category?: string | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          order_index?: number | null
          question: string
          updated_at?: string
          venue_id: string
        }
        Update: {
          answer?: string
          category?: string | null
          created_at?: string
          id?: string
          is_active?: boolean | null
          order_index?: number | null
          question?: string
          updated_at?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "venue_faqs_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      venue_sport_display_names: {
        Row: {
          created_at: string
          display_name: string
          id: string
          sport_id: string
          updated_at: string
          venue_id: string
        }
        Insert: {
          created_at?: string
          display_name: string
          id?: string
          sport_id: string
          updated_at?: string
          venue_id: string
        }
        Update: {
          created_at?: string
          display_name?: string
          id?: string
          sport_id?: string
          updated_at?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "venue_sport_display_names_sport_id_fkey"
            columns: ["sport_id"]
            isOneToOne: false
            referencedRelation: "sports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "venue_sport_display_names_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      venue_subscriptions: {
        Row: {
          created_at: string
          id: string
          user_id: string
          venue_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          user_id: string
          venue_id: string
        }
        Update: {
          created_at?: string
          id?: string
          user_id?: string
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "venue_subscriptions_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      venues: {
        Row: {
          allow_cash_payments: boolean | null
          amenities: Json | null
          capacity: number | null
          contact_number: string | null
          created_at: string
          description: string | null
          id: string
          image_url: string | null
          images: string[] | null
          is_active: boolean
          latitude: number | null
          location: string
          longitude: number | null
          name: string
          opening_hours: string | null
          platform_fee_percentage: number | null
          rating: number | null
          updated_at: string
        }
        Insert: {
          allow_cash_payments?: boolean | null
          amenities?: Json | null
          capacity?: number | null
          contact_number?: string | null
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          images?: string[] | null
          is_active?: boolean
          latitude?: number | null
          location: string
          longitude?: number | null
          name: string
          opening_hours?: string | null
          platform_fee_percentage?: number | null
          rating?: number | null
          updated_at?: string
        }
        Update: {
          allow_cash_payments?: boolean | null
          amenities?: Json | null
          capacity?: number | null
          contact_number?: string | null
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          images?: string[] | null
          is_active?: boolean
          latitude?: number | null
          location?: string
          longitude?: number | null
          name?: string
          opening_hours?: string | null
          platform_fee_percentage?: number | null
          rating?: number | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      admin_update_booking_status: {
        Args: {
          booking_id_param: string
          new_status_param: string
          cancellation_reason_param?: string
        }
        Returns: Json
      }
      backfill_historical_earnings: {
        Args: {
          p_start_date?: string
          p_end_date?: string
          p_venue_id?: string
        }
        Returns: Json
      }
      calculate_daily_earnings: {
        Args: { p_venue_id: string; p_target_date?: string }
        Returns: Json
      }
      calculate_net_amount: {
        Args: { gross_amount: number; platform_fee_percentage: number }
        Returns: number
      }
      cleanup_expired_pending_users: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      create_booking_with_lock: {
        Args:
          | {
              p_court_id: string
              p_user_id: string
              p_booking_date: string
              p_start_time: string
              p_end_time: string
              p_total_price: number
              p_guest_name?: string
              p_guest_phone?: string
            }
          | {
              p_court_id: string
              p_user_id: string
              p_booking_date: string
              p_start_time: string
              p_end_time: string
              p_total_price: number
              p_guest_name?: string
              p_guest_phone?: string
              p_booked_by_admin_id?: string
              p_payment_hold_until?: string
            }
          | {
              p_court_id: string
              p_user_id: string
              p_booking_date: string
              p_start_time: string
              p_end_time: string
              p_total_price: number
              p_guest_name?: string
              p_guest_phone?: string
              p_booked_by_admin_id?: string
              p_payment_reference?: string
              p_payment_status?: string
            }
          | {
              p_court_id: string
              p_user_id: string
              p_booking_date: string
              p_start_time: string
              p_end_time: string
              p_total_price: number
              p_guest_name?: string
              p_guest_phone?: string
              p_booked_by_admin_id?: string
              p_payment_reference?: string
              p_payment_status?: string
              p_payment_method?: string
            }
        Returns: string
      }
      create_help_request: {
        Args:
          | { p_user_id: string; p_subject: string }
          | {
              p_user_id: string
              p_subject: string
              p_venue_id: string
              p_category: string
            }
        Returns: {
          category: string | null
          created_at: string
          id: string
          last_message_at: string
          status: string
          subject: string
          ticket_number: string
          updated_at: string
          user_id: string
          venue_id: string | null
        }
      }
      create_test_booking_for_email: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      create_weekly_settlement: {
        Args: { p_venue_id: string; p_week_start_date: string }
        Returns: Json
      }
      debug_edge_function_call: {
        Args: { booking_id_param: string }
        Returns: Json
      }
      fix_help_request_data: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      gbt_bit_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bool_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bool_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bpchar_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bytea_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_cash_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_cash_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_date_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_date_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_enum_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_enum_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float4_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float4_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_inet_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int2_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int2_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int4_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int4_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_numeric_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_oid_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_oid_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_text_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_time_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_time_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_timetz_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_ts_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_ts_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_tstz_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_uuid_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_uuid_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_var_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_var_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey_var_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey_var_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey16_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey16_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey2_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey2_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey32_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey32_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey4_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey4_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey8_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey8_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      generate_slug: {
        Args: { team_name: string }
        Returns: string
      }
      generate_ticket_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_tournament_slug: {
        Args: { tournament_name: string }
        Returns: string
      }
      get_admin_daily_earnings: {
        Args: { p_start_date: string; p_end_date: string; p_venue_id?: string }
        Returns: {
          venue_id: string
          venue_name: string
          cycle_date: string
          cycle_day_name: string
          total_bookings: number
          confirmed_bookings: number
          gross_revenue: number
          platform_fee_amount: number
          net_revenue: number
          is_current_day: boolean
          is_frozen: boolean
        }[]
      }
      get_admin_dashboard_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_venues: number
          total_bookings: number
          recent_bookings: number
          total_courts: number
        }[]
      }
      get_admin_dashboard_stats_optimized: {
        Args: { admin_user_id: string; target_date?: string }
        Returns: Json
      }
      get_admin_settlements: {
        Args: { p_venue_id?: string; p_status?: string; p_limit?: number }
        Returns: {
          settlement_id: string
          venue_id: string
          venue_name: string
          settlement_week_start: string
          settlement_week_end: string
          settlement_reference: string
          total_gross_revenue: number
          total_platform_fees: number
          total_net_revenue: number
          total_refunds: number
          total_bookings: number
          total_confirmed_bookings: number
          total_cancelled_bookings: number
          total_admin_bookings: number
          status: string
          expected_settlement_date: string
          processed_at: string
          processed_by_name: string
          settled_at: string
          settled_by_name: string
          notes: string
          created_at: string
          updated_at: string
        }[]
      }
      get_admin_venues: {
        Args: Record<PropertyKey, never>
        Returns: {
          venue_id: string
          venue_name: string
          allow_cash_payments: boolean
        }[]
      }
      get_analytics_dashboard_optimized: {
        Args: { admin_user_id: string; start_date: string; end_date: string }
        Returns: Json
      }
      get_available_slots: {
        Args: { p_court_id: string; p_date: string }
        Returns: {
          start_time: string
          end_time: string
          is_available: boolean
        }[]
      }
      get_booking_email_data: {
        Args: { booking_id_param?: string }
        Returns: {
          booking_id: string
          booking_date: string
          start_time: string
          end_time: string
          total_price: number
          status: Database["public"]["Enums"]["booking_status"]
          payment_reference: string
          booking_created_at: string
          confirmation_email_sent: boolean
          confirmation_email_sent_at: string
          confirmation_email_error: string
          user_id: string
          user_name: string
          user_email: string
          user_phone: string
          guest_name: string
          guest_phone: string
          court_id: string
          court_name: string
          venue_id: string
          venue_name: string
          venue_address: string
          venue_phone: string
          sport_id: string
          sport_name: string
          duration_hours: number
          booking_reference: string
          recipient_email: string
          recipient_name: string
          formatted_date: string
          formatted_start_time: string
          formatted_end_time: string
          formatted_duration: string
        }[]
      }
      get_cancellations_with_details: {
        Args: {
          admin_user_id: string
          limit_count?: number
          offset_count?: number
        }
        Returns: {
          cancellation_id: string
          booking_id: string
          booking_date: string
          start_time: string
          end_time: string
          venue_name: string
          court_name: string
          customer_name: string
          customer_phone: string
          total_price: number
          cancellation_reason: string
          cancelled_by_role: string
          cancelled_at: string
          refund_status: string
          refund_amount: number
          refund_processed_at: string
          payment_reference: string
        }[]
      }
      get_custom_revenue_optimized: {
        Args: { admin_user_id: string; start_date: string; end_date: string }
        Returns: {
          gross_revenue: number
          net_revenue: number
          platform_fee_amount: number
        }[]
      }
      get_daily_revenue_with_fees: {
        Args: { p_start_date: string; p_end_date: string; p_venue_id?: string }
        Returns: {
          booking_date: string
          gross_revenue: number
          net_revenue: number
          platform_fee_amount: number
          booking_count: number
        }[]
      }
      get_help_requests: {
        Args: { p_status?: string }
        Returns: {
          id: string
          user_id: string
          subject: string
          status: string
          ticket_number: string
          category: string
          venue_id: string
          created_at: string
          updated_at: string
          last_message_at: string
          user_name: string
          user_email: string
        }[]
      }
      get_popular_courts_optimized: {
        Args: { admin_user_id: string; days_back?: number }
        Returns: Json
      }
      get_recent_activity_optimized: {
        Args: { admin_user_id: string; activity_limit?: number }
        Returns: Json
      }
      get_revenue_report_with_fees: {
        Args: { p_start_date: string; p_end_date: string; p_venue_id?: string }
        Returns: {
          booking_id: string
          booking_date: string
          venue_name: string
          court_name: string
          customer_name: string
          gross_amount: number
          platform_fee_amount: number
          net_amount: number
          payment_method: string
          payment_status: string
        }[]
      }
      get_user_help_requests: {
        Args: { p_user_id: string }
        Returns: {
          category: string | null
          created_at: string
          id: string
          last_message_at: string
          status: string
          subject: string
          ticket_number: string
          updated_at: string
          user_id: string
          venue_id: string | null
        }[]
      }
      get_user_resolved_help_requests: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          user_id: string
          subject: string
          status: string
          ticket_number: string
          category: string
          venue_id: string
          created_at: string
          updated_at: string
          last_message_at: string
          first_message_preview: string
        }[]
      }
      get_user_venues: {
        Args: { user_uuid?: string }
        Returns: string[]
      }
      get_venue_revenue_with_fees: {
        Args: { p_start_date: string; p_end_date: string; p_venue_id?: string }
        Returns: {
          venue_id: string
          venue_name: string
          gross_revenue: number
          net_revenue: number
          platform_fee_amount: number
          booking_count: number
          average_gross_booking_value: number
          average_net_booking_value: number
        }[]
      }
      has_role: {
        Args:
          | { role_name: Database["public"]["Enums"]["app_role"] }
          | {
              user_uuid: string
              required_role: Database["public"]["Enums"]["app_role"]
            }
        Returns: boolean
      }
      insert_payment_log: {
        Args: {
          p_booking_id: string
          p_payment_id: string
          p_event_type: string
          p_amount: number
          p_status: string
          p_payload: Json
        }
        Returns: string
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_admin_or_super: {
        Args: { user_uuid?: string }
        Returns: boolean
      }
      is_admin_user: {
        Args: { user_id_param?: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: Record<PropertyKey, never> | { user_id_param?: string }
        Returns: boolean
      }
      is_venue_admin: {
        Args: { venue_id_param: string }
        Returns: boolean
      }
      is_venue_admin_for: {
        Args: { user_uuid: string; venue_uuid: string }
        Returns: boolean
      }
      test_booking_email: {
        Args: { booking_id_param: string }
        Returns: Json
      }
      test_email_data_function: {
        Args: { test_booking_id?: string }
        Returns: Json
      }
      test_fixed_trigger: {
        Args: { test_booking_id: string }
        Returns: Json
      }
      test_notification_still_works: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      test_trigger_execution: {
        Args: { test_booking_id: string }
        Returns: Json
      }
      test_trigger_with_simulation: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      update_help_request_status: {
        Args: { p_help_request_id: string; p_status: string }
        Returns: {
          category: string | null
          created_at: string
          id: string
          last_message_at: string
          status: string
          subject: string
          ticket_number: string
          updated_at: string
          user_id: string
          venue_id: string | null
        }
      }
      update_refund_status: {
        Args: {
          cancellation_id: string
          new_status: string
          refund_notes?: string
          razorpay_refund_id?: string
        }
        Returns: boolean
      }
      update_settlement_status: {
        Args: {
          p_settlement_id: string
          p_new_status: string
          p_notes?: string
        }
        Returns: Json
      }
      validate_admin_booking_access: {
        Args: { booking_id: string }
        Returns: boolean
      }
      validate_verification_token: {
        Args: { token_input: string }
        Returns: {
          is_valid: boolean
          user_data: Json
          error_message: string
        }[]
      }
    }
    Enums: {
      app_role: "user" | "admin" | "super_admin"
      booking_status: "pending" | "confirmed" | "cancelled" | "completed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      app_role: ["user", "admin", "super_admin"],
      booking_status: ["pending", "confirmed", "cancelled", "completed"],
    },
  },
} as const
