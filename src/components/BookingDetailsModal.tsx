
import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Calendar, Clock, MapPin, User, CreditCard, Info } from 'lucide-react';
import SportDisplayName from '@/components/SportDisplayName';
import { GroupedBooking } from '@/utils/bookingGrouping';

interface BookingDetailsModalProps {
  booking: GroupedBooking | null;
  isOpen: boolean;
  onClose: () => void;
}

const BookingDetailsModal: React.FC<BookingDetailsModalProps> = ({
  booking,
  isOpen,
  onClose,
}) => {
  if (!booking) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-500/20 text-green-400';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'cancelled':
        return 'bg-red-500/20 text-red-400';
      case 'completed':
        return 'bg-blue-500/20 text-blue-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getPaymentStatusColor = (status: string | null) => {
    if (!status) return 'bg-gray-500/20 text-gray-400';
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-500/20 text-green-400';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'failed':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl bg-navy-900 border-navy-700 text-white max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white flex items-center gap-2">
            <Info className="h-5 w-5 text-emerald-400" />
            Booking Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Booking Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
              </span>
              {booking.slot_count > 1 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-500/20 text-emerald-400">
                  {booking.slot_count} slots
                </span>
              )}
            </div>
          </div>

          {/* Venue & Court Information */}
          <div className="bg-white/5 rounded-lg p-4 border border-white/10">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
              <MapPin className="h-5 w-5 text-emerald-400" />
              Venue & Court Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-300">Venue</p>
                <p className="text-base font-medium text-white">{booking.court.venue.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-300">Court</p>
                <p className="text-base font-medium text-white">{booking.court.name}</p>
              </div>
              <div className="md:col-span-2">
                <p className="text-sm text-gray-300">Sport</p>
                <p className="text-base font-medium text-white">
                  <SportDisplayName 
                    venueId={booking.court.venue.id} 
                    sportId={booking.court.sport.id} 
                    defaultName={booking.court.sport.name} 
                  />
                </p>
              </div>
            </div>
          </div>

          {/* Date & Time Information */}
          <div className="bg-white/5 rounded-lg p-4 border border-white/10">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
              <Calendar className="h-5 w-5 text-emerald-400" />
              Date & Time
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-300">Date</p>
                <p className="text-base font-medium text-white">{formatDate(booking.booking_date)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-300">Time</p>
                <p className="text-base font-medium text-white">
                  {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                </p>
              </div>
            </div>
          </div>

          {/* User Information */}
          {(booking.admin_booking || booking.guest_name || booking.user_info) && (
            <div className="bg-white/5 rounded-lg p-4 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                <User className="h-5 w-5 text-emerald-400" />
                Customer Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {booking.admin_booking ? (
                  <>
                    <div>
                      <p className="text-sm text-gray-300">Customer Name</p>
                      <p className="text-base font-medium text-purple-300">
                        {booking.admin_booking.customer_name}
                        <span className="text-xs text-purple-400 ml-2">(Admin Booking)</span>
                      </p>
                    </div>
                    {booking.admin_booking.customer_phone && (
                      <div>
                        <p className="text-sm text-gray-300">Phone Number</p>
                        <p className="text-base font-medium text-white">{booking.admin_booking.customer_phone}</p>
                      </div>
                    )}
                  </>
                ) : booking.guest_name ? (
                  <div>
                    <p className="text-sm text-gray-300">Guest Name</p>
                    <p className="text-base font-medium text-white">
                      {booking.guest_name}
                      <span className="text-xs text-gray-400 ml-2">(Guest Booking)</span>
                    </p>
                  </div>
                ) : booking.user_info ? (
                  <>
                    <div>
                      <p className="text-sm text-gray-300">Full Name</p>
                      <p className="text-base font-medium text-white">{booking.user_info.full_name || 'N/A'}</p>
                    </div>
                    {booking.user_info.email && (
                      <div>
                        <p className="text-sm text-gray-300">Email</p>
                        <p className="text-base font-medium text-white">{booking.user_info.email}</p>
                      </div>
                    )}
                    {booking.user_info.phone && (
                      <div>
                        <p className="text-sm text-gray-300">Phone</p>
                        <p className="text-base font-medium text-white">{booking.user_info.phone}</p>
                      </div>
                    )}
                  </>
                ) : null}
              </div>
            </div>
          )}

          {/* Cancellation Information */}
          {booking.status === 'cancelled' && booking.individual_bookings[0]?.cancellation_reason && (
            <div className="bg-red-900/20 border border-red-800/30 rounded-lg p-4 shadow-sm">
              <h3 className="text-base sm:text-lg font-semibold text-red-300 mb-3 flex items-center gap-2">
                <Info className="h-4 w-4 sm:h-5 sm:w-5 text-red-400 flex-shrink-0" />
                Cancellation Information
              </h3>
              <div>
                <p className="text-xs sm:text-sm text-red-300 mb-2 font-medium">Reason for Cancellation:</p>
                <p className="text-sm sm:text-base text-red-200 leading-relaxed break-words">
                  {booking.individual_bookings[0].cancellation_reason}
                </p>
              </div>
            </div>
          )}

          {/* Payment Information */}
          <div className="bg-white/5 rounded-lg p-4 border border-white/10">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-emerald-400" />
              Payment Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {booking.booking_reference && (
                <div>
                  <p className="text-sm text-gray-300">Booking Reference</p>
                  <p className="text-base font-bold text-indigo-400 font-mono">{booking.booking_reference}</p>
                </div>
              )}
              <div>
                <p className="text-sm text-gray-300">Total Amount</p>
                <p className="text-lg font-bold text-green-400">₹{booking.total_price.toFixed(2)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-300">Payment Method</p>
                <p className="text-base font-medium text-white">
                  {booking.payment_method ? booking.payment_method.charAt(0).toUpperCase() + booking.payment_method.slice(1) : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-300">Payment Status</p>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(booking.payment_status)}`}>
                  {booking.payment_status || 'Unknown'}
                </span>
              </div>
              {booking.payment_reference && (
                <div>
                  <p className="text-sm text-gray-300">Payment Reference ID</p>
                  <p className="text-sm font-medium text-white break-all">{booking.payment_reference}</p>
                </div>
              )}
            </div>
          </div>

          {/* Individual Slots (for grouped bookings) */}
          {booking.slot_count > 1 && (
            <div className="bg-white/5 rounded-lg p-4 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                <Clock className="h-5 w-5 text-emerald-400" />
                Individual Time Slots
              </h3>
              <div className="space-y-2">
                {booking.individual_bookings.map((slot, index) => (
                  <div key={slot.id} className="flex items-center justify-between bg-white/5 rounded p-3">
                    <div className="flex items-center gap-3">
                      <span className="text-sm font-medium text-emerald-400">Slot {index + 1}</span>
                      <span className="text-sm text-white">
                        {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm font-medium text-green-400">₹{slot.total_price.toFixed(2)}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(slot.status)}`}>
                        {slot.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BookingDetailsModal;
