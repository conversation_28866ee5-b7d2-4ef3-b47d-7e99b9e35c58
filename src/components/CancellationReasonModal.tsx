import React, { useState } from 'react';
import { AlertCircle, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';

interface CancellationReasonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  bookingInfo?: {
    venueName: string;
    courtName: string;
    date: string;
    time: string;
    customerName?: string;
  };
  loading?: boolean;
}

const PREDEFINED_REASONS = [
  'Customer requested cancellation',
  'Venue maintenance/closure',
  'Weather conditions',
  'Equipment failure',
  'Staff unavailability',
  'Double booking error',
  'Payment issues',
  'Emergency closure',
  'Other (specify below)'
];

export const CancellationReasonModal: React.FC<CancellationReasonModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  bookingInfo,
  loading = false
}) => {
  const [selectedReason, setSelectedReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [error, setError] = useState('');

  const handleConfirm = () => {
    const finalReason = selectedReason === 'Other (specify below)' 
      ? customReason.trim() 
      : selectedReason;

    // Validation
    if (!finalReason) {
      setError('Please select or enter a cancellation reason');
      return;
    }

    if (selectedReason === 'Other (specify below)' && customReason.trim().length < 5) {
      setError('Please provide a detailed reason (minimum 5 characters)');
      return;
    }

    setError('');
    onConfirm(finalReason);
  };

  const handleClose = () => {
    if (!loading) {
      setSelectedReason('');
      setCustomReason('');
      setError('');
      onClose();
    }
  };

  const isOtherSelected = selectedReason === 'Other (specify below)';
  const isFormValid = selectedReason && (!isOtherSelected || customReason.trim().length >= 5);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            Cancel Booking - Reason Required
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Booking Details */}
          {bookingInfo && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Booking Details</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <p><strong>Venue:</strong> {bookingInfo.venueName}</p>
                <p><strong>Court:</strong> {bookingInfo.courtName}</p>
                <p><strong>Date:</strong> {bookingInfo.date}</p>
                <p><strong>Time:</strong> {bookingInfo.time}</p>
                {bookingInfo.customerName && (
                  <p><strong>Customer:</strong> {bookingInfo.customerName}</p>
                )}
              </div>
            </div>
          )}

          {/* Reason Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cancellation Reason *
            </label>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {PREDEFINED_REASONS.map((reason) => (
                <label key={reason} className="flex items-start gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="cancellation-reason"
                    value={reason}
                    checked={selectedReason === reason}
                    onChange={(e) => setSelectedReason(e.target.value)}
                    className="mt-1 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                    disabled={loading}
                  />
                  <span className="text-sm text-gray-700">{reason}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Custom Reason Input */}
          {isOtherSelected && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Please specify the reason *
              </label>
              <textarea
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                rows={3}
                placeholder="Please provide a detailed reason for cancellation..."
                disabled={loading}
                maxLength={500}
              />
              <div className="flex justify-between mt-1">
                <span className="text-xs text-gray-500">
                  Minimum 5 characters required
                </span>
                <span className="text-xs text-gray-500">
                  {customReason.length}/500
                </span>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* Warning Message */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Important:</p>
                <p>This action will cancel the booking and cannot be undone. The cancellation reason will be recorded for audit purposes.</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={loading}
            >
              Keep Booking
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!isFormValid || loading}
              className="flex-1 bg-red-600 hover:bg-red-700"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Cancelling...
                </>
              ) : (
                'Cancel Booking'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CancellationReasonModal;
