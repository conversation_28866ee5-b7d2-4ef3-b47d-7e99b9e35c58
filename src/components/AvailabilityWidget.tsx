import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { getAvailableSlots } from '@/integrations/supabase/custom-types';
import { Loader2, Clock, AlertCircle, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { GetAvailableSlotsResult } from '@/types/help';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/hooks/use-toast';

interface AvailabilitySlot {
  start_time: string;
  end_time: string;
  is_available: boolean;
  is_booked?: boolean;
  is_blocked?: boolean;
  blocked_court_id?: string;
}

interface AvailabilityWidgetProps {
  courtId: string;
  date: string;
  onSelectSlot?: (slot: { start_time: string; end_time: string; is_available: boolean }) => void;
  isAdmin?: boolean;
}

const AvailabilityWidget: React.FC<AvailabilityWidgetProps> = ({
  courtId,
  date,
  onSelectSlot,
  isAdmin = false
}) => {
  const [slots, setSlots] = useState<AvailabilitySlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isMobile = useIsMobile();
  const [lastRefetch, setLastRefetch] = useState<number>(Date.now());

  const fetchAvailability = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!courtId || !date) {
        setError("Invalid court or date information");
        setLoading(false);
        return;
      }

      console.log(`Fetching availability for court ${courtId} on date ${date}`);

      // 1. Fetch court_group_id for the selected court
      const { data: courtDetails, error: courtDetailsError } = await supabase
        .from('courts')
        .select('court_group_id')
        .eq('id', courtId)
        .single();
      if (courtDetailsError) throw courtDetailsError;
      
      let courtIdsToCheck = [courtId];
      if (courtDetails && courtDetails.court_group_id) {
        // 2. If in a group, fetch all court IDs in the group
        const { data: groupCourts, error: groupCourtsError } = await supabase
          .from('courts')
          .select('id')
          .eq('court_group_id', courtDetails.court_group_id)
          .eq('is_active', true);
        if (groupCourtsError) throw groupCourtsError;
        courtIdsToCheck = groupCourts.map((c: { id: string }) => c.id);
      }

      console.log(`Court IDs to check: ${courtIdsToCheck.join(', ')}`);

      // 3. Fetch available slots for the selected court (for template/pricing)
      const { data, error } = await getAvailableSlots(courtId, date);
      if (error) throw error;

      // 4. Fetch bookings for all courts in the group (or just the selected court)
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select('court_id, start_time, end_time, booking_date')
        .in('court_id', courtIdsToCheck)
        .eq('booking_date', date)
        .in('status', ['confirmed', 'pending']);
      if (bookingsError) throw bookingsError;

      // 5. Fetch blocked slots for all courts in the group (or just the selected court)
      const { data: blockedSlots, error: blockedSlotsError } = await supabase
        .from('blocked_slots')
        .select('court_id, start_time, end_time, date')
        .in('court_id', courtIdsToCheck)
        .eq('date', date);
      if (blockedSlotsError) throw blockedSlotsError;

      console.log('AvailabilityWidget - Fetched blocked slots:', blockedSlots);
      console.log('AvailabilityWidget - Fetched bookings:', bookings);

      // 6. Mark slots as unavailable if booked or blocked in any court in the group
      const padTime = (t: string) => t.length === 5 ? t + ':00' : t;
      const slotsWithStatus = data?.map(slot => {
        const slotStart = padTime(slot.start_time);
        const slotEnd = padTime(slot.end_time);
        const blockedSlot = blockedSlots?.find(bs =>
          padTime(bs.start_time) === slotStart &&
          padTime(bs.end_time) === slotEnd
        );
        const isBlocked = !!blockedSlot;
        const isBooked = bookings?.some(b =>
          padTime(b.start_time) === slotStart &&
          padTime(b.end_time) === slotEnd
        );
        return {
          ...slot,
          is_available: slot.is_available && !isBooked && !isBlocked,
          is_booked: isBooked,
          is_blocked: isBlocked,
          blocked_court_id: blockedSlot ? blockedSlot.court_id : undefined
        };
      }) || [];
      
      console.log('Processed slots with status:', slotsWithStatus);
      setSlots(slotsWithStatus);
    } catch (error: any) {
      console.error('Error fetching availability:', error);
      setError(`Error fetching availability: ${error.message}`);
      toast({
        title: "Failed to load availability",
        description: "Please try again or contact support if the problem persists.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // When courtId or date changes, we need to refetch availability
    if (courtId && date) {
      fetchAvailability();
    }
    
    // Reset state when key props change
    return () => {
      setSlots([]);
      setError(null);
      setLoading(true);
    };
  }, [courtId, date, lastRefetch]);

  useEffect(() => {
    // Set up real-time subscription for bookings changes
    if (!courtId || !date) {
      return;
    }

    if (import.meta.env.DEV) {
      console.log('Setting up realtime subscriptions for AvailabilityWidget');
    }

    // Create a unique channel name based on the component instance
    const channelId = `availability_${courtId}_${date}_${Date.now()}`;
    
    // Set up subscription for bookings changes
    const bookingsChannel = supabase.channel(`bookings_${channelId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'bookings',
        filter: `booking_date=eq.${date}`
      }, (payload) => {
        if (import.meta.env.DEV) {
          console.log('Booking change detected in AvailabilityWidget:', payload);
        }
        // Trigger a refetch when booking changes
        fetchAvailability();
      })
      .subscribe();
    
    // Set up subscription for blocked slots changes
    const blockedSlotsChannel = supabase.channel(`blocked_slots_${channelId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'blocked_slots',
        filter: `date=eq.${date}`
      }, (payload) => {
        if (import.meta.env.DEV) {
          console.log('Blocked slot change detected in AvailabilityWidget:', payload);
        }
        // Trigger a refetch when blocked slots change
        fetchAvailability();
      })
      .subscribe();
    
    // Clean up subscriptions when component unmounts
    return () => {
      if (import.meta.env.DEV) {
        console.log('Cleaning up realtime subscriptions for AvailabilityWidget');
      }
      supabase.removeChannel(bookingsChannel);
      supabase.removeChannel(blockedSlotsChannel);
    };
  }, [courtId, date]);

  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const handleSlotClick = (slot: { start_time: string; end_time: string; is_available: boolean; is_booked?: boolean; is_blocked?: boolean }) => {
    if (slot.is_blocked || slot.is_booked) {
      toast({
        title: 'Slot Unavailable',
        description: 'Slot is booked/blocked already, select another one.',
        variant: 'destructive'
      });
      return;
    }
    if (onSelectSlot && (slot.is_available || isAdmin)) {
      console.log('Slot selected:', slot);
      onSelectSlot(slot);
    }
  };

  // Add a manual refresh button
  const handleManualRefresh = () => {
    console.log('Manual refresh triggered');
    setLastRefetch(Date.now());
  };

  return (
    <Card className="w-full border border-indigo/20 bg-navy-light/50 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-medium flex items-center text-white">
            <Clock className="mr-2 h-5 w-5 text-indigo-light" />
            Real-Time Availability
          </CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleManualRefresh}
            className="text-indigo-light hover:text-white hover:bg-indigo/20"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-24">
            <Loader2 className="h-8 w-8 animate-spin text-indigo" />
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-24 text-center">
            <AlertCircle className="h-8 w-8 text-red-400 mb-2" />
            <p className="text-red-400">{error}</p>
            <Button 
              onClick={handleManualRefresh} 
              className="mt-3 bg-indigo hover:bg-indigo-dark text-white"
              size="sm"
            >
              Refresh
            </Button>
          </div>
        ) : slots.length === 0 ? (
          <p className="text-center text-gray-400 py-4">No time slots available for this date</p>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
            {slots.map((slot, index) => (
              <div 
                key={`${slot.start_time}-${slot.end_time}-${index}`}
                className={
                  `border rounded-md p-2 text-center transition-all cursor-pointer
                  hover:transform hover:scale-105
                  ${slot.is_available || isAdmin ? 'border-green-500/30 bg-green-500/10 hover:bg-green-500/20' : ''}
                  ${slot.is_booked ? 'border-red-500/30 bg-red-500/10' : ''}
                  ${slot.is_blocked ? 'border-yellow-500/30 bg-yellow-500/10' : ''}
                  ${(!slot.is_available && isAdmin) ? 'cursor-pointer opacity-70 hover:opacity-100' : ''}
                  ${!slot.is_available && !isAdmin ? 'cursor-not-allowed' : 'cursor-pointer'}
                  `
                }
                onClick={() => handleSlotClick(slot)}
              >
                <p className="text-sm font-medium text-white">
                  {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                </p>
                {slot.is_blocked ? (
                  <Badge className="mt-1 bg-yellow-500/20 text-yellow-700 hover:bg-yellow-500/30">Blocked</Badge>
                ) : slot.is_booked ? (
                  <Badge className="mt-1 bg-red-500/20 text-red-400 hover:bg-red-500/30">Booked</Badge>
                ) : (
                  <Badge className="mt-1 bg-green-500/20 text-green-400 hover:bg-green-500/30">Available</Badge>
                )}
                {/* Unblock button for admins on blocked slots */}
                {isAdmin && slot.is_blocked && (
                  <Button
                    size="sm"
                    variant="outline"
                    className="mt-2 text-xs bg-yellow-100 text-yellow-800 border-yellow-400 hover:bg-yellow-200"
                    onClick={async (e) => {
                      e.stopPropagation();
                      const padTime = (t: string) => t.length === 5 ? t + ':00' : t;
                      // Use the actual blocked_court_id for unblock
                      const unblockCourtId = slot.blocked_court_id || courtId;
                      // Fetch courtIdsToCheck as in fetchAvailability
                      let courtIdsToCheck = [unblockCourtId];
                      const { data: courtDetails } = await supabase
                        .from('courts')
                        .select('court_group_id')
                        .eq('id', unblockCourtId)
                        .single();
                      if (courtDetails && courtDetails.court_group_id) {
                        const { data: groupCourts } = await supabase
                          .from('courts')
                          .select('id')
                          .eq('court_group_id', courtDetails.court_group_id)
                          .eq('is_active', true);
                        if (groupCourts) courtIdsToCheck = groupCourts.map((c: { id: string }) => c.id);
                      }
                      // Print all blocked slots for all courts in group for this date
                      const { data: allBlockedBefore } = await supabase
                        .from('blocked_slots')
                        .select('*')
                        .in('court_id', courtIdsToCheck)
                        .eq('date', date);
                      console.log('All blocked slots for group/date BEFORE delete:', allBlockedBefore);
                      const matchObj = {
                        court_id: unblockCourtId,
                        start_time: padTime(slot.start_time),
                        end_time: padTime(slot.end_time),
                        date: date
                      };
                      console.log('Attempting to unblock:', matchObj);
                      const { data: blockedRows, error: fetchError } = await supabase
                        .from('blocked_slots')
                        .select('*')
                        .match(matchObj);
                      console.log('Blocked slot rows found for unblock:', blockedRows, fetchError);
                      if (blockedRows && blockedRows.length > 0 && blockedRows[0].id) {
                        const { error: delError } = await supabase
                          .from('blocked_slots')
                          .delete()
                          .eq('id', blockedRows[0].id);
                        console.log('Deleted by id:', blockedRows[0].id, delError);
                      } else {
                        const { error: delError } = await supabase
                          .from('blocked_slots')
                          .delete()
                          .match(matchObj);
                        console.log('Deleted by match:', matchObj, delError);
                      }
                      // Print all blocked slots for all courts in group for this date after delete
                      const { data: allBlockedAfter } = await supabase
                        .from('blocked_slots')
                        .select('*')
                        .in('court_id', courtIdsToCheck)
                        .eq('date', date);
                      console.log('All blocked slots for group/date AFTER delete:', allBlockedAfter);
                      toast({
                        title: 'Slot Unblocked',
                        description: 'The slot has been unblocked.',
                        variant: 'default'
                      });
                      setLastRefetch(Date.now());
                    }}
                  >
                    Undo Block
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AvailabilityWidget;
