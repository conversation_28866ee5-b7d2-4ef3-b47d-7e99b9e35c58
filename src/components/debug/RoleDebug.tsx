import React from 'react';
import { useAuth } from '@/context/AuthContext';

const RoleDebug: React.FC = () => {
  const { user, userRole } = useAuth();

  return (
    <div className="fixed top-4 right-4 bg-black/90 text-white p-4 rounded-lg z-50 max-w-sm">
      <h3 className="font-bold text-green-400 mb-2">🔍 Role Debug Info</h3>
      <div className="space-y-1 text-xs">
        <div><strong>User ID:</strong> {user?.id || 'Not logged in'}</div>
        <div><strong>Email:</strong> {user?.email || 'N/A'}</div>
        <div><strong>User Role:</strong> <span className="text-yellow-400">{userRole || 'Not detected'}</span></div>
        <div><strong>Is Super Admin:</strong> <span className={userRole === 'super_admin' ? 'text-green-400' : 'text-red-400'}>{userRole === 'super_admin' ? 'YES' : 'NO'}</span></div>
        <div><strong>Current Path:</strong> {window.location.pathname}</div>
      </div>
    </div>
  );
};

export default RoleDebug;
