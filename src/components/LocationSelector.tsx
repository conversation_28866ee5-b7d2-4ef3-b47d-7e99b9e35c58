
import React, { useState, useEffect } from 'react';
import { MapPin, Navigation, RefreshCw, Search, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useEnhancedLocation } from '@/hooks/use-enhanced-location';
import { geocoding } from '@/utils/geocoding';
import { Address } from '@/types/location';

interface LocationSelectorProps {
  onLocationChange?: (location: any) => void;
  className?: string;
}

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  onLocationChange,
  className = ""
}) => {
  const {
    data: locationData,
    isLoading,
    error,
    hasPermission,
    isRefreshing,
    detectLocation,
    refreshLocation,
    setManualLocation
  } = useEnhancedLocation();

  const [showManualEntry, setShowManualEntry] = useState(false);
  const [manualAddress, setManualAddress] = useState('');
  const [addressSuggestions, setAddressSuggestions] = useState<Address[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Notify parent of location changes
  useEffect(() => {
    if (locationData && onLocationChange) {
      onLocationChange(locationData);
    }
  }, [locationData, onLocationChange]);

  // Handle manual address search
  const handleAddressSearch = async (query: string) => {
    setManualAddress(query);
    
    if (query.length < 3) {
      setAddressSuggestions([]);
      return;
    }

    setIsSearching(true);
    try {
      const suggestions = await geocoding.searchAddresses(query);
      setAddressSuggestions(suggestions);
    } catch (error) {
      console.error('Address search failed:', error);
      setAddressSuggestions([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle address selection
  const handleAddressSelect = async (address: Address) => {
    setManualAddress(address.display_name);
    setAddressSuggestions([]);
    await setManualLocation(address.display_name);
    setShowManualEntry(false);
  };

  // Handle manual address submit
  const handleManualSubmit = async () => {
    if (!manualAddress.trim()) return;
    
    await setManualLocation(manualAddress.trim());
    setShowManualEntry(false);
    setAddressSuggestions([]);
  };

  // Get accuracy indicator
  const getAccuracyInfo = () => {
    if (!locationData) return null;

    const { source, accuracy } = locationData;
    
    switch (source) {
      case 'gps':
        return {
          icon: <CheckCircle className="h-4 w-4 text-green-500" />,
          label: 'High accuracy',
          color: 'bg-green-500/10 text-green-400 border-green-500/20'
        };
      case 'ip':
        return {
          icon: <Navigation className="h-4 w-4 text-yellow-500" />,
          label: 'Approximate',
          color: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
        };
      case 'manual':
        return {
          icon: <MapPin className="h-4 w-4 text-blue-500" />,
          label: 'Manual',
          color: 'bg-blue-500/10 text-blue-400 border-blue-500/20'
        };
      case 'cache':
        return {
          icon: <RefreshCw className="h-4 w-4 text-gray-500" />,
          label: 'Cached',
          color: 'bg-gray-500/10 text-gray-400 border-gray-500/20'
        };
      default:
        return null;
    }
  };

  const accuracyInfo = getAccuracyInfo();

  return (
    <Card className={`border-none bg-black animate-fade-in shadow-lg ${className}`}>
      <CardContent className="p-4">
        <div className="flex flex-col gap-3">
          {/* Current Location Display */}
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 bg-[#1E3B2C]/80 p-3 rounded-full">
              <MapPin className="h-6 w-6 text-[#2def80]" />
            </div>
            
            <div className="flex-grow min-w-0">
              <h3 className="font-semibold text-[#2def80] text-sm mb-1">
                Current Location
              </h3>
              
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-gray-300" />
                  <span className="text-sm text-gray-300">Detecting location...</span>
                </div>
              ) : error ? (
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-400" />
                  <span className="text-sm text-red-400">Location unavailable</span>
                </div>
              ) : locationData ? (
                <div className="space-y-1">
                  <p className="text-sm text-white truncate">
                    {locationData.address.area || locationData.address.city || locationData.address.display_name}
                  </p>
                  {locationData.address.city && locationData.address.area && (
                    <p className="text-xs text-gray-400 truncate">
                      {locationData.address.city}
                    </p>
                  )}
                  
                  {accuracyInfo && (
                    <div className="flex items-center gap-2 mt-2">
                      <Badge className={`text-xs ${accuracyInfo.color}`}>
                        {accuracyInfo.icon}
                        <span className="ml-1">{accuracyInfo.label}</span>
                      </Badge>
                    </div>
                  )}
                </div>
              ) : (
                <span className="text-sm text-gray-400">No location set</span>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={refreshLocation}
              disabled={isRefreshing}
              className="flex-1 bg-[#1E3B2C] hover:bg-[#2def80] text-white text-sm h-9"
            >
              {isRefreshing ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
            
            <Button
              onClick={() => setShowManualEntry(!showManualEntry)}
              variant="outline"
              className="flex-1 border-[#1E3B2C] text-[#2def80] hover:bg-[#1E3B2C]/10 text-sm h-9"
            >
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>

          {/* Manual Entry Section */}
          {showManualEntry && (
            <div className="space-y-2 pt-2 border-t border-[#1E3B2C]">
              <div className="relative">
                <Input
                  placeholder="Enter your location..."
                  value={manualAddress}
                  onChange={(e) => handleAddressSearch(e.target.value)}
                  className="bg-gray-900/50 border-[#1E3B2C] text-white placeholder:text-gray-400"
                />
                {isSearching && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
                )}
              </div>
              
              {/* Address Suggestions */}
              {addressSuggestions.length > 0 && (
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {addressSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleAddressSelect(suggestion)}
                      className="w-full text-left p-2 text-sm bg-gray-800/50 hover:bg-gray-700/50 rounded border border-[#1E3B2C]/30 text-white transition-colors"
                    >
                      <div className="truncate">{suggestion.display_name}</div>
                    </button>
                  ))}
                </div>
              )}
              
              {/* Submit Button */}
              {manualAddress && addressSuggestions.length === 0 && !isSearching && (
                <Button
                  onClick={handleManualSubmit}
                  className="w-full bg-[#2def80] hover:bg-[#1E3B2C] text-black text-sm h-9"
                >
                  Set Location
                </Button>
              )}
            </div>
          )}

          {/* Permission Guidance */}
          {error && hasPermission === false && (
            <div className="text-xs text-gray-400 bg-gray-900/30 p-2 rounded border border-[#1E3B2C]/20">
              💡 Enable location access in your browser settings for better accuracy
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
