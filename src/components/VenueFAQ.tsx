
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { VenueFAQ as VenueFAQType } from '@/types/help';
import { MessageCircle, HelpCircle } from 'lucide-react';

interface VenueFAQProps {
  venueId: string;
}

const CATEGORY_LABELS = {
  general: 'General',
  booking_issues: 'Booking',
  facility_questions: 'Facilities',
  payment_problems: 'Payment'
};

export const VenueFAQ: React.FC<VenueFAQProps> = ({ venueId }) => {
  const [faqs, setFaqs] = useState<VenueFAQType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVenueFAQs = async () => {
      try {
        const { data, error } = await supabase
          .from('venue_faqs')
          .select('*')
          .eq('venue_id', venueId)
          .eq('is_active', true)
          .order('order_index')
          .order('created_at');

        if (error) throw error;
        setFaqs(data || []);
      } catch (error) {
        console.error('Error fetching venue FAQs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVenueFAQs();
  }, [venueId]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="w-5 h-5" />
            Frequently Asked Questions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-12 bg-gray-200 rounded" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (faqs.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="w-5 h-5" />
            Frequently Asked Questions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <MessageCircle className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>No FAQs available for this venue yet.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Group FAQs by category
  const faqsByCategory = faqs.reduce((acc, faq) => {
    const category = faq.category || 'general';
    if (!acc[category]) acc[category] = [];
    acc[category].push(faq);
    return acc;
  }, {} as Record<string, VenueFAQType[]>);

  const categories = Object.keys(faqsByCategory);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <HelpCircle className="w-5 h-5" />
          Frequently Asked Questions
        </CardTitle>
      </CardHeader>
      <CardContent>
        {categories.length === 1 ? (
          // Single category - show directly
          <Accordion type="single" collapsible className="w-full">
            {faqsByCategory[categories[0]].map((faq, index) => (
              <AccordionItem key={faq.id} value={`item-${index}`}>
                <AccordionTrigger className="text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>
                  <div className="text-gray-600 whitespace-pre-wrap">
                    {faq.answer}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          // Multiple categories - show in tabs
          <Tabs defaultValue={categories[0]} className="w-full">
            <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${categories.length}, 1fr)` }}>
              {categories.map((category) => (
                <TabsTrigger key={category} value={category} className="text-xs">
                  {CATEGORY_LABELS[category as keyof typeof CATEGORY_LABELS] || category}
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {faqsByCategory[category].length}
                  </Badge>
                </TabsTrigger>
              ))}
            </TabsList>
            
            {categories.map((category) => (
              <TabsContent key={category} value={category}>
                <Accordion type="single" collapsible className="w-full">
                  {faqsByCategory[category].map((faq, index) => (
                    <AccordionItem key={faq.id} value={`${category}-item-${index}`}>
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="text-gray-600 whitespace-pre-wrap">
                          {faq.answer}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};
