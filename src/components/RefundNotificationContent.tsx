import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  FileText,
  CreditCard,
  Hash
} from 'lucide-react';

interface RefundNotificationContentProps {
  metadata: {
    reference?: string;
    refund_note?: string;
    refund_status?: string;
    refund_id?: string;
    cancellation_id?: string;
    booking_id?: string;
  };
}

const RefundNotificationContent: React.FC<RefundNotificationContentProps> = ({ metadata }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'not_applicable':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'not_applicable':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'processed':
        return 'Processed';
      case 'rejected':
        return 'Rejected';
      case 'not_applicable':
        return 'Not Applicable';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="space-y-3 text-black">
      {/* Reference */}
      {metadata.reference && (
        <div className="flex items-center gap-2">
          <Hash className="h-4 w-4 text-gray-600" />
          <span className="font-medium text-gray-700">Reference:</span>
          <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded border">
            {metadata.reference}
          </span>
        </div>
      )}

      {/* Refund Status */}
      {metadata.refund_status && (
        <div className="flex items-center gap-2">
          {getStatusIcon(metadata.refund_status)}
          <span className="font-medium text-gray-700">Refund Status:</span>
          <Badge 
            variant="outline" 
            className={`${getStatusColor(metadata.refund_status)} flex items-center gap-1`}
          >
            {getStatusText(metadata.refund_status)}
          </Badge>
        </div>
      )}

      {/* Refund Note */}
      {metadata.refund_note && (
        <div className="flex items-start gap-2">
          <FileText className="h-4 w-4 text-gray-600 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <span className="font-medium text-gray-700">Refund Note:</span>
            <div className="mt-1 text-sm text-gray-800 bg-gray-50 p-2 rounded border">
              {metadata.refund_note}
            </div>
          </div>
        </div>
      )}

      {/* Refund ID */}
      {metadata.refund_id && (
        <div className="flex items-center gap-2">
          <CreditCard className="h-4 w-4 text-gray-600" />
          <span className="font-medium text-gray-700">Refund ID:</span>
          <span className="font-mono text-sm bg-blue-50 text-blue-800 px-2 py-1 rounded border border-blue-200">
            {metadata.refund_id}
          </span>
        </div>
      )}
    </div>
  );
};

export default RefundNotificationContent;
