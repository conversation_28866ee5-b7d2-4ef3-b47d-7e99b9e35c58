import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';
import { Plus, Edit2, Trash2, HelpCircle } from 'lucide-react';
import { HELP_CATEGORIES, type HelpCategory } from '@/types/help';

interface VenueFAQ {
  id: string;
  venue_id: string;
  question: string;
  answer: string;
  category: HelpCategory;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  order_index: number;
}

interface Venue {
  id: string;
  name: string;
}

const CATEGORY_LABELS = {
  [HELP_CATEGORIES.BOOKING_ISSUES]: 'Booking Issues',
  [HELP_CATEGORIES.FACILITY_QUESTIONS]: 'Facility Questions',
  [HELP_CATEGORIES.PAYMENT_PROBLEMS]: 'Payment Problems',
  [HELP_CATEGORIES.GENERAL]: 'General Inquiry'
};

export const VenueFAQManagement: React.FC = () => {
  const { user } = useAuth();
  const [venues, setVenues] = useState<Venue[]>([]);
  const [selectedVenueId, setSelectedVenueId] = useState<string>('');
  const [faqs, setFaqs] = useState<VenueFAQ[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingFaq, setEditingFaq] = useState<VenueFAQ | null>(null);
  const [newFaq, setNewFaq] = useState({
    question: '',
    answer: '',
    category: HELP_CATEGORIES.GENERAL as HelpCategory
  });
  const [loading, setLoading] = useState(true);

  // Fetch venues
  useEffect(() => {
    const fetchVenues = async () => {
      try {
        const { data, error } = await supabase
          .from('venues')
          .select('id, name')
          .order('name');

        if (error) throw error;
        setVenues(data || []);
        if (data && data.length > 0) {
          setSelectedVenueId(data[0].id);
        }
      } catch (error) {
        console.error('Error fetching venues:', error);
        toast({
          title: 'Error',
          description: 'Failed to load venues',
          variant: 'destructive'
        });
      }
    };

    fetchVenues();
  }, []);

  // Fetch FAQs when venue is selected
  useEffect(() => {
    if (!selectedVenueId) return;

    const fetchFaqs = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('venue_faqs')
          .select('*')
          .eq('venue_id', selectedVenueId)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setFaqs((data || []).map(faq => ({
          ...faq,
          category: faq.category as HelpCategory
        })));
      } catch (error) {
        console.error('Error fetching FAQs:', error);
        toast({
          title: 'Error',
          description: 'Failed to load FAQs',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchFaqs();
  }, [selectedVenueId]);

  const handleAddFaq = async () => {
    if (!selectedVenueId || !newFaq.question.trim() || !newFaq.answer.trim()) {
      toast({
        title: 'Required Fields',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('venue_faqs')
        .insert({
          venue_id: selectedVenueId,
          question: newFaq.question.trim(),
          answer: newFaq.answer.trim(),
          category: newFaq.category,
          is_active: true,
          order_index: 0
        });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'FAQ added successfully'
      });

      // Refresh FAQs with category casting
      const { data } = await supabase
        .from('venue_faqs')
        .select('*')
        .eq('venue_id', selectedVenueId)
        .order('created_at', { ascending: false });

      setFaqs((data || []).map(faq => ({
        ...faq,
        category: faq.category as HelpCategory
      })));
      setIsAddModalOpen(false);
      setNewFaq({
        question: '',
        answer: '',
        category: HELP_CATEGORIES.GENERAL
      });
    } catch (error) {
      console.error('Error adding FAQ:', error);
      toast({
        title: 'Error',
        description: 'Failed to add FAQ',
        variant: 'destructive'
      });
    }
  };

  const handleEditFaq = async () => {
    if (!editingFaq || !editingFaq.question.trim() || !editingFaq.answer.trim()) {
      toast({
        title: 'Required Fields',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('venue_faqs')
        .update({
          question: editingFaq.question.trim(),
          answer: editingFaq.answer.trim(),
          category: editingFaq.category,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingFaq.id);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'FAQ updated successfully'
      });

      // Refresh FAQs with category casting
      const { data } = await supabase
        .from('venue_faqs')
        .select('*')
        .eq('venue_id', selectedVenueId)
        .order('created_at', { ascending: false });

      setFaqs((data || []).map(faq => ({
        ...faq,
        category: faq.category as HelpCategory
      })));
      setIsEditModalOpen(false);
      setEditingFaq(null);
    } catch (error) {
      console.error('Error updating FAQ:', error);
      toast({
        title: 'Error',
        description: 'Failed to update FAQ',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteFaq = async (faqId: string) => {
    if (!confirm('Are you sure you want to delete this FAQ?')) return;

    try {
      const { error } = await supabase
        .from('venue_faqs')
        .delete()
        .eq('id', faqId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'FAQ deleted successfully'
      });

      // Refresh FAQs with category casting
      const { data } = await supabase
        .from('venue_faqs')
        .select('*')
        .eq('venue_id', selectedVenueId)
        .order('created_at', { ascending: false });

      setFaqs((data || []).map(faq => ({
        ...faq,
        category: faq.category as HelpCategory
      })));
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete FAQ',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card className="bg-gradient-to-br from-black to-emerald-900/10 border-emerald-900/30 shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-emerald-400 flex items-center gap-2">
            <HelpCircle className="w-6 h-6" />
            Venue FAQ Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Venue Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">Select Venue</label>
              <Select value={selectedVenueId} onValueChange={setSelectedVenueId}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {venues.map(venue => (
                    <SelectItem key={venue.id} value={venue.id}>
                      {venue.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Add FAQ Button */}
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="w-full sm:w-auto"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add New FAQ
            </Button>

            {/* FAQs List */}
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-emerald-400"></div>
              </div>
            ) : faqs.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                No FAQs found for this venue. Add some to help your users!
              </div>
            ) : (
              <div className="space-y-4">
                {faqs.map(faq => (
                  <Card key={faq.id} className="bg-emerald-900/5 border-emerald-900/20">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-emerald-400">{faq.question}</h3>
                            <span className="text-xs px-2 py-1 rounded-full bg-emerald-900/20 text-emerald-400">
                              {CATEGORY_LABELS[faq.category]}
                            </span>
                          </div>
                          <p className="text-gray-300 text-sm whitespace-pre-wrap">{faq.answer}</p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setEditingFaq(faq);
                              setIsEditModalOpen(true);
                            }}
                          >
                            <Edit2 className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-400 hover:text-red-300"
                            onClick={() => handleDeleteFaq(faq.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Add FAQ Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New FAQ</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Category</label>
              <Select
                value={newFaq.category}
                onValueChange={(value: HelpCategory) => setNewFaq(prev => ({ ...prev, category: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(CATEGORY_LABELS).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Question *</label>
              <Input
                value={newFaq.question}
                onChange={(e) => setNewFaq(prev => ({ ...prev, question: e.target.value }))}
                placeholder="Enter the question"
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Answer *</label>
              <Textarea
                value={newFaq.answer}
                onChange={(e) => setNewFaq(prev => ({ ...prev, answer: e.target.value }))}
                placeholder="Enter the answer"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddFaq}>
              Add FAQ
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit FAQ Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit FAQ</DialogTitle>
          </DialogHeader>
          {editingFaq && (
            <>
              <div className="space-y-4 py-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Category</label>
                  <Select
                    value={editingFaq.category}
                    onValueChange={(value: HelpCategory) => setEditingFaq(prev => prev ? { ...prev, category: value } : null)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(CATEGORY_LABELS).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Question *</label>
                  <Input
                    value={editingFaq.question}
                    onChange={(e) => setEditingFaq(prev => prev ? { ...prev, question: e.target.value } : null)}
                    placeholder="Enter the question"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Answer *</label>
                  <Textarea
                    value={editingFaq.answer}
                    onChange={(e) => setEditingFaq(prev => prev ? { ...prev, answer: e.target.value } : null)}
                    placeholder="Enter the answer"
                    rows={4}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditFaq}>
                  Save Changes
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}; 
