import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { 
  Bell, 
  Ban, 
  Unlock, 
  Calendar, 
  Clock, 
  User, 
  X,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useRealTimeSlotUpdates } from '@/hooks/useRealTimeSlotUpdates';
import { useAuth } from '@/context/AuthContext';

interface SlotNotification {
  id: string;
  type: 'slot_blocked' | 'slot_unblocked' | 'slot_updated' | 'booking_created' | 'booking_cancelled';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  data?: any;
  severity: 'info' | 'warning' | 'success' | 'error';
}

interface SlotManagementNotificationsProps {
  venueIds?: string[];
  maxNotifications?: number;
  autoHideDelay?: number;
}

const SlotManagementNotifications: React.FC<SlotManagementNotificationsProps> = ({
  venueIds,
  maxNotifications = 50,
  autoHideDelay = 5000
}) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<SlotNotification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const addNotification = (notification: Omit<SlotNotification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: SlotNotification = {
      ...notification,
      id: `${Date.now()}-${Math.random()}`,
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev].slice(0, maxNotifications);
      return updated;
    });

    setUnreadCount(prev => prev + 1);

    // Auto-hide notification after delay
    if (autoHideDelay > 0) {
      setTimeout(() => {
        markAsRead(newNotification.id);
      }, autoHideDelay);
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read: true }
          : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
    setUnreadCount(0);
  };

  const clearNotifications = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  const removeNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
  };

  // Update unread count when notifications change
  useEffect(() => {
    const unread = notifications.filter(notif => !notif.read).length;
    setUnreadCount(unread);
  }, [notifications]);

  // Real-time slot updates
  useRealTimeSlotUpdates({
    venueIds,
    showNotifications: false, // We'll handle notifications manually
    onSlotBlocked: (slot) => {
      addNotification({
        type: 'slot_blocked',
        title: 'Slot Blocked',
        message: `Slot blocked for ${slot.date} at ${slot.start_time}-${slot.end_time}`,
        severity: 'warning',
        data: slot
      });
    },
    onSlotUnblocked: (slotId) => {
      addNotification({
        type: 'slot_unblocked',
        title: 'Slot Unblocked',
        message: `A blocked slot has been made available`,
        severity: 'success',
        data: { slotId }
      });
    },
    onSlotUpdated: (slot, oldSlot) => {
      addNotification({
        type: 'slot_updated',
        title: 'Slot Updated',
        message: `Blocked slot details have been updated`,
        severity: 'info',
        data: { slot, oldSlot }
      });
    },
    onBookingCreated: (booking) => {
      addNotification({
        type: 'booking_created',
        title: 'New Booking',
        message: `New booking created for ${booking.booking_date}`,
        severity: 'info',
        data: booking
      });
    },
    onBookingCancelled: (booking) => {
      addNotification({
        type: 'booking_cancelled',
        title: 'Booking Cancelled',
        message: `Booking cancelled for ${booking.booking_date}`,
        severity: 'warning',
        data: booking
      });
    }
  });

  const getNotificationIcon = (type: SlotNotification['type']) => {
    switch (type) {
      case 'slot_blocked':
        return <Ban className="h-4 w-4 text-red-500" />;
      case 'slot_unblocked':
        return <Unlock className="h-4 w-4 text-green-500" />;
      case 'slot_updated':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'booking_created':
        return <Calendar className="h-4 w-4 text-emerald-500" />;
      case 'booking_cancelled':
        return <X className="h-4 w-4 text-orange-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: SlotNotification['severity']) => {
    switch (severity) {
      case 'error':
        return 'bg-red-100 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-100 border-yellow-200 text-yellow-800';
      case 'success':
        return 'bg-green-100 border-green-200 text-green-800';
      case 'info':
      default:
        return 'bg-blue-100 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Notifications Panel */}
      {isOpen && (
        <Card className="absolute right-0 top-full mt-2 w-96 max-h-96 shadow-lg z-50 border">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Slot Management Notifications</CardTitle>
              <div className="flex gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    className="text-xs"
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearNotifications}
                  className="text-xs"
                >
                  Clear all
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-80">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No notifications</p>
                </div>
              ) : (
                <div className="space-y-1">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 border-l-4 hover:bg-gray-50 transition-colors ${
                        notification.read ? 'opacity-60' : ''
                      } ${getSeverityColor(notification.severity)}`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-sm">{notification.title}</h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeNotification(notification.id);
                              }}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                          <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                            <Clock className="h-3 w-3" />
                            {format(notification.timestamp, 'MMM dd, HH:mm')}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SlotManagementNotifications;
