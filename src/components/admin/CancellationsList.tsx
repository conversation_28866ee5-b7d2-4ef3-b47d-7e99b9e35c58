import React from 'react';
import { format, parseISO } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  User, 
  Phone, 
  DollarSign,
  FileText,
  Edit3,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { CancellationWithDetails } from '@/types/cancellation';

interface CancellationsListProps {
  cancellations: CancellationWithDetails[];
  loading: boolean;
  onRefundStatusUpdate: (cancellation: CancellationWithDetails) => void;
}

const CancellationsList: React.FC<CancellationsListProps> = ({
  cancellations,
  loading,
  onRefundStatusUpdate
}) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-sport-green"></div>
      </div>
    );
  }

  if (cancellations.length === 0) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Cancellations Found</h3>
            <p className="text-gray-600">There are no booking cancellations matching your criteria.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getRefundStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'processed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Processed</Badge>;
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'not_applicable':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Not Applicable</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Super Admin</Badge>;
      case 'admin':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Venue Admin</Badge>;
      default:
        return <Badge variant="secondary">{role}</Badge>;
    }
  };

  const formatTime = (timeString: string) => {
    return format(parseISO(`2000-01-01T${timeString}`), 'h:mm a');
  };

  const formatDate = (dateString: string) => {
    return format(parseISO(dateString), 'MMM dd, yyyy');
  };

  const formatDateTime = (dateTimeString: string) => {
    return format(parseISO(dateTimeString), 'MMM dd, yyyy h:mm a');
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-900">
          Cancellations ({cancellations.length})
        </h2>
      </div>

      <div className="space-y-4">
        {cancellations.map((cancellation) => (
          <Card key={cancellation.cancellation_id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Booking Details */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">Booking Details</h3>
                    {getRoleBadge(cancellation.cancelled_by_role)}
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{cancellation.venue_name}</span>
                      <span className="text-gray-500">• {cancellation.court_name}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{formatDate(cancellation.booking_date)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{formatTime(cancellation.start_time)} - {formatTime(cancellation.end_time)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{cancellation.customer_name}</span>
                    </div>
                    
                    {cancellation.customer_phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span>{cancellation.customer_phone}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Cancellation Details */}
                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900">Cancellation Details</h3>
                  
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-500">Cancelled on:</span>
                      <div className="font-medium">{formatDateTime(cancellation.cancelled_at)}</div>
                    </div>
                    
                    <div>
                      <span className="text-gray-500">Reason:</span>
                      <div className="font-medium bg-gray-50 p-2 rounded text-xs mt-1">
                        {cancellation.cancellation_reason}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-500">Booking Amount:</span>
                      <span className="font-medium">₹{cancellation.total_price.toLocaleString()}</span>
                    </div>
                    
                    {cancellation.payment_reference && (
                      <div className="text-xs text-gray-500">
                        Payment Ref: {cancellation.payment_reference}
                      </div>
                    )}
                  </div>
                </div>

                {/* Refund Details & Actions */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">Refund Status</h3>
                    {getRefundStatusBadge(cancellation.refund_status)}
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-500">Refund Amount:</span>
                      <span className="font-medium">
                        ₹{(cancellation.refund_amount || 0).toLocaleString()}
                      </span>
                    </div>
                    
                    {cancellation.refund_processed_at && (
                      <div>
                        <span className="text-gray-500">Processed on:</span>
                        <div className="font-medium text-xs">
                          {formatDateTime(cancellation.refund_processed_at)}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="pt-2">
                    <Button
                      onClick={() => onRefundStatusUpdate(cancellation)}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      <Edit3 className="h-4 w-4 mr-2" />
                      Update Refund Status
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default CancellationsList;
