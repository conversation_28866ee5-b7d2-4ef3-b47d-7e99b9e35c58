
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useForm } from "react-hook-form";
import { Calendar, Save, AlertTriangle, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from "react-hot-toast";

interface Settlement {
  settlement_id: string;
  venue_name: string;
  settlement_reference: string;
  status: 'pending' | 'processed' | 'settled';
  notes?: string;
  expected_settlement_date: string;
}

interface SettlementManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  settlement: Settlement | null;
  onUpdate: () => void;
}

interface UpdateForm {
  status: 'pending' | 'processed' | 'settled';
  notes: string;
  expected_settlement_date: string;
}

interface SettlementUpdateResult {
  success: boolean;
  error?: string;
  message?: string;
  settlement_id?: string;
  old_status?: string;
  new_status?: string;
  updated_by?: string;
}

const SettlementManagementModal: React.FC<SettlementManagementModalProps> = ({
  isOpen,
  onClose,
  settlement,
  onUpdate
}) => {
  const [loading, setLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingUpdate, setPendingUpdate] = useState<UpdateForm | null>(null);

  const form = useForm<UpdateForm>({
    defaultValues: {
      status: settlement?.status || 'pending',
      notes: settlement?.notes || '',
      expected_settlement_date: settlement?.expected_settlement_date || ''
    }
  });

  // Reset form when settlement changes
  React.useEffect(() => {
    if (settlement) {
      form.reset({
        status: settlement.status,
        notes: settlement.notes || '',
        expected_settlement_date: settlement.expected_settlement_date
      });
    }
  }, [settlement, form]);

  const handleSubmit = (data: UpdateForm) => {
    // If status is changing, show confirmation dialog
    if (data.status !== settlement?.status) {
      setPendingUpdate(data);
      setShowConfirmDialog(true);
    } else {
      // Just updating notes/date, no confirmation needed
      executeUpdate(data);
    }
  };

  const executeUpdate = async (data: UpdateForm) => {
    if (!settlement) return;

    setLoading(true);
    try {
      // Check current user authentication
      const { data: { user } } = await supabase.auth.getUser();
      console.log('🔐 Current authenticated user:', {
        userId: user?.id,
        email: user?.email
      });

      console.log('🔄 Updating settlement via database function:', {
        settlementId: settlement.settlement_id,
        newStatus: data.status,
        notes: data.notes,
        expectedDate: data.expected_settlement_date
      });

      // Call the database function that handles email triggers
      const { data: result, error } = await supabase.rpc('update_settlement_details', {
        p_settlement_id: settlement.settlement_id,
        p_new_status: data.status,
        p_notes: data.notes || null,
        p_expected_settlement_date: data.expected_settlement_date || null
      });

      console.log('📊 Settlement update result:', result);

      if (error) {
        console.error('❌ Error updating settlement:', error);
        throw error;
      }

      // Check if the function returned an error
      if (result && !result.success) {
        console.error('❌ Function returned error:', result.error);
        throw new Error(result.error);
      }

      // Show success message with email trigger info
      const successMessage = result?.email_triggered
        ? 'Settlement updated successfully! Email notification sent.'
        : 'Settlement updated successfully!';

      toast.success(successMessage);

      console.log('✅ Settlement update completed:', {
        success: true,
        oldStatus: result?.old_status,
        newStatus: result?.new_status,
        emailTriggered: result?.email_triggered,
        updatedBy: result?.updated_by
      });

      onUpdate();
      onClose();

    } catch (error) {
      console.error('❌ Error updating settlement:', error);
      const errorMessage = error.message || 'Failed to update settlement';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      setShowConfirmDialog(false);
      setPendingUpdate(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600';
      case 'processed': return 'text-blue-600';
      case 'settled': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'pending': return 'Settlement is waiting for processing';
      case 'processed': return 'Settlement has been processed and is ready for payout';
      case 'settled': return 'Settlement has been completed and funds transferred';
      default: return '';
    }
  };

  if (!settlement) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="bg-navy-dark border-white/20 text-white max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle className="text-emerald-300 flex items-center">
              <Save className="w-5 h-5 mr-2" />
              Manage Settlement
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Settlement Info */}
            <div className="bg-black/40 rounded-lg p-3 space-y-2">
              <div className="text-sm text-gray-300">
                <strong>Venue:</strong> {settlement.venue_name}
              </div>
              <div className="text-sm text-gray-300">
                <strong>Reference:</strong> {settlement.settlement_reference}
              </div>
              <div className="text-sm text-gray-300">
                <strong>Current Status:</strong> 
                <span className={`ml-1 font-semibold ${getStatusColor(settlement.status)}`}>
                  {settlement.status.charAt(0).toUpperCase() + settlement.status.slice(1)}
                </span>
              </div>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                {/* Status Selection */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Settlement Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="bg-black/40 border-white/20 text-white">
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-navy-dark border-white/20">
                          <SelectItem value="pending" className="text-white">
                            <div className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                              Pending
                            </div>
                          </SelectItem>
                          <SelectItem value="processed" className="text-white">
                            <div className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                              Processed
                            </div>
                          </SelectItem>
                          <SelectItem value="settled" className="text-white">
                            <div className="flex items-center">
                              <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                              Settled
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <div className="text-xs text-gray-400">
                        {getStatusDescription(form.watch('status'))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Expected Settlement Date */}
                <FormField
                  control={form.control}
                  name="expected_settlement_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        Expected Settlement Date
                      </FormLabel>
                      <FormControl>
                        <input
                          type="date"
                          {...field}
                          className="w-full px-3 py-2 bg-black/40 border border-white/20 rounded-md text-white focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Admin Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Admin Notes</FormLabel>
                      <FormControl>
                        <textarea
                          {...field}
                          placeholder="Add notes about this settlement..."
                          rows={3}
                          className="w-full px-3 py-2 bg-black/40 border border-white/20 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Action Buttons */}
                <div className="flex space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    className="flex-1 border-white/20 text-white hover:bg-white/10"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading}
                    className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    {loading ? 'Updating...' : 'Update Settlement'}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent className="bg-navy-dark border-white/20 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-yellow-300">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Confirm Status Change
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-300">
              You are about to change the settlement status from{' '}
              <span className={`font-semibold ${getStatusColor(settlement.status)}`}>
                {settlement.status}
              </span>{' '}
              to{' '}
              <span className={`font-semibold ${getStatusColor(pendingUpdate?.status || '')}`}>
                {pendingUpdate?.status}
              </span>
              .
              <br /><br />
              This action will update the settlement record and may trigger payout processes. Are you sure you want to continue?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-600 hover:bg-gray-700 text-white border-0">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => pendingUpdate && executeUpdate(pendingUpdate)}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Confirm Update
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default SettlementManagementModal;
