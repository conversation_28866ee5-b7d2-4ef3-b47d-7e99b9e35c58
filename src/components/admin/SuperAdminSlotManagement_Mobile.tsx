import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { format, addDays } from 'date-fns';
import { 
  Calendar as CalendarIcon, 
  Filter, 
  Ban, 
  Unlock, 
  Search,
  Building,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ChevronDown,
  ChevronUp,
  Plus,
  Minus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface Venue {
  id: string;
  name: string;
  location: string;
  courts: Court[];
}

interface Court {
  id: string;
  name: string;
  venue_id: string;
  court_group_id?: string;
}

interface BlockedSlot {
  id: string;
  court_id: string;
  court_name: string;
  venue_id: string;
  venue_name: string;
  date: string;
  start_time: string;
  end_time: string;
  reason: string;
  created_by: string;
  created_by_name: string;
  created_at: string;
}

interface SlotManagementStats {
  total_blocked_slots: number;
  venues_with_blocks: number;
  courts_with_blocks: number;
}

const SuperAdminSlotManagement_Mobile: React.FC = () => {
  const { user } = useAuth();
  const [venues, setVenues] = useState<Venue[]>([]);
  const [blockedSlots, setBlockedSlots] = useState<BlockedSlot[]>([]);
  const [stats, setStats] = useState<SlotManagementStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeView, setActiveView] = useState<'overview' | 'manage' | 'bulk'>('overview');
  
  // Filters and search
  const [selectedVenueId, setSelectedVenueId] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    start: new Date(),
    end: addDays(new Date(), 7)
  });
  
  // Bulk operations
  const [selectedCourtIds, setSelectedCourtIds] = useState<string[]>([]);
  const [timeRange, setTimeRange] = useState({
    start: '09:00',
    end: '10:00'
  });
  const [blockReason, setBlockReason] = useState('');
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [bulkOperationLoading, setBulkOperationLoading] = useState(false);
  
  // UI state
  const [expandedSlots, setExpandedSlots] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [showBulkForm, setShowBulkForm] = useState(false);

  useEffect(() => {
    fetchVenues();
    fetchBlockedSlots();
    fetchStats();
  }, []);

  useEffect(() => {
    fetchBlockedSlots();
    fetchStats();
  }, [searchTerm, selectedVenueId]);

  const fetchVenues = async () => {
    try {
      const { data, error } = await supabase
        .from('venues')
        .select(`
          id,
          name,
          location,
          courts (
            id,
            name,
            venue_id,
            court_group_id
          )
        `)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setVenues(data || []);
    } catch (error) {
      console.error('Error fetching venues:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch venues',
        variant: 'destructive'
      });
    }
  };

  const fetchBlockedSlots = async () => {
    try {
      const { data, error } = await supabase.rpc('get_blocked_slots_with_details', {
        p_venue_id: selectedVenueId || null,
        p_start_date: format(dateRange.start, 'yyyy-MM-dd'),
        p_end_date: format(dateRange.end, 'yyyy-MM-dd')
      });

      if (error) throw error;
      
      let filteredData = data || [];
      
      // Apply search filter
      if (searchTerm) {
        filteredData = filteredData.filter((slot: BlockedSlot) =>
          slot.venue_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          slot.court_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          slot.reason?.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      setBlockedSlots(filteredData);
    } catch (error) {
      console.error('Error fetching blocked slots:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch blocked slots',
        variant: 'destructive'
      });
    }
  };

  const fetchStats = async () => {
    try {
      const { data, error } = await supabase.rpc('get_slot_management_stats', {
        p_venue_id: selectedVenueId || null,
        p_start_date: format(dateRange.start, 'yyyy-MM-dd'),
        p_end_date: format(dateRange.end, 'yyyy-MM-dd')
      });

      if (error) throw error;
      
      if (data?.success) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkBlock = async () => {
    if (!selectedVenueId || selectedCourtIds.length === 0) {
      toast({
        title: 'Missing Information',
        description: 'Please select venue and courts',
        variant: 'destructive'
      });
      return;
    }

    setBulkOperationLoading(true);
    try {
      const { data, error } = await supabase.rpc('bulk_block_slots', {
        p_court_ids: selectedCourtIds,
        p_start_date: format(dateRange.start, 'yyyy-MM-dd'),
        p_end_date: format(dateRange.end, 'yyyy-MM-dd'),
        p_start_time: timeRange.start,
        p_end_time: timeRange.end,
        p_reason: blockReason || 'Bulk blocked by super admin',
        p_created_by: user?.id
      });

      if (error) throw error;

      if (data?.success) {
        toast({
          title: 'Success',
          description: data.message,
          variant: 'default'
        });
        
        // Reset form
        setSelectedCourtIds([]);
        setBlockReason('');
        setShowBulkForm(false);
        
        // Refresh data
        fetchBlockedSlots();
        fetchStats();
      } else {
        throw new Error(data?.error || 'Unknown error');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to block slots',
        variant: 'destructive'
      });
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const handleBulkUnblock = async () => {
    if (selectedSlots.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select slots to unblock',
        variant: 'destructive'
      });
      return;
    }

    setBulkOperationLoading(true);
    try {
      const { data, error } = await supabase.rpc('unblock_slots', {
        p_blocked_slot_ids: selectedSlots
      });

      if (error) throw error;

      if (data?.success) {
        toast({
          title: 'Success',
          description: data.message,
          variant: 'default'
        });
        
        setSelectedSlots([]);
        fetchBlockedSlots();
        fetchStats();
      } else {
        throw new Error(data?.error || 'Unknown error');
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to unblock slots',
        variant: 'destructive'
      });
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const toggleSlotExpansion = (slotId: string) => {
    const newExpanded = new Set(expandedSlots);
    if (newExpanded.has(slotId)) {
      newExpanded.delete(slotId);
    } else {
      newExpanded.add(slotId);
    }
    setExpandedSlots(newExpanded);
  };

  const selectedVenue = venues.find(v => v.id === selectedVenueId);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4 bg-navy-dark min-h-screen">
      {/* Header */}
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Super Admin Slot Management</h1>
        <p className="text-gray-300 text-sm">Manage slot availability across all venues</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <Card className="bg-gradient-to-br from-red-600 to-red-500 border-red-400">
          <CardContent className="p-4 text-center">
            <Ban className="h-6 w-6 text-white mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">{stats?.total_blocked_slots || 0}</div>
            <div className="text-xs text-red-100">Blocked Slots</div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-orange-600 to-orange-500 border-orange-400">
          <CardContent className="p-4 text-center">
            <Building className="h-6 w-6 text-white mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">{stats?.venues_with_blocks || 0}</div>
            <div className="text-xs text-orange-100">Venues Affected</div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <div className="flex bg-navy-light rounded-lg p-1 mb-4">
        <button
          onClick={() => setActiveView('overview')}
          className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
            activeView === 'overview'
              ? 'bg-emerald-600 text-white'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          Overview
        </button>
        <button
          onClick={() => setActiveView('manage')}
          className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
            activeView === 'manage'
              ? 'bg-emerald-600 text-white'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          Manage
        </button>
        <button
          onClick={() => setActiveView('bulk')}
          className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
            activeView === 'bulk'
              ? 'bg-emerald-600 text-white'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          Bulk
        </button>
      </div>

      {/* Overview View */}
      {activeView === 'overview' && (
        <div className="space-y-4">
          {/* Quick Actions */}
          <Card className="bg-navy-light border-navy-600">
            <CardHeader>
              <CardTitle className="text-white text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                onClick={() => setActiveView('bulk')}
                className="w-full bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white"
              >
                <Ban className="h-4 w-4 mr-2" />
                Bulk Block Slots
              </Button>
              <Button
                onClick={() => setActiveView('manage')}
                variant="outline"
                className="w-full border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white"
              >
                <Search className="h-4 w-4 mr-2" />
                Manage Existing Blocks
              </Button>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card className="bg-navy-light border-navy-600">
            <CardHeader>
              <CardTitle className="text-white text-lg">System Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-300 text-sm">Slot Blocking</span>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300 text-sm">Real-time Updates</span>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Enabled
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300 text-sm">Bulk Operations</span>
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Available
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Manage View */}
      {activeView === 'manage' && (
        <div className="space-y-4">
          {/* Search and Filters */}
          <Card className="bg-navy-light border-navy-600">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Search & Filter</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="text-emerald-400"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {showFilters ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search venues, courts, reasons..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-navy-dark border-navy-500 text-white"
                />
              </div>

              {showFilters && (
                <div className="space-y-3 pt-3 border-t border-navy-500">
                  <div>
                    <Label className="text-gray-300 text-sm">Venue</Label>
                    <Select value={selectedVenueId} onValueChange={setSelectedVenueId}>
                      <SelectTrigger className="bg-navy-dark border-navy-500 text-white">
                        <SelectValue placeholder="All venues" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All venues</SelectItem>
                        {venues.map(venue => (
                          <SelectItem key={venue.id} value={venue.id}>
                            {venue.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label className="text-gray-300 text-sm">Start Date</Label>
                      <Input
                        type="date"
                        value={format(dateRange.start, 'yyyy-MM-dd')}
                        onChange={(e) => setDateRange(prev => ({ ...prev, start: new Date(e.target.value) }))}
                        className="bg-navy-dark border-navy-500 text-white"
                      />
                    </div>
                    <div>
                      <Label className="text-gray-300 text-sm">End Date</Label>
                      <Input
                        type="date"
                        value={format(dateRange.end, 'yyyy-MM-dd')}
                        onChange={(e) => setDateRange(prev => ({ ...prev, end: new Date(e.target.value) }))}
                        className="bg-navy-dark border-navy-500 text-white"
                      />
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedVenueId('');
                    }}
                    className="w-full border-emerald-500 text-emerald-400"
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Blocked Slots List */}
          <Card className="bg-navy-light border-navy-600">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">
                  Blocked Slots ({blockedSlots.length})
                </CardTitle>
                {selectedSlots.length > 0 && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={handleBulkUnblock}
                    disabled={bulkOperationLoading}
                  >
                    <Unlock className="h-4 w-4 mr-1" />
                    Unblock ({selectedSlots.length})
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {blockedSlots.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  <Ban className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No blocked slots found</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {blockedSlots.map((slot) => (
                    <div
                      key={slot.id}
                      className="bg-navy-dark rounded-lg border border-navy-500 overflow-hidden"
                    >
                      <div className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3 flex-1">
                            <Checkbox
                              checked={selectedSlots.includes(slot.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedSlots(prev => [...prev, slot.id]);
                                } else {
                                  setSelectedSlots(prev => prev.filter(id => id !== slot.id));
                                }
                              }}
                              className="mt-1"
                            />
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-white text-sm">{slot.venue_name}</div>
                              <div className="text-gray-300 text-xs">{slot.court_name}</div>
                              <div className="text-emerald-400 text-xs mt-1">
                                {format(new Date(slot.date), 'MMM dd, yyyy')} • {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleSlotExpansion(slot.id)}
                            className="text-gray-400 hover:text-white"
                          >
                            {expandedSlots.has(slot.id) ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </Button>
                        </div>

                        {expandedSlots.has(slot.id) && (
                          <div className="mt-3 pt-3 border-t border-navy-500 space-y-2">
                            <div>
                              <span className="text-gray-400 text-xs">Reason:</span>
                              <p className="text-white text-sm">{slot.reason || 'No reason provided'}</p>
                            </div>
                            <div>
                              <span className="text-gray-400 text-xs">Created by:</span>
                              <p className="text-white text-sm">{slot.created_by_name || 'Unknown'}</p>
                            </div>
                            <div>
                              <span className="text-gray-400 text-xs">Created at:</span>
                              <p className="text-white text-sm">{format(new Date(slot.created_at), 'MMM dd, yyyy HH:mm')}</p>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleBulkUnblock()}
                              className="w-full mt-3 border-red-500 text-red-400 hover:bg-red-500 hover:text-white"
                            >
                              <Unlock className="h-4 w-4 mr-2" />
                              Unblock This Slot
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Bulk Operations View */}
      {activeView === 'bulk' && (
        <div className="space-y-4">
          {/* Bulk Form Toggle */}
          <Card className="bg-navy-light border-navy-600">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Bulk Slot Blocking</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowBulkForm(!showBulkForm)}
                  className="text-emerald-400"
                >
                  {showBulkForm ? <Minus className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                </Button>
              </div>
            </CardHeader>
            {showBulkForm && (
              <CardContent className="space-y-4">
                {/* Venue Selection */}
                <div>
                  <Label className="text-gray-300 text-sm">Select Venue</Label>
                  <Select value={selectedVenueId} onValueChange={setSelectedVenueId}>
                    <SelectTrigger className="bg-navy-dark border-navy-500 text-white">
                      <SelectValue placeholder="Choose a venue" />
                    </SelectTrigger>
                    <SelectContent>
                      {venues.map(venue => (
                        <SelectItem key={venue.id} value={venue.id}>
                          {venue.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Court Selection */}
                {selectedVenue && (
                  <div>
                    <Label className="text-gray-300 text-sm">Select Courts</Label>
                    <div className="bg-navy-dark rounded-lg border border-navy-500 p-3 max-h-40 overflow-y-auto">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={selectedCourtIds.length === selectedVenue.courts.length}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCourtIds(selectedVenue.courts.map(c => c.id));
                              } else {
                                setSelectedCourtIds([]);
                              }
                            }}
                          />
                          <Label className="text-white text-sm font-medium">Select All Courts</Label>
                        </div>
                        {selectedVenue.courts.map(court => (
                          <div key={court.id} className="flex items-center gap-2">
                            <Checkbox
                              checked={selectedCourtIds.includes(court.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedCourtIds(prev => [...prev, court.id]);
                                } else {
                                  setSelectedCourtIds(prev => prev.filter(id => id !== court.id));
                                }
                              }}
                            />
                            <Label className="text-gray-300 text-sm">{court.name}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                    <p className="text-xs text-gray-400 mt-2">
                      {selectedCourtIds.length} of {selectedVenue.courts.length} courts selected
                    </p>
                  </div>
                )}

                {/* Date Range */}
                <div>
                  <Label className="text-gray-300 text-sm">Date Range</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Input
                        type="date"
                        value={format(dateRange.start, 'yyyy-MM-dd')}
                        onChange={(e) => setDateRange(prev => ({ ...prev, start: new Date(e.target.value) }))}
                        className="bg-navy-dark border-navy-500 text-white"
                      />
                    </div>
                    <div>
                      <Input
                        type="date"
                        value={format(dateRange.end, 'yyyy-MM-dd')}
                        onChange={(e) => setDateRange(prev => ({ ...prev, end: new Date(e.target.value) }))}
                        className="bg-navy-dark border-navy-500 text-white"
                      />
                    </div>
                  </div>
                </div>

                {/* Time Range */}
                <div>
                  <Label className="text-gray-300 text-sm">Time Range</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Input
                        type="time"
                        value={timeRange.start}
                        onChange={(e) => setTimeRange(prev => ({ ...prev, start: e.target.value }))}
                        className="bg-navy-dark border-navy-500 text-white"
                      />
                    </div>
                    <div>
                      <Input
                        type="time"
                        value={timeRange.end}
                        onChange={(e) => setTimeRange(prev => ({ ...prev, end: e.target.value }))}
                        className="bg-navy-dark border-navy-500 text-white"
                      />
                    </div>
                  </div>
                </div>

                {/* Reason */}
                <div>
                  <Label className="text-gray-300 text-sm">Reason for Blocking</Label>
                  <Textarea
                    placeholder="Enter reason for blocking these slots..."
                    value={blockReason}
                    onChange={(e) => setBlockReason(e.target.value)}
                    rows={3}
                    className="bg-navy-dark border-navy-500 text-white"
                  />
                </div>

                {/* Preview */}
                <div className="bg-yellow-900/30 border border-yellow-600/50 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <h4 className="font-medium text-yellow-400 mb-2">Bulk Operation Preview</h4>
                      <div className="text-yellow-300 space-y-1">
                        <p><strong>Venue:</strong> {selectedVenue?.name || 'None selected'}</p>
                        <p><strong>Courts:</strong> {selectedCourtIds.length} selected</p>
                        <p><strong>Date Range:</strong> {format(dateRange.start, 'MMM dd')} - {format(dateRange.end, 'MMM dd')}</p>
                        <p><strong>Time:</strong> {timeRange.start} - {timeRange.end}</p>
                        <p><strong>Estimated Slots:</strong> {
                          selectedCourtIds.length *
                          Math.ceil((new Date(dateRange.end).getTime() - new Date(dateRange.start).getTime()) / (1000 * 60 * 60 * 24)) + 1
                        }</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <Button
                  onClick={handleBulkBlock}
                  disabled={!selectedVenueId || selectedCourtIds.length === 0 || bulkOperationLoading}
                  className="w-full bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white"
                  size="lg"
                >
                  {bulkOperationLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Blocking Slots...
                    </>
                  ) : (
                    <>
                      <Ban className="h-4 w-4 mr-2" />
                      Block Selected Slots
                    </>
                  )}
                </Button>
              </CardContent>
            )}
          </Card>

          {/* Quick Actions */}
          <Card className="bg-navy-light border-navy-600">
            <CardHeader>
              <CardTitle className="text-white text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                onClick={() => {
                  setDateRange({
                    start: new Date(),
                    end: addDays(new Date(), 7)
                  });
                }}
                variant="outline"
                className="w-full border-emerald-500 text-emerald-400 hover:bg-emerald-500 hover:text-white"
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Set This Week
              </Button>
              <Button
                onClick={() => {
                  setSelectedCourtIds([]);
                  setBlockReason('');
                  setSelectedVenueId('');
                }}
                variant="outline"
                className="w-full border-gray-500 text-gray-400 hover:bg-gray-500 hover:text-white"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Clear Form
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default SuperAdminSlotManagement_Mobile;
