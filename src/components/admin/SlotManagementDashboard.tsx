import React, { useState, useEffect } from 'react';
import { format, subDays, addDays } from 'date-fns';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  Ban, 
  Building, 
  MapPin, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Users,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useSlotManagement } from '@/hooks/useSlotManagement';
import SlotManagementService from '@/services/slotManagementService';

interface DashboardStats {
  totalBlocked: number;
  venuesAffected: number;
  courtsAffected: number;
  todayBlocked: number;
  weeklyTrend: number;
  topReasons: Array<{ reason: string; count: number }>;
  blockingTrend: Array<{ date: string; count: number }>;
  venueBreakdown: Array<{ venue: string; count: number; percentage: number }>;
  timeDistribution: Array<{ hour: number; count: number }>;
}

interface SlotManagementDashboardProps {
  className?: string;
}

const COLORS = ['#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#f97316'];

const SlotManagementDashboard: React.FC<SlotManagementDashboardProps> = ({ className }) => {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: subDays(new Date(), 30),
    end: new Date()
  });

  const { stats, fetchStats } = useSlotManagement();

  useEffect(() => {
    fetchDashboardData();
  }, [dateRange]);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // Fetch comprehensive stats
      await fetchStats(
        undefined,
        format(dateRange.start, 'yyyy-MM-dd'),
        format(dateRange.end, 'yyyy-MM-dd')
      );

      // Fetch additional dashboard-specific data
      const [
        blockedSlotsData,
        venueStatsData,
        trendData
      ] = await Promise.all([
        SlotManagementService.getFilteredBlockedSlots({
          start_date: format(dateRange.start, 'yyyy-MM-dd'),
          end_date: format(dateRange.end, 'yyyy-MM-dd'),
          limit: 1000
        }),
        SlotManagementService.getVenuesWithCourts(),
        fetchBlockingTrend()
      ]);

      if (blockedSlotsData.data) {
        const processedStats = processBlockedSlotsData(blockedSlotsData.data);
        setDashboardStats(processedStats);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBlockingTrend = async () => {
    // Fetch daily blocking trend for the last 30 days
    const trendData = [];
    for (let i = 29; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const { data } = await SlotManagementService.getFilteredBlockedSlots({
        start_date: format(date, 'yyyy-MM-dd'),
        end_date: format(date, 'yyyy-MM-dd'),
        limit: 1000
      });
      
      trendData.push({
        date: format(date, 'MMM dd'),
        count: data?.length || 0
      });
    }
    return trendData;
  };

  const processBlockedSlotsData = (slots: any[]): DashboardStats => {
    const today = format(new Date(), 'yyyy-MM-dd');
    const todaySlots = slots.filter(slot => slot.date === today);
    
    // Calculate weekly trend
    const lastWeek = subDays(new Date(), 7);
    const thisWeekSlots = slots.filter(slot => new Date(slot.date) >= lastWeek);
    const prevWeekSlots = slots.filter(slot => {
      const slotDate = new Date(slot.date);
      return slotDate >= subDays(lastWeek, 7) && slotDate < lastWeek;
    });
    
    const weeklyTrend = prevWeekSlots.length > 0 
      ? ((thisWeekSlots.length - prevWeekSlots.length) / prevWeekSlots.length) * 100
      : 0;

    // Top reasons
    const reasonCounts: Record<string, number> = {};
    slots.forEach(slot => {
      const reason = slot.reason || 'No reason provided';
      reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
    });
    
    const topReasons = Object.entries(reasonCounts)
      .map(([reason, count]) => ({ reason, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Venue breakdown
    const venueCounts: Record<string, number> = {};
    slots.forEach(slot => {
      venueCounts[slot.venue_name] = (venueCounts[slot.venue_name] || 0) + 1;
    });
    
    const venueBreakdown = Object.entries(venueCounts)
      .map(([venue, count]) => ({
        venue,
        count,
        percentage: (count / slots.length) * 100
      }))
      .sort((a, b) => b.count - a.count);

    // Time distribution
    const hourCounts: Record<number, number> = {};
    slots.forEach(slot => {
      const hour = parseInt(slot.start_time.split(':')[0]);
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });
    
    const timeDistribution = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      count: hourCounts[hour] || 0
    }));

    return {
      totalBlocked: slots.length,
      venuesAffected: new Set(slots.map(slot => slot.venue_id)).size,
      courtsAffected: new Set(slots.map(slot => slot.court_id)).size,
      todayBlocked: todaySlots.length,
      weeklyTrend,
      topReasons,
      blockingTrend: [], // Will be populated by fetchBlockingTrend
      venueBreakdown,
      timeDistribution
    };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Slot Management Dashboard</h2>
          <p className="text-gray-600">
            Overview for {format(dateRange.start, 'MMM dd')} - {format(dateRange.end, 'MMM dd, yyyy')}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setDateRange({
              start: subDays(new Date(), 7),
              end: new Date()
            })}
          >
            Last 7 Days
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setDateRange({
              start: subDays(new Date(), 30),
              end: new Date()
            })}
          >
            Last 30 Days
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Blocked Slots</CardTitle>
            <Ban className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalBlocked || 0}</div>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <Calendar className="h-3 w-3 mr-1" />
              {dashboardStats?.todayBlocked || 0} today
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Venues Affected</CardTitle>
            <Building className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.venuesAffected || 0}</div>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <MapPin className="h-3 w-3 mr-1" />
              {dashboardStats?.courtsAffected || 0} courts total
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Trend</CardTitle>
            {(dashboardStats?.weeklyTrend || 0) >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardStats?.weeklyTrend ? `${dashboardStats.weeklyTrend.toFixed(1)}%` : '0%'}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              vs previous week
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Active</div>
            <div className="text-xs text-gray-500 mt-1">
              All systems operational
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Reasons Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Top Blocking Reasons</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={dashboardStats?.topReasons || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="reason" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Venue Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Venue Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={dashboardStats?.venueBreakdown || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ venue, percentage }) => `${venue}: ${percentage.toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {(dashboardStats?.venueBreakdown || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Time Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Blocking Time Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={dashboardStats?.timeDistribution || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="hour"
                  tickFormatter={(hour) => `${hour}:00`}
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(hour) => `${hour}:00 - ${hour + 1}:00`}
                  formatter={(value) => [value, 'Blocked Slots']}
                />
                <Area
                  type="monotone"
                  dataKey="count"
                  stroke="#10b981"
                  fill="#10b981"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => window.location.href = '/admin/super-slot-management'}
              >
                <Ban className="h-6 w-6 mb-2 text-red-500" />
                <span className="text-sm">Block Slots</span>
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => fetchDashboardData()}
              >
                <Activity className="h-6 w-6 mb-2 text-blue-500" />
                <span className="text-sm">Refresh Data</span>
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  // Export functionality
                  console.log('Export dashboard data');
                }}
              >
                <TrendingUp className="h-6 w-6 mb-2 text-green-500" />
                <span className="text-sm">Export Report</span>
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => {
                  setDateRange({
                    start: subDays(new Date(), 90),
                    end: new Date()
                  });
                }}
              >
                <Calendar className="h-6 w-6 mb-2 text-purple-500" />
                <span className="text-sm">90 Day View</span>
              </Button>
            </div>

            {/* System Health Indicators */}
            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-3">System Health</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Database Performance</span>
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Excellent
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">API Response Time</span>
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    &lt; 200ms
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Real-time Updates</span>
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Most Blocked Venue</CardTitle>
          </CardHeader>
          <CardContent>
            {dashboardStats?.venueBreakdown?.[0] ? (
              <div>
                <div className="text-xl font-bold text-gray-900">
                  {dashboardStats.venueBreakdown[0].venue}
                </div>
                <div className="text-sm text-gray-500">
                  {dashboardStats.venueBreakdown[0].count} slots ({dashboardStats.venueBreakdown[0].percentage.toFixed(1)}%)
                </div>
              </div>
            ) : (
              <div className="text-gray-500">No data available</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Peak Blocking Hours</CardTitle>
          </CardHeader>
          <CardContent>
            {dashboardStats?.timeDistribution ? (
              <div>
                {(() => {
                  const peakHour = dashboardStats.timeDistribution.reduce((max, current) =>
                    current.count > max.count ? current : max
                  );
                  return (
                    <div>
                      <div className="text-xl font-bold text-gray-900">
                        {peakHour.hour}:00 - {peakHour.hour + 1}:00
                      </div>
                      <div className="text-sm text-gray-500">
                        {peakHour.count} slots blocked
                      </div>
                    </div>
                  );
                })()}
              </div>
            ) : (
              <div className="text-gray-500">No data available</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Top Blocking Reason</CardTitle>
          </CardHeader>
          <CardContent>
            {dashboardStats?.topReasons?.[0] ? (
              <div>
                <div className="text-xl font-bold text-gray-900">
                  {dashboardStats.topReasons[0].reason}
                </div>
                <div className="text-sm text-gray-500">
                  {dashboardStats.topReasons[0].count} occurrences
                </div>
              </div>
            ) : (
              <div className="text-gray-500">No data available</div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SlotManagementDashboard;
