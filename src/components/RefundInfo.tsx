import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock,
  DollarSign,
  FileText 
} from 'lucide-react';

interface CancellationInfo {
  id: string;
  refund_status: string;
  refund_notes: string | null;
  refund_amount: number | null;
  refund_processed_at: string | null;
  cancellation_reason: string;
}

interface RefundInfoProps {
  cancellation: CancellationInfo | null;
  bookingAmount: number;
  className?: string;
}

const RefundInfo: React.FC<RefundInfoProps> = ({ 
  cancellation, 
  bookingAmount, 
  className = "" 
}) => {
  // Don't show refund info if no cancellation or status is still pending
  if (!cancellation || cancellation.refund_status === 'pending') {
    return null;
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'not_applicable':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'not_applicable':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'processed':
        return 'Refund Processed';
      case 'rejected':
        return 'Refund Rejected';
      case 'not_applicable':
        return 'Refund Not Applicable';
      default:
        return 'Unknown Status';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return `₹${amount.toFixed(2)}`;
  };

  return (
    <Card className={`border-l-4 border-l-blue-500 ${className}`}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-gray-900 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Refund Information
            </h4>
            <Badge 
              variant="outline" 
              className={`${getStatusColor(cancellation.refund_status)} flex items-center gap-1`}
            >
              {getStatusIcon(cancellation.refund_status)}
              {getStatusText(cancellation.refund_status)}
            </Badge>
          </div>

          {/* Refund Details */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
            <div>
              <span className="text-gray-600">Original Amount:</span>
              <div className="font-medium">{formatAmount(bookingAmount)}</div>
            </div>
            
            {cancellation.refund_amount !== null && (
              <div>
                <span className="text-gray-600">Refund Amount:</span>
                <div className="font-medium">{formatAmount(cancellation.refund_amount)}</div>
              </div>
            )}
            
            {cancellation.refund_processed_at && (
              <div>
                <span className="text-gray-600">Processed On:</span>
                <div className="font-medium">{formatDate(cancellation.refund_processed_at)}</div>
              </div>
            )}
          </div>

          {/* Cancellation Reason */}
          <div>
            <span className="text-gray-600 text-sm">Cancellation Reason:</span>
            <div className="text-sm font-medium mt-1">{cancellation.cancellation_reason}</div>
          </div>

          {/* Refund Notes */}
          {cancellation.refund_notes && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <FileText className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                <div>
                  <span className="text-gray-600 text-sm font-medium">Admin Notes:</span>
                  <div className="text-sm text-gray-700 mt-1">{cancellation.refund_notes}</div>
                </div>
              </div>
            </div>
          )}

          {/* Status-specific messages */}
          {cancellation.refund_status === 'processed' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-green-800 text-sm">
                <CheckCircle className="h-4 w-4" />
                <span className="font-medium">
                  Your refund has been processed successfully. Please allow 3-5 business days for the amount to reflect in your account.
                </span>
              </div>
            </div>
          )}

          {cancellation.refund_status === 'rejected' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-red-800 text-sm">
                <XCircle className="h-4 w-4" />
                <span className="font-medium">
                  Your refund request has been reviewed and rejected. Please check the admin notes above for details.
                </span>
              </div>
            </div>
          )}

          {cancellation.refund_status === 'not_applicable' && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-yellow-800 text-sm">
                <AlertCircle className="h-4 w-4" />
                <span className="font-medium">
                  Based on our refund policy, this booking is not eligible for a refund.
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RefundInfo;
