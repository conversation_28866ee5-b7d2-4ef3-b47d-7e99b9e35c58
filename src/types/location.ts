
export interface Coordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface Address {
  street?: string;
  area?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  display_name: string;
}

export type LocationSource = 'gps' | 'ip' | 'manual' | 'cache';

export interface LocationData {
  coordinates: Coordinates;
  address: Address;
  source: LocationSource;
  timestamp: number;
  accuracy?: number;
}

export interface LocationState {
  data: LocationData | null;
  isLoading: boolean;
  error: string | null;
  hasPermission: boolean | null;
  isRefreshing: boolean;
}

export interface GeocodingResponse {
  display_name: string;
  address: {
    road?: string;
    suburb?: string;
    city?: string;
    state?: string;
    country?: string;
    postcode?: string;
  };
  lat: string;
  lon: string;
}

export interface IPLocationResponse {
  latitude: number;
  longitude: number;
  city: string;
  region: string;
  country_name: string;
  accuracy?: number;
}
