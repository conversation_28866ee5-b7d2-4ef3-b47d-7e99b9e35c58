
/**
 * Cancellation and Refund Types
 * For the new cancellations audit system
 */

export interface CancellationRecord {
  id: string;
  booking_id: string;
  cancelled_by_user_id: string;
  cancelled_by_role: 'admin' | 'super_admin';
  cancellation_reason: string;
  original_booking_data: any; // JSONB data
  refund_status: 'pending' | 'processed' | 'rejected' | 'not_applicable';
  refund_amount: number | null;
  admin_refund_amount: number | null; // Admin-determined refund amount
  refund_processed_by: string | null;
  refund_processed_at: string | null;
  refund_notes: string | null;
  razorpay_refund_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface CancellationWithDetails {
  cancellation_id: string;
  booking_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  venue_name: string;
  court_name: string;
  customer_name: string;
  customer_phone: string | null;
  total_price: number;
  cancellation_reason: string;
  cancelled_by_role: string; // Keep as string to handle DB values
  cancelled_at: string;
  refund_status: string; // Change to string to handle DB values
  refund_amount: number | null;
  admin_refund_amount: number | null; // Admin-determined refund amount
  refund_processed_at: string | null;
  payment_reference: string | null;
}

export interface RefundStatusUpdate {
  cancellation_id: string;
  new_status: 'pending' | 'processed' | 'rejected' | 'not_applicable';
  refund_notes?: string;
  razorpay_refund_id?: string;
  admin_refund_amount?: number; // Admin-determined refund amount
}

export interface CancellationFilters {
  venue_id?: string;
  refund_status?: 'pending' | 'processed' | 'rejected' | 'not_applicable' | 'all';
  cancelled_by_role?: 'admin' | 'super_admin' | 'all';
  date_from?: string;
  date_to?: string;
  search_query?: string;
}

export interface CancellationStats {
  total_cancellations: number;
  pending_refunds: number;
  processed_refunds: number;
  total_refund_amount: number;
  avg_refund_amount: number;
  cancellations_by_role: {
    admin: number;
    super_admin: number;
  };
}
