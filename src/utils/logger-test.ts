/**
 * Test utility to verify logger security measures
 * Run this in browser console to test logger behavior
 */

import { logger } from './logger';

export function testLoggerSecurity() {
  console.log('🧪 Testing Logger Security...');
  
  // Test 1: Check if sensitive data is sanitized
  console.log('\n1. Testing sensitive data sanitization:');
  logger.debug('Test with sensitive data', {
    user_id: '12345',
    password: 'secret123',
    access_token: 'token_abc123',
    normal_field: 'this should be visible'
  });
  
  // Test 2: Check auth logging
  console.log('\n2. Testing auth logging:');
  logger.auth.stateChange('SIGNED_IN', true);
  logger.auth.sessionCheck(true);
  
  // Test 3: Check production mode detection
  console.log('\n3. Environment detection:');
  console.log('DEV mode:', import.meta.env.DEV);
  console.log('PROD mode:', import.meta.env.PROD);
  console.log('MODE:', import.meta.env.MODE);
  console.log('LOG_LEVEL:', import.meta.env.VITE_LOG_LEVEL);
  
  // Test 4: Test different log levels
  console.log('\n4. Testing log levels:');
  logger.debug('This is a DEBUG message');
  logger.info('This is an INFO message');
  logger.warn('This is a WARN message');
  logger.error('This is an ERROR message');
  
  console.log('\n✅ Logger security test complete!');
  console.log('📋 Expected behavior:');
  console.log('- In DEVELOPMENT: All logs should be visible');
  console.log('- In PRODUCTION: Only ERROR logs should be visible');
  console.log('- Sensitive data should be [REDACTED] or undefined');
  console.log('- Auth logs should be hidden in production');
}

export function checkProductionSafety() {
  const isProd = import.meta.env.PROD || import.meta.env.MODE === 'production';
  
  if (isProd) {
    console.warn('🔒 PRODUCTION MODE DETECTED');
    console.warn('✅ Auth logs are disabled');
    console.warn('✅ Only ERROR level logs are shown');
    console.warn('✅ Sensitive data is not logged');
    return true;
  } else {
    console.log('🔧 DEVELOPMENT MODE DETECTED');
    console.log('ℹ️ All logs are visible for debugging');
    console.log('ℹ️ Sensitive data is sanitized but visible');
    return false;
  }
}

// Auto-run in development
if (import.meta.env.DEV) {
  console.log('🔍 Logger loaded - run testLoggerSecurity() to test');
}
