/**
 * 🔄 PHASE 2: REAL-TIME UPDATES IMPLEMENTATION
 * 
 * Provides real-time updates for:
 * - Dashboard metrics (booking counts, revenue)
 * - New booking notifications
 * - Booking status changes (cancellations, completions)
 * - Live admin activity monitoring
 */

import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';
import { logSecurityEvent } from './adminSecurity';

export interface RealTimeBookingUpdate {
  id: string;
  type: 'new_booking' | 'status_change' | 'cancellation';
  booking_id: string;
  status: string;
  venue_name?: string;
  court_name?: string;
  customer_name?: string;
  booking_date?: string;
  total_price?: number;
  timestamp: string;
}

export interface RealTimeDashboardMetrics {
  todayBookings: number;
  pendingBookings: number;
  todaysRevenue: number;
  lastUpdated: string;
}

export type RealTimeUpdateCallback = (update: RealTimeBookingUpdate) => void;
export type DashboardMetricsCallback = (metrics: RealTimeDashboardMetrics) => void;

class RealTimeManager {
  private channels: Map<string, RealtimeChannel> = new Map();
  private updateCallbacks: Set<RealTimeUpdateCallback> = new Set();
  private metricsCallbacks: Set<DashboardMetricsCallback> = new Set();
  private userId: string | null = null;
  private userRole: string | null = null;
  private adminVenues: string[] = [];

  /**
   * Initialize real-time updates for admin user
   */
  async initialize(userId: string, userRole: string, adminVenues: Array<{ venue_id: string }>) {
    this.userId = userId;
    this.userRole = userRole;
    this.adminVenues = adminVenues.map(v => v.venue_id);

    console.log('🔄 Initializing real-time updates for admin:', { userId, userRole, venueCount: this.adminVenues.length });

    // Set up real-time channels
    await this.setupBookingUpdatesChannel();
    await this.setupDashboardMetricsChannel();

    logSecurityEvent('REALTIME_INITIALIZED', userId, {
      userRole,
      venueCount: this.adminVenues.length,
      channels: ['bookings', 'dashboard_metrics']
    });
  }

  /**
   * Set up real-time channel for booking updates
   */
  private async setupBookingUpdatesChannel() {
    const channel = supabase
      .channel('admin_booking_updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings'
        },
        async (payload) => {
          await this.handleBookingUpdate(payload);
        }
      )
      .subscribe((status) => {
        console.log('🔄 Booking updates channel status:', status);
      });

    this.channels.set('bookings', channel);
  }

  /**
   * Set up real-time channel for dashboard metrics updates
   */
  private async setupDashboardMetricsChannel() {
    // Listen for changes that affect dashboard metrics
    const metricsChannel = supabase
      .channel('admin_dashboard_metrics')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings'
        },
        async () => {
          await this.refreshDashboardMetrics();
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'reviews'
        },
        async () => {
          await this.refreshDashboardMetrics();
        }
      )
      .subscribe((status) => {
        console.log('🔄 Dashboard metrics channel status:', status);
      });

    this.channels.set('dashboard_metrics', metricsChannel);
  }

  /**
   * Handle real-time booking updates
   */
  private async handleBookingUpdate(payload: any) {
    try {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      if (!this.userId) return;

      // Check if this booking affects admin's venues
      const bookingId = newRecord?.id || oldRecord?.id;
      if (!bookingId) return;

      // Get booking details with venue info
      const { data: bookingDetails } = await supabase
        .from('bookings')
        .select(`
          id,
          status,
          booking_date,
          total_price,
          guest_name,
          court:court_id (
            name,
            venue:venue_id (
              id,
              name
            )
          )
        `)
        .eq('id', bookingId)
        .single();

      if (!bookingDetails?.court?.venue) return;

      // Check if admin has access to this venue
      const venueId = bookingDetails.court.venue.id;
      if (this.userRole === 'admin' && !this.adminVenues.includes(venueId)) {
        return; // Admin doesn't have access to this venue
      }

      // Determine update type
      let updateType: RealTimeBookingUpdate['type'] = 'new_booking';
      if (eventType === 'UPDATE') {
        if (newRecord.status === 'cancelled') {
          updateType = 'cancellation';
        } else {
          updateType = 'status_change';
        }
      }

      // Create real-time update
      const update: RealTimeBookingUpdate = {
        id: `${updateType}-${bookingId}-${Date.now()}`,
        type: updateType,
        booking_id: bookingId,
        status: newRecord?.status || oldRecord?.status,
        venue_name: bookingDetails.court.venue.name,
        court_name: bookingDetails.court.name,
        customer_name: bookingDetails.guest_name || 'Customer',
        booking_date: bookingDetails.booking_date,
        total_price: bookingDetails.total_price,
        timestamp: new Date().toISOString()
      };

      // Notify all callbacks
      this.updateCallbacks.forEach(callback => {
        try {
          callback(update);
        } catch (error) {
          console.error('Error in real-time update callback:', error);
        }
      });

      // Log the real-time event
      logSecurityEvent('REALTIME_BOOKING_UPDATE', this.userId!, {
        updateType,
        bookingId,
        venueId,
        status: update.status
      });

    } catch (error) {
      console.error('Error handling real-time booking update:', error);
    }
  }

  /**
   * Refresh dashboard metrics in real-time
   */
  private async refreshDashboardMetrics() {
    if (!this.userId) return;

    try {
      // Use optimized dashboard function for real-time metrics
      const { data } = await supabase.rpc('get_admin_dashboard_stats_optimized', {
        admin_user_id: this.userId,
        target_date: new Date().toISOString().split('T')[0]
      });

      if (data) {
        const metrics: RealTimeDashboardMetrics = {
          todayBookings: data.todayBookings || 0,
          pendingBookings: data.pendingBookings || 0,
          todaysRevenue: data.todaysRevenue || 0,
          lastUpdated: new Date().toISOString()
        };

        // Notify all metrics callbacks
        this.metricsCallbacks.forEach(callback => {
          try {
            callback(metrics);
          } catch (error) {
            console.error('Error in dashboard metrics callback:', error);
          }
        });
      }
    } catch (error) {
      console.error('Error refreshing dashboard metrics:', error);
    }
  }

  /**
   * Subscribe to real-time booking updates
   */
  onBookingUpdate(callback: RealTimeUpdateCallback) {
    this.updateCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.updateCallbacks.delete(callback);
    };
  }

  /**
   * Subscribe to real-time dashboard metrics updates
   */
  onDashboardMetricsUpdate(callback: DashboardMetricsCallback) {
    this.metricsCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.metricsCallbacks.delete(callback);
    };
  }

  /**
   * Manually trigger dashboard metrics refresh
   */
  async triggerMetricsRefresh() {
    await this.refreshDashboardMetrics();
  }

  /**
   * Clean up all real-time subscriptions
   */
  cleanup() {
    console.log('🔄 Cleaning up real-time subscriptions...');
    
    this.channels.forEach((channel, name) => {
      supabase.removeChannel(channel);
      console.log(`🔄 Removed channel: ${name}`);
    });
    
    this.channels.clear();
    this.updateCallbacks.clear();
    this.metricsCallbacks.clear();
    
    if (this.userId) {
      logSecurityEvent('REALTIME_CLEANUP', this.userId, {
        channelsRemoved: Array.from(this.channels.keys())
      });
    }
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): { [key: string]: string } {
    const status: { [key: string]: string } = {};
    
    this.channels.forEach((channel, name) => {
      status[name] = channel.state;
    });
    
    return status;
  }
}

// Export singleton instance
export const realTimeManager = new RealTimeManager();

/**
 * Hook for easy real-time updates integration
 */
export function useRealTimeUpdates(
  userId: string,
  userRole: string,
  adminVenues: Array<{ venue_id: string }>,
  onBookingUpdate?: RealTimeUpdateCallback,
  onMetricsUpdate?: DashboardMetricsCallback
) {
  const initialize = async () => {
    await realTimeManager.initialize(userId, userRole, adminVenues);
    
    const unsubscribers: (() => void)[] = [];
    
    if (onBookingUpdate) {
      unsubscribers.push(realTimeManager.onBookingUpdate(onBookingUpdate));
    }
    
    if (onMetricsUpdate) {
      unsubscribers.push(realTimeManager.onDashboardMetricsUpdate(onMetricsUpdate));
    }
    
    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  };
  
  return { initialize, cleanup: () => realTimeManager.cleanup() };
}
