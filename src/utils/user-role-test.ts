/**
 * Test utility to verify user role query fixes
 * This helps ensure the PGRST116 error is resolved for user_roles table
 */

import { supabase } from '@/integrations/supabase/client';
import { safeUserRoleQuery } from './supabase-query-validator';

export interface UserRoleTestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

/**
 * Test the old problematic query (should fail for users with multiple roles)
 */
export async function testProblematicUserRoleQuery(userId: string): Promise<UserRoleTestResult> {
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId)
      .single(); // This should fail with PGRST116 if multiple roles exist

    return {
      testName: 'Problematic .single() User Role Query',
      passed: false,
      error: 'Expected this to fail but it succeeded',
      details: { data, error }
    };
  } catch (error: any) {
    if (error.message?.includes('PGRST116') || error.message?.includes('JSON object requested')) {
      return {
        testName: 'Problematic .single() User Role Query',
        passed: true,
        details: 'Correctly failed with PGRST116 error'
      };
    }
    return {
      testName: 'Problematic .single() User Role Query',
      passed: false,
      error: `Unexpected error: ${error.message}`,
      details: error
    };
  }
}

/**
 * Test the fixed query that handles multiple roles
 */
export async function testFixedUserRoleQuery(userId: string): Promise<UserRoleTestResult> {
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId); // This should work correctly for multiple roles

    if (error) {
      return {
        testName: 'Fixed Multiple Roles Query',
        passed: false,
        error: error.message,
        details: error
      };
    }

    return {
      testName: 'Fixed Multiple Roles Query',
      passed: true,
      details: { 
        roleCount: data?.length || 0,
        roles: data?.map(r => r.role) || [],
        message: `Found ${data?.length || 0} roles`
      }
    };
  } catch (error: any) {
    return {
      testName: 'Fixed Multiple Roles Query',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test the safe user role utility function
 */
export async function testSafeUserRoleUtility(userId: string): Promise<UserRoleTestResult> {
  try {
    const result = await safeUserRoleQuery(userId);
    
    return {
      testName: 'Safe User Role Utility Function',
      passed: !result.error,
      error: result.error?.message,
      details: {
        highestRole: result.role,
        allRoles: result.allRoles,
        hasError: !!result.error
      }
    };
  } catch (error: any) {
    return {
      testName: 'Safe User Role Utility Function',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test role hierarchy logic
 */
export async function testRoleHierarchy(userId: string): Promise<UserRoleTestResult> {
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId);

    if (error) {
      return {
        testName: 'Role Hierarchy Test',
        passed: false,
        error: error.message,
        details: error
      };
    }

    const roles = data?.map(r => r.role) || [];
    
    // Test hierarchy logic
    let expectedHighestRole = 'user';
    if (roles.includes('super_admin')) {
      expectedHighestRole = 'super_admin';
    } else if (roles.includes('admin')) {
      expectedHighestRole = 'admin';
    }

    const { role: actualHighestRole } = await safeUserRoleQuery(userId);

    return {
      testName: 'Role Hierarchy Test',
      passed: actualHighestRole === expectedHighestRole,
      details: {
        allRoles: roles,
        expectedHighestRole,
        actualHighestRole,
        hierarchyCorrect: actualHighestRole === expectedHighestRole
      }
    };
  } catch (error: any) {
    return {
      testName: 'Role Hierarchy Test',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test the specific super_admin error case
 */
export async function testSuperAdminErrorCase(): Promise<UserRoleTestResult> {
  const superAdminUserId = '85b5a6a5-1280-4259-a97c-2af8be8b00e7';
  
  try {
    // Test the exact query pattern that was failing
    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', superAdminUserId); // This should work now

    if (error) {
      return {
        testName: 'Super Admin Error Case Test',
        passed: false,
        error: error.message,
        details: { 
          userId: superAdminUserId,
          error,
          message: 'The super admin error case is still failing'
        }
      };
    }

    return {
      testName: 'Super Admin Error Case Test',
      passed: true,
      details: { 
        userId: superAdminUserId,
        roleCount: data?.length || 0,
        roles: data?.map(r => r.role) || [],
        message: 'The super admin error case is now resolved'
      }
    };
  } catch (error: any) {
    return {
      testName: 'Super Admin Error Case Test',
      passed: false,
      error: error.message,
      details: { 
        userId: superAdminUserId,
        error,
        message: 'Unexpected error in super admin test case'
      }
    };
  }
}

/**
 * Run all user role query tests
 */
export async function runUserRoleQueryTests(
  userId: string = '85b5a6a5-1280-4259-a97c-2af8be8b00e7'
): Promise<UserRoleTestResult[]> {
  console.log('🧪 Running user role query tests...');
  
  const tests = [
    () => testSuperAdminErrorCase(),
    () => testFixedUserRoleQuery(userId),
    () => testSafeUserRoleUtility(userId),
    () => testRoleHierarchy(userId),
    // Note: We skip the problematic query test to avoid errors in production
  ];

  const results: UserRoleTestResult[] = [];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
      console.log(`${result.passed ? '✅' : '❌'} ${result.testName}: ${result.passed ? 'PASSED' : 'FAILED'}`);
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    } catch (error) {
      results.push({
        testName: 'Unknown Test',
        passed: false,
        error: `Test execution failed: ${error}`,
        details: error
      });
    }
  }

  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  console.log(`\n📊 User Role Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All user role tests passed! User role queries are working correctly.');
  } else {
    console.log('⚠️ Some user role tests failed. Check the details above.');
  }

  return results;
}

/**
 * Quick test function for development
 */
export async function quickUserRoleTest(): Promise<boolean> {
  try {
    // Test with the super admin user ID that was causing issues
    const testUserId = '85b5a6a5-1280-4259-a97c-2af8be8b00e7';
    
    const result = await safeUserRoleQuery(testUserId);
    
    // Should return a role without error
    return !result.error && result.role !== null;
  } catch (error) {
    console.error('Quick user role test failed:', error);
    return false;
  }
}

/**
 * Test AdminHome_Mobile component pattern
 */
export async function testAdminHomeMobilePattern(userId: string): Promise<UserRoleTestResult> {
  try {
    // Simulate the AdminHome_Mobile component pattern
    const { data: userRoles, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId); // Fixed pattern without .single()

    if (roleError) {
      return {
        testName: 'AdminHome_Mobile Pattern Test',
        passed: false,
        error: roleError.message,
        details: roleError
      };
    }

    // Handle multiple roles - find the highest role
    let highestRole = null;
    if (userRoles && userRoles.length > 0) {
      if (userRoles.some(r => r.role === 'super_admin')) {
        highestRole = 'super_admin';
      } else if (userRoles.some(r => r.role === 'admin')) {
        highestRole = 'admin';
      } else {
        highestRole = 'user';
      }
    }

    return {
      testName: 'AdminHome_Mobile Pattern Test',
      passed: true,
      details: {
        roleCount: userRoles?.length || 0,
        allRoles: userRoles?.map(r => r.role) || [],
        highestRole,
        message: 'AdminHome_Mobile pattern works correctly'
      }
    };
  } catch (error: any) {
    return {
      testName: 'AdminHome_Mobile Pattern Test',
      passed: false,
      error: error.message,
      details: error
    };
  }
}
