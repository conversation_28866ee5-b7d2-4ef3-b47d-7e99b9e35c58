/**
 * Test file for weeklyUtils functions
 * Run this to verify the Active Days counter fix
 */

import { 
  getCurrentDayInWeekCycle, 
  getActiveDaysDisplay, 
  getActiveDaysLabel,
  getISTDateString,
  isToday,
  getDayName,
  debugWeeklyUtils
} from './weeklyUtils';
import { startOfWeek, addDays, format } from 'date-fns';

// Test function to verify day calculations
export function testWeeklyUtils() {
  console.log('🧪 Testing Weekly Utils Functions...\n');

  // Test 1: Current day calculation for different days of the week
  console.log('📅 Test 1: Day Number Calculation');
  
  // Create a test week starting Monday (June 17, 2025 is a Tuesday)
  const testWeekStart = new Date('2025-06-16T00:00:00'); // Monday
  
  const testDays = [
    { date: new Date('2025-06-16T12:00:00'), expected: 1, name: 'Monday' },
    { date: new Date('2025-06-17T12:00:00'), expected: 2, name: 'Tuesday' },
    { date: new Date('2025-06-18T12:00:00'), expected: 3, name: 'Wednesday' },
    { date: new Date('2025-06-19T12:00:00'), expected: 4, name: 'Thursday' },
    { date: new Date('2025-06-20T12:00:00'), expected: 5, name: 'Friday' },
    { date: new Date('2025-06-21T12:00:00'), expected: 6, name: 'Saturday' },
    { date: new Date('2025-06-22T12:00:00'), expected: 7, name: 'Sunday' }
  ];

  testDays.forEach(({ date, expected, name }) => {
    const result = getCurrentDayInWeekCycle(date, testWeekStart);
    const status = result === expected ? '✅' : '❌';
    console.log(`${status} ${name}: Expected ${expected}, Got ${result}`);
  });

  // Test 2: IST timezone handling
  console.log('\n🌏 Test 2: IST Timezone Handling');
  
  // Test with current time
  const now = new Date();
  const istOffset = 5.5 * 60 * 60 * 1000;
  const istDate = new Date(now.getTime() + istOffset);
  const currentWeekStart = startOfWeek(istDate, { weekStartsOn: 1 });
  const currentDay = getCurrentDayInWeekCycle();
  
  console.log(`Current UTC Time: ${now.toISOString()}`);
  console.log(`Current IST Time: ${istDate.toISOString()}`);
  console.log(`Current Week Start (Monday): ${format(currentWeekStart, 'yyyy-MM-dd')}`);
  console.log(`Current Day in Cycle: ${currentDay}/7`);
  console.log(`Day Name: ${getDayName(getISTDateString())}`);

  // Test 3: Active Days Display Logic
  console.log('\n📊 Test 3: Active Days Display Logic');
  
  // Test current week
  const currentWeekDisplay = getActiveDaysDisplay(currentWeekStart, 3);
  const currentWeekLabel = getActiveDaysLabel(currentWeekStart);
  console.log(`Current Week Display: ${currentWeekDisplay}`);
  console.log(`Current Week Label: ${currentWeekLabel}`);
  
  // Test historical week
  const historicalWeekStart = new Date('2025-06-09T00:00:00'); // Previous Monday
  const historicalWeekDisplay = getActiveDaysDisplay(historicalWeekStart, 5);
  const historicalWeekLabel = getActiveDaysLabel(historicalWeekStart);
  console.log(`Historical Week Display: ${historicalWeekDisplay}`);
  console.log(`Historical Week Label: ${historicalWeekLabel}`);

  // Test 4: Edge Cases
  console.log('\n🔍 Test 4: Edge Cases');
  
  // Test date outside of week
  const outsideDate = new Date('2025-06-23T12:00:00'); // Monday of next week
  const outsideResult = getCurrentDayInWeekCycle(outsideDate, testWeekStart);
  console.log(`Date outside week: Expected 0, Got ${outsideResult} ${outsideResult === 0 ? '✅' : '❌'}`);
  
  // Test midnight transitions
  const mondayMidnight = new Date('2025-06-16T00:00:00');
  const sundayMidnight = new Date('2025-06-22T23:59:59');
  const mondayResult = getCurrentDayInWeekCycle(mondayMidnight, testWeekStart);
  const sundayResult = getCurrentDayInWeekCycle(sundayMidnight, testWeekStart);
  console.log(`Monday midnight: Expected 1, Got ${mondayResult} ${mondayResult === 1 ? '✅' : '❌'}`);
  console.log(`Sunday midnight: Expected 7, Got ${sundayResult} ${sundayResult === 7 ? '✅' : '❌'}`);

  // Test 5: Real-world scenario (Today is Tuesday, June 17, 2025)
  console.log('\n🎯 Test 5: Real-world Scenario (Tuesday, June 17, 2025)');
  
  // Simulate Tuesday in IST
  const tuesdayIST = new Date('2025-06-17T14:30:00+05:30'); // 2:30 PM IST on Tuesday
  const tuesdayWeekStart = startOfWeek(tuesdayIST, { weekStartsOn: 1 });
  const tuesdayDay = getCurrentDayInWeekCycle(tuesdayIST, tuesdayWeekStart);
  
  console.log(`Tuesday IST: ${tuesdayIST.toISOString()}`);
  console.log(`Week Start: ${format(tuesdayWeekStart, 'yyyy-MM-dd')}`);
  console.log(`Day Number: ${tuesdayDay}/7 (Should be 2/7 for Tuesday) ${tuesdayDay === 2 ? '✅' : '❌'}`);
  
  const tuesdayDisplay = getActiveDaysDisplay(tuesdayWeekStart, 1); // 1 day with data
  console.log(`Active Days Display: ${tuesdayDisplay} (Should show 2/7 for current week)`);

  console.log('\n🎉 Weekly Utils Test Complete!');
  
  // Run debug function
  console.log('\n🔧 Debug Information:');
  debugWeeklyUtils();
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testWeeklyUtils = testWeeklyUtils;
  console.log('💡 Run testWeeklyUtils() in browser console to test the fix');
}
