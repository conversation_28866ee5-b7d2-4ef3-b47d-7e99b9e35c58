/**
 * Utility for managing pending users in the MSG91 integration
 * This helps admins monitor and clean up pending user registrations
 */

import { supabase } from '@/integrations/supabase/client';

export interface PendingUser {
  id: string;
  email: string;
  full_name: string | null;
  phone: string | null;
  verification_token: string;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

export interface PendingUserStats {
  total: number;
  expired: number;
  active: number;
  recentlyCreated: number; // Last 24 hours
}

export class PendingUsersManager {
  /**
   * Get all pending users (admin only)
   */
  async getAllPendingUsers(): Promise<{ data: PendingUser[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('pending_users')
        .select('*')
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      console.error('Error fetching pending users:', error);
      return { data: null, error };
    }
  }

  /**
   * Get pending user statistics
   */
  async getPendingUserStats(): Promise<{ stats: PendingUserStats | null; error: any }> {
    try {
      const { data, error } = await this.getAllPendingUsers();
      
      if (error || !data) {
        return { stats: null, error };
      }

      const now = new Date();
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const stats: PendingUserStats = {
        total: data.length,
        expired: data.filter(user => new Date(user.expires_at) < now).length,
        active: data.filter(user => new Date(user.expires_at) >= now).length,
        recentlyCreated: data.filter(user => new Date(user.created_at) >= twentyFourHoursAgo).length
      };

      return { stats, error: null };
    } catch (error) {
      console.error('Error calculating pending user stats:', error);
      return { stats: null, error };
    }
  }

  /**
   * Clean up expired pending users
   */
  async cleanupExpiredUsers(): Promise<{ deletedCount: number; error: any }> {
    try {
      const now = new Date().toISOString();
      
      const { data, error } = await supabase
        .from('pending_users')
        .delete()
        .lt('expires_at', now)
        .select('id');

      if (error) {
        return { deletedCount: 0, error };
      }

      return { deletedCount: data?.length || 0, error: null };
    } catch (error) {
      console.error('Error cleaning up expired users:', error);
      return { deletedCount: 0, error };
    }
  }

  /**
   * Get pending users by email (for debugging)
   */
  async getPendingUserByEmail(email: string): Promise<{ data: PendingUser | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('pending_users')
        .select('*')
        .eq('email', email.toLowerCase())
        .maybeSingle();

      return { data, error };
    } catch (error) {
      console.error('Error fetching pending user by email:', error);
      return { data: null, error };
    }
  }

  /**
   * Manually delete a pending user (admin only)
   */
  async deletePendingUser(userId: string): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await supabase
        .from('pending_users')
        .delete()
        .eq('id', userId);

      return { success: !error, error };
    } catch (error) {
      console.error('Error deleting pending user:', error);
      return { success: false, error };
    }
  }

  /**
   * Extend expiration time for a pending user
   */
  async extendUserExpiration(userId: string, hoursToAdd: number = 24): Promise<{ success: boolean; error: any }> {
    try {
      const newExpirationTime = new Date(Date.now() + hoursToAdd * 60 * 60 * 1000).toISOString();
      
      const { error } = await supabase
        .from('pending_users')
        .update({ 
          expires_at: newExpirationTime,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      return { success: !error, error };
    } catch (error) {
      console.error('Error extending user expiration:', error);
      return { success: false, error };
    }
  }

  /**
   * Get verification tokens that are about to expire (within next hour)
   */
  async getExpiringTokens(): Promise<{ data: PendingUser[] | null; error: any }> {
    try {
      const oneHourFromNow = new Date(Date.now() + 60 * 60 * 1000).toISOString();
      const now = new Date().toISOString();
      
      const { data, error } = await supabase
        .from('pending_users')
        .select('*')
        .gte('expires_at', now)
        .lte('expires_at', oneHourFromNow)
        .order('expires_at', { ascending: true });

      return { data, error };
    } catch (error) {
      console.error('Error fetching expiring tokens:', error);
      return { data: null, error };
    }
  }

  /**
   * Generate a report of pending user activity
   */
  async generateReport(): Promise<string> {
    try {
      const { stats, error: statsError } = await this.getPendingUserStats();
      const { data: expiringUsers, error: expiringError } = await this.getExpiringTokens();

      if (statsError || expiringError) {
        return 'Error generating report: Unable to fetch data';
      }

      const report = `
📊 Pending Users Report (${new Date().toLocaleString()})
═══════════════════════════════════════════════════════

📈 Statistics:
• Total pending users: ${stats?.total || 0}
• Active (not expired): ${stats?.active || 0}
• Expired: ${stats?.expired || 0}
• Created in last 24h: ${stats?.recentlyCreated || 0}

⏰ Expiring Soon (next hour):
${expiringUsers?.length ? 
  expiringUsers.map(user => 
    `• ${user.email} (expires: ${new Date(user.expires_at).toLocaleString()})`
  ).join('\n') : 
  '• No users expiring soon'
}

🧹 Recommendations:
${stats?.expired && stats.expired > 0 ? 
  `• Run cleanup to remove ${stats.expired} expired users` : 
  '• No cleanup needed'
}
${stats?.active && stats.active > 10 ? 
  '• High number of pending users - consider investigating email delivery' : 
  ''
}
      `.trim();

      return report;
    } catch (error) {
      console.error('Error generating report:', error);
      return 'Error generating report: ' + (error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Perform maintenance tasks
   */
  async performMaintenance(): Promise<{ 
    cleanupResult: { deletedCount: number; error: any };
    report: string;
  }> {
    console.log('🔧 Starting pending users maintenance...');
    
    // Clean up expired users
    const cleanupResult = await this.cleanupExpiredUsers();
    
    if (cleanupResult.deletedCount > 0) {
      console.log(`🗑️ Cleaned up ${cleanupResult.deletedCount} expired users`);
    }
    
    // Generate report
    const report = await this.generateReport();
    
    console.log('📋 Maintenance Report:');
    console.log(report);
    
    return { cleanupResult, report };
  }
}

// Export singleton instance
export const pendingUsersManager = new PendingUsersManager();

// Helper functions for console use
export const getPendingUsersReport = () => pendingUsersManager.generateReport().then(console.log);
export const cleanupPendingUsers = () => pendingUsersManager.performMaintenance();
export const getPendingUsersStats = () => pendingUsersManager.getPendingUserStats().then(result => console.log(result.stats));
