/**
 * Test utility for MSG91 integration
 * This helps verify that the MSG91 email service is working correctly
 */

import { msg91EmailService } from '@/services/msg91EmailService';
import { customAuthService } from '@/services/customAuthService';
import { supabase } from '@/integrations/supabase/client';

export interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

export class MSG91IntegrationTester {
  private testEmail = '<EMAIL>';
  private testName = 'Test User';

  /**
   * Test MSG91 configuration retrieval
   */
  async testMSG91Config(): Promise<TestResult> {
    try {
      const { data, error } = await supabase.functions.invoke('get-msg91-config');
      
      if (error) {
        return {
          testName: 'MSG91 Config Retrieval',
          passed: false,
          error: error.message,
          details: error
        };
      }

      const hasRequiredFields = data.authKey && data.domain;
      
      return {
        testName: 'MSG91 Config Retrieval',
        passed: hasRequiredFields,
        error: hasRequiredFields ? undefined : 'Missing required configuration fields',
        details: {
          hasAuthKey: !!data.authKey,
          hasDomain: !!data.domain
        }
      };
    } catch (error) {
      return {
        testName: 'MSG91 Config Retrieval',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      };
    }
  }

  /**
   * Test email sending functionality
   */
  async testEmailSending(): Promise<TestResult> {
    try {
      const result = await msg91EmailService.sendVerificationEmail(
        this.testEmail,
        this.testName,
        'https://example.com/verify?token=test123',
        false
      );

      return {
        testName: 'Email Sending',
        passed: result.success,
        error: result.error,
        details: result
      };
    } catch (error) {
      return {
        testName: 'Email Sending',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      };
    }
  }

  /**
   * Test database table creation
   */
  async testDatabaseTables(): Promise<TestResult> {
    try {
      // Test email_verification_tokens table
      const { error: tokenError } = await supabase
        .from('email_verification_tokens')
        .select('id')
        .limit(1);

      if (tokenError) {
        return {
          testName: 'Database Tables',
          passed: false,
          error: `email_verification_tokens table error: ${tokenError.message}`,
          details: tokenError
        };
      }

      // Test pending_users table
      const { error: pendingError } = await supabase
        .from('pending_users')
        .select('id')
        .limit(1);

      if (pendingError) {
        return {
          testName: 'Database Tables',
          passed: false,
          error: `pending_users table error: ${pendingError.message}`,
          details: pendingError
        };
      }

      // Test profiles table with new columns
      const { error: profileError } = await supabase
        .from('profiles')
        .select('id, email_verified, phone_verified')
        .limit(1);

      if (profileError) {
        return {
          testName: 'Database Tables',
          passed: false,
          error: `profiles table error: ${profileError.message}`,
          details: profileError
        };
      }

      return {
        testName: 'Database Tables',
        passed: true,
        details: {
          email_verification_tokens: 'OK',
          pending_users: 'OK',
          profiles_with_new_columns: 'OK'
        }
      };
    } catch (error) {
      return {
        testName: 'Database Tables',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      };
    }
  }

  /**
   * Test custom auth service functionality
   */
  async testCustomAuthService(): Promise<TestResult> {
    try {
      // This is a dry run test - we won't actually create a user
      // Just test that the service methods exist and can be called
      
      const testData = {
        email: '<EMAIL>',
        password: 'testpassword123',
        name: 'Test User',
        phone: '+1234567890'
      };

      // Test that the method exists and returns expected structure
      const signUpMethod = customAuthService.signUpWithCustomEmail;
      const resendMethod = customAuthService.resendVerificationEmail;
      const verifyMethod = customAuthService.verifyEmailToken;

      if (!signUpMethod || !resendMethod || !verifyMethod) {
        return {
          testName: 'Custom Auth Service',
          passed: false,
          error: 'Missing required methods in customAuthService',
          details: {
            hasSignUp: !!signUpMethod,
            hasResend: !!resendMethod,
            hasVerify: !!verifyMethod
          }
        };
      }

      return {
        testName: 'Custom Auth Service',
        passed: true,
        details: {
          signUpMethod: 'Available',
          resendMethod: 'Available',
          verifyMethod: 'Available'
        }
      };
    } catch (error) {
      return {
        testName: 'Custom Auth Service',
        passed: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error
      };
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting MSG91 Integration Tests...');
    
    const tests = [
      this.testDatabaseTables(),
      this.testMSG91Config(),
      this.testCustomAuthService(),
      // Note: Email sending test is commented out to avoid sending test emails
      // Uncomment when you want to test actual email sending
      // this.testEmailSending()
    ];

    const results = await Promise.all(tests);
    
    console.log('📊 Test Results:');
    results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    });

    const passedTests = results.filter(r => r.passed).length;
    const totalTests = results.length;
    
    console.log(`\n🎯 Summary: ${passedTests}/${totalTests} tests passed`);
    
    return results;
  }
}

// Export a singleton instance for easy use
export const msg91Tester = new MSG91IntegrationTester();

// Helper function to run tests from console
export const runMSG91Tests = () => msg91Tester.runAllTests();
