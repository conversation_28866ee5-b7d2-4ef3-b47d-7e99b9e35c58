import { supabase } from '@/integrations/supabase/client';

export interface UserPermissions {
  canViewBlockedSlots: boolean;
  canBlockSlots: boolean;
  canUnblockSlots: boolean;
  canBulkBlockSlots: boolean;
  canBlockVenueSlots: boolean;
  canViewAllVenues: boolean;
  canViewSlotManagementDashboard: boolean;
  canManageSlotPermissions: boolean;
  allowedVenueIds: string[];
  role: string;
}

export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: string;
}

export class SlotManagementPermissions {
  /**
   * Get comprehensive permissions for a user
   */
  static async getUserPermissions(userId: string): Promise<UserPermissions | null> {
    try {
      // Get user role
      const { data: roleData, error: roleError } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', userId)
        .single();

      if (roleError || !roleData) {
        console.error('Error fetching user role:', roleError);
        return null;
      }

      const role = roleData.role;

      // Get venue access for non-super admins
      let allowedVenueIds: string[] = [];
      if (role !== 'super_admin') {
        const { data: venueData, error: venueError } = await supabase
          .from('venue_admins')
          .select('venue_id')
          .eq('admin_id', userId);

        if (venueError) {
          console.error('Error fetching venue access:', venueError);
          return null;
        }

        allowedVenueIds = venueData?.map(v => v.venue_id) || [];
      }

      // Define permissions based on role
      const permissions: UserPermissions = {
        canViewBlockedSlots: ['admin', 'super_admin'].includes(role),
        canBlockSlots: ['admin', 'super_admin'].includes(role),
        canUnblockSlots: ['admin', 'super_admin'].includes(role),
        canBulkBlockSlots: role === 'super_admin',
        canBlockVenueSlots: role === 'super_admin',
        canViewAllVenues: role === 'super_admin',
        canViewSlotManagementDashboard: role === 'super_admin',
        canManageSlotPermissions: role === 'super_admin',
        allowedVenueIds,
        role
      };

      return permissions;
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return null;
    }
  }

  /**
   * Check if user can perform a specific action
   */
  static async checkPermission(
    userId: string,
    action: keyof UserPermissions,
    resourceId?: string
  ): Promise<PermissionCheckResult> {
    try {
      const permissions = await this.getUserPermissions(userId);
      
      if (!permissions) {
        return {
          allowed: false,
          reason: 'Unable to verify user permissions'
        };
      }

      // Check basic permission
      const hasPermission = permissions[action] as boolean;
      if (!hasPermission) {
        return {
          allowed: false,
          reason: `Insufficient permissions for action: ${action}`,
          requiredRole: action.includes('bulk') || action.includes('dashboard') ? 'super_admin' : 'admin'
        };
      }

      // For venue-specific actions, check venue access
      if (resourceId && !permissions.canViewAllVenues) {
        if (!permissions.allowedVenueIds.includes(resourceId)) {
          return {
            allowed: false,
            reason: 'No access to this venue'
          };
        }
      }

      return { allowed: true };
    } catch (error) {
      console.error('Error checking permission:', error);
      return {
        allowed: false,
        reason: 'Permission check failed'
      };
    }
  }

  /**
   * Check if user can access slot management features
   */
  static async canAccessSlotManagement(userId: string): Promise<PermissionCheckResult> {
    return this.checkPermission(userId, 'canViewBlockedSlots');
  }

  /**
   * Check if user can perform bulk operations
   */
  static async canPerformBulkOperations(userId: string): Promise<PermissionCheckResult> {
    return this.checkPermission(userId, 'canBulkBlockSlots');
  }

  /**
   * Check if user can access super admin dashboard
   */
  static async canAccessDashboard(userId: string): Promise<PermissionCheckResult> {
    return this.checkPermission(userId, 'canViewSlotManagementDashboard');
  }

  /**
   * Check if user can block slots for a specific venue
   */
  static async canBlockSlotsForVenue(userId: string, venueId: string): Promise<PermissionCheckResult> {
    const permissions = await this.getUserPermissions(userId);
    
    if (!permissions) {
      return {
        allowed: false,
        reason: 'Unable to verify user permissions'
      };
    }

    if (!permissions.canBlockSlots) {
      return {
        allowed: false,
        reason: 'No permission to block slots',
        requiredRole: 'admin'
      };
    }

    if (!permissions.canViewAllVenues && !permissions.allowedVenueIds.includes(venueId)) {
      return {
        allowed: false,
        reason: 'No access to this venue'
      };
    }

    return { allowed: true };
  }

  /**
   * Check if user can unblock specific slots
   */
  static async canUnblockSlots(userId: string, slotIds: string[]): Promise<PermissionCheckResult> {
    try {
      const permissions = await this.getUserPermissions(userId);
      
      if (!permissions) {
        return {
          allowed: false,
          reason: 'Unable to verify user permissions'
        };
      }

      if (!permissions.canUnblockSlots) {
        return {
          allowed: false,
          reason: 'No permission to unblock slots',
          requiredRole: 'admin'
        };
      }

      // For super admins, allow unblocking any slots
      if (permissions.role === 'super_admin') {
        return { allowed: true };
      }

      // For regular admins, check if they have access to the venues of these slots
      const { data: slotData, error } = await supabase
        .from('blocked_slots')
        .select(`
          id,
          courts!inner (
            venue_id
          )
        `)
        .in('id', slotIds);

      if (error) {
        return {
          allowed: false,
          reason: 'Error verifying slot access'
        };
      }

      const venueIds = slotData?.map(slot => slot.courts.venue_id) || [];
      const hasAccessToAllVenues = venueIds.every(venueId => 
        permissions.allowedVenueIds.includes(venueId)
      );

      if (!hasAccessToAllVenues) {
        return {
          allowed: false,
          reason: 'No access to some of the selected slots'
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('Error checking unblock permission:', error);
      return {
        allowed: false,
        reason: 'Permission check failed'
      };
    }
  }

  /**
   * Filter venues based on user permissions
   */
  static async getAccessibleVenues(userId: string): Promise<string[]> {
    try {
      const permissions = await this.getUserPermissions(userId);
      
      if (!permissions) {
        return [];
      }

      if (permissions.canViewAllVenues) {
        // Super admin can access all venues
        const { data: venues, error } = await supabase
          .from('venues')
          .select('id')
          .eq('is_active', true);

        if (error) {
          console.error('Error fetching all venues:', error);
          return [];
        }

        return venues?.map(v => v.id) || [];
      }

      return permissions.allowedVenueIds;
    } catch (error) {
      console.error('Error getting accessible venues:', error);
      return [];
    }
  }

  /**
   * Create audit log entry for slot management actions
   */
  static async logSlotManagementAction(
    userId: string,
    action: string,
    details: Record<string, any>
  ): Promise<void> {
    try {
      await supabase
        .from('audit_logs')
        .insert({
          user_id: userId,
          action,
          table_name: 'blocked_slots',
          details,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Error logging slot management action:', error);
      // Don't throw error as this shouldn't block the main operation
    }
  }
}

export default SlotManagementPermissions;
