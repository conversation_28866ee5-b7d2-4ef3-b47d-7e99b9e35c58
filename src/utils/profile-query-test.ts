/**
 * Test utility to verify profile query fixes
 * This helps ensure the PGRST116 error is resolved for profiles table
 */

import { supabase } from '@/integrations/supabase/client';
import { safeProfileQuery, safeProfileQueryWithCreate } from './supabase-query-validator';

export interface ProfileTestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

/**
 * Test the old problematic query (should fail for non-existent profiles)
 */
export async function testProblematicProfileQuery(userId: string): Promise<ProfileTestResult> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('full_name, profile_name')
      .eq('id', userId)
      .single(); // This should fail with PGRST116 if no profile exists

    return {
      testName: 'Problematic .single() Profile Query',
      passed: false,
      error: 'Expected this to fail but it succeeded',
      details: { data, error }
    };
  } catch (error: any) {
    if (error.message?.includes('PGRST116') || error.message?.includes('JSON object requested')) {
      return {
        testName: 'Problematic .single() Profile Query',
        passed: true,
        details: 'Correctly failed with PGRST116 error'
      };
    }
    return {
      testName: 'Problematic .single() Profile Query',
      passed: false,
      error: `Unexpected error: ${error.message}`,
      details: error
    };
  }
}

/**
 * Test the fixed query using .maybeSingle()
 */
export async function testFixedProfileQuery(userId: string): Promise<ProfileTestResult> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('full_name, profile_name')
      .eq('id', userId)
      .maybeSingle(); // This should work correctly

    if (error) {
      return {
        testName: 'Fixed .maybeSingle() Profile Query',
        passed: false,
        error: error.message,
        details: error
      };
    }

    return {
      testName: 'Fixed .maybeSingle() Profile Query',
      passed: true,
      details: { 
        hasProfile: !!data,
        message: data ? 'Profile exists' : 'Profile does not exist'
      }
    };
  } catch (error: any) {
    return {
      testName: 'Fixed .maybeSingle() Profile Query',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test the safe profile utility function
 */
export async function testSafeProfileUtility(userId: string): Promise<ProfileTestResult> {
  try {
    const result = await safeProfileQuery(userId, 'full_name, profile_name');
    
    return {
      testName: 'Safe Profile Utility Function',
      passed: !result.error,
      error: result.error?.message,
      details: {
        hasProfile: !!result.profile,
        hasError: !!result.error
      }
    };
  } catch (error: any) {
    return {
      testName: 'Safe Profile Utility Function',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test the safe profile utility with auto-creation
 */
export async function testSafeProfileWithCreate(userId: string, userEmail?: string): Promise<ProfileTestResult> {
  try {
    const result = await safeProfileQueryWithCreate(
      userId, 
      userEmail || '<EMAIL>', 
      'Test User'
    );
    
    return {
      testName: 'Safe Profile Utility with Auto-Create',
      passed: !result.error,
      error: result.error?.message,
      details: {
        hasProfile: !!result.profile,
        wasCreated: result.created,
        hasError: !!result.error
      }
    };
  } catch (error: any) {
    return {
      testName: 'Safe Profile Utility with Auto-Create',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test profile queries in team chat context
 */
export async function testTeamChatProfileQuery(userId: string): Promise<ProfileTestResult> {
  try {
    // Simulate the team chat profile query
    const { data: profileData } = await supabase
      .from('profiles')
      .select('full_name, profile_name')
      .eq('id', userId)
      .maybeSingle();

    return {
      testName: 'Team Chat Profile Query',
      passed: true,
      details: {
        hasProfile: !!profileData,
        fullName: profileData?.full_name || null,
        profileName: profileData?.profile_name || null
      }
    };
  } catch (error: any) {
    return {
      testName: 'Team Chat Profile Query',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test the specific error case that was reported
 */
export async function testSpecificErrorCase(): Promise<ProfileTestResult> {
  const userId = '42e1f573-fac5-4bf0-a0cf-6437ca1efe44';

  try {
    // Test the exact query pattern that was failing
    const { data, error } = await supabase
      .from('profiles')
      .select('full_name, profile_name')
      .eq('id', userId)
      .maybeSingle(); // This should work now

    if (error) {
      return {
        testName: 'Specific Error Case Test',
        passed: false,
        error: error.message,
        details: {
          userId,
          error,
          message: 'The specific error case is still failing'
        }
      };
    }

    return {
      testName: 'Specific Error Case Test',
      passed: true,
      details: {
        userId,
        hasProfile: !!data,
        profileData: data,
        message: 'The specific error case is now resolved'
      }
    };
  } catch (error: any) {
    return {
      testName: 'Specific Error Case Test',
      passed: false,
      error: error.message,
      details: {
        userId,
        error,
        message: 'Unexpected error in specific test case'
      }
    };
  }
}

/**
 * Run all profile query tests
 */
export async function runProfileQueryTests(
  userId: string = '42e1f573-fac5-4bf0-a0cf-6437ca1efe44'
): Promise<ProfileTestResult[]> {
  console.log('🧪 Running profile query tests...');

  const tests = [
    () => testSpecificErrorCase(),
    () => testFixedProfileQuery(userId),
    () => testSafeProfileUtility(userId),
    () => testTeamChatProfileQuery(userId),
    // Note: We skip the problematic query test and auto-create test to avoid side effects
  ];

  const results: ProfileTestResult[] = [];

  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
      console.log(`${result.passed ? '✅' : '❌'} ${result.testName}: ${result.passed ? 'PASSED' : 'FAILED'}`);
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    } catch (error) {
      results.push({
        testName: 'Unknown Test',
        passed: false,
        error: `Test execution failed: ${error}`,
        details: error
      });
    }
  }

  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;

  console.log(`\n📊 Profile Test Results: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All profile tests passed! Profile queries are working correctly.');
  } else {
    console.log('⚠️ Some profile tests failed. Check the details above.');
  }

  return results;
}

/**
 * Quick test function for development
 */
export async function quickProfileTest(): Promise<boolean> {
  try {
    // Use test ID that is likely to have no profile
    const testUserId = '00000000-0000-0000-0000-000000000001';
    
    const result = await safeProfileQuery(testUserId);
    
    // Should return null for profile but no error
    return !result.error && !result.profile;
  } catch (error) {
    console.error('Quick profile test failed:', error);
    return false;
  }
}

/**
 * Test all components that use profile queries
 */
export async function testAllProfileComponents(): Promise<{ [key: string]: boolean }> {
  const testUserId = '42e1f573-fac5-4bf0-a0cf-6437ca1efe44';
  const results: { [key: string]: boolean } = {};

  // Test TeamChat component pattern
  try {
    const { data: profileData } = await supabase
      .from('profiles')
      .select('full_name, profile_name')
      .eq('id', testUserId)
      .maybeSingle();
    results.teamChat = true;
  } catch (error) {
    results.teamChat = false;
  }

  // Test Profile page pattern
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, full_name, email, phone')
      .eq('id', testUserId)
      .maybeSingle();
    results.profilePage = !error;
  } catch (error) {
    results.profilePage = false;
  }

  // Test AdminChatInterface pattern
  try {
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('full_name, email')
      .eq('id', testUserId)
      .maybeSingle();
    results.adminChat = !profileError;
  } catch (error) {
    results.adminChat = false;
  }

  // Test BookingManagement pattern
  try {
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('full_name, email, phone')
      .eq('id', testUserId)
      .maybeSingle();
    results.bookingManagement = !userError;
  } catch (error) {
    results.bookingManagement = false;
  }

  return results;
}
