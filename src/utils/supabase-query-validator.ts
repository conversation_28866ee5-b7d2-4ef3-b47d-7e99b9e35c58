/**
 * Utility to validate and test Supabase queries to prevent "[object Object]" errors
 * This helps catch issues where complex objects are passed to query methods
 */

import { supabase } from '@/integrations/supabase/client';

export interface QueryValidationResult {
  isValid: boolean;
  error?: string;
  suggestion?: string;
}

/**
 * Validates that venue_id parameters are proper UUID strings
 */
export function validateVenueId(venueId: any): QueryValidationResult {
  if (typeof venueId !== 'string') {
    return {
      isValid: false,
      error: `venue_id must be a string, got ${typeof venueId}`,
      suggestion: 'Extract the UUID string from the object before passing to query'
    };
  }

  // Basic UUID format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(venueId)) {
    return {
      isValid: false,
      error: `venue_id "${venueId}" is not a valid UUID format`,
      suggestion: 'Ensure venue_id is a properly formatted UUID string'
    };
  }

  return { isValid: true };
}

/**
 * Validates venue filter objects to prevent .match() issues
 */
export function validateVenueFilter(filter: any): QueryValidationResult {
  if (typeof filter !== 'object' || filter === null) {
    return {
      isValid: false,
      error: 'Filter must be an object',
      suggestion: 'Use proper Supabase query methods instead of .match() with complex objects'
    };
  }

  // Check for nested objects that would cause "[object Object]" errors
  for (const [key, value] of Object.entries(filter)) {
    if (typeof value === 'object' && value !== null) {
      return {
        isValid: false,
        error: `Filter property "${key}" contains nested object: ${JSON.stringify(value)}`,
        suggestion: `Use .in('${key}', array) or .eq('${key}', value) instead of .match() with nested objects`
      };
    }
  }

  return { isValid: true };
}

/**
 * Safe wrapper for reviews queries with venue filtering
 */
export async function safeReviewsQuery(options: {
  venueIds?: string[];
  isApproved?: boolean;
  dateFrom?: string;
  countOnly?: boolean;
}) {
  const { venueIds, isApproved, dateFrom, countOnly } = options;

  try {
    // Validate venue IDs if provided
    if (venueIds) {
      for (const venueId of venueIds) {
        const validation = validateVenueId(venueId);
        if (!validation.isValid) {
          throw new Error(`Invalid venue ID: ${validation.error}`);
        }
      }
    }

    // Build query safely - start with select to get the right type
    let query = countOnly
      ? supabase.from('reviews').select('id', { count: 'exact' })
      : supabase.from('reviews').select('*');

    // Apply filters using proper Supabase methods
    if (venueIds && venueIds.length > 0) {
      query = query.in('venue_id', venueIds);
    }

    if (isApproved !== undefined) {
      query = query.eq('is_approved', isApproved);
    }

    if (dateFrom) {
      query = query.gte('created_at', dateFrom);
    }

    return await query;
  } catch (error) {
    console.error('Safe reviews query error:', error);
    throw error;
  }
}

/**
 * Test function to verify the fix works
 */
export async function testReviewsQueryFix() {
  console.log('🧪 Testing reviews query fix...');

  try {
    // Test 1: Valid venue IDs array
    const testVenueIds = ['123e4567-e89b-12d3-a456-426614174000'];
    const result1 = await safeReviewsQuery({
      venueIds: testVenueIds,
      isApproved: true,
      countOnly: true
    });
    console.log('✅ Test 1 passed: Valid venue IDs array');

    // Test 2: Empty venue IDs (should work for super admin)
    const result2 = await safeReviewsQuery({
      isApproved: true,
      countOnly: true
    });
    console.log('✅ Test 2 passed: No venue filter');

    // Test 3: Date filtering
    const result3 = await safeReviewsQuery({
      dateFrom: '2024-01-01',
      countOnly: true
    });
    console.log('✅ Test 3 passed: Date filtering');

    console.log('🎉 All tests passed! Reviews query fix is working correctly.');
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Safe wrapper for venue subscription queries
 */
export async function safeVenueSubscriptionCheck(userId: string, venueId: string) {
  try {
    const validation = validateVenueId(venueId);
    if (!validation.isValid) {
      throw new Error(`Invalid venue ID: ${validation.error}`);
    }

    const { data, error } = await supabase
      .from('venue_subscriptions')
      .select('id')
      .eq('user_id', userId)
      .eq('venue_id', venueId)
      .maybeSingle(); // Use maybeSingle() to handle 0 or 1 rows

    if (error) {
      console.error('Error checking venue subscription:', error);
      return { isSubscribed: false, error };
    }

    return { isSubscribed: !!data, error: null };
  } catch (error) {
    console.error('Safe venue subscription check error:', error);
    return { isSubscribed: false, error };
  }
}

/**
 * Safe wrapper for user profile queries
 */
export async function safeProfileQuery(userId: string, selectFields: string = 'id, full_name, email, phone') {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(selectFields)
      .eq('id', userId)
      .maybeSingle(); // Use maybeSingle() to handle missing profiles

    if (error) {
      console.error('Error fetching user profile:', error);
      return { profile: null, error };
    }

    return { profile: data, error: null };
  } catch (error) {
    console.error('Safe profile query error:', error);
    return { profile: null, error };
  }
}

/**
 * Safe wrapper for user profile queries with automatic profile creation
 */
export async function safeProfileQueryWithCreate(
  userId: string,
  userEmail?: string,
  userFullName?: string,
  selectFields: string = 'id, full_name, email, phone'
) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(selectFields)
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user profile:', error);
      return { profile: null, error, created: false };
    }

    if (!data) {
      // Profile doesn't exist, create a basic one
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert([{
          id: userId,
          full_name: userFullName || '',
          email: userEmail || ''
        }])
        .select(selectFields)
        .single();

      if (createError) {
        console.error('Error creating profile:', createError);
        return { profile: null, error: createError, created: false };
      }

      return { profile: newProfile, error: null, created: true };
    }

    return { profile: data, error: null, created: false };
  } catch (error) {
    console.error('Safe profile query with create error:', error);
    return { profile: null, error, created: false };
  }
}

/**
 * Safe wrapper for user role queries that handles multiple roles
 */
export async function safeUserRoleQuery(userId: string): Promise<{
  role: 'user' | 'admin' | 'super_admin' | null;
  error: any;
  allRoles?: string[];
}> {
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId); // Don't use .single() - users can have multiple roles

    if (error) {
      console.error('Error fetching user roles:', error);
      return { role: null, error };
    }

    if (!data || data.length === 0) {
      return { role: 'user', error: null, allRoles: [] }; // Default to user role
    }

    const roles = data.map(r => r.role);

    // Find the highest role (super_admin > admin > user)
    let highestRole: 'user' | 'admin' | 'super_admin' = 'user';
    if (roles.includes('super_admin')) {
      highestRole = 'super_admin';
    } else if (roles.includes('admin')) {
      highestRole = 'admin';
    }

    return { role: highestRole, error: null, allRoles: roles };
  } catch (error) {
    console.error('Safe user role query error:', error);
    return { role: null, error };
  }
}

/**
 * Safe wrapper for single record queries that might not exist
 * Note: This function uses 'any' types to work around TypeScript strict table name checking
 */
export async function safeSingleQuery<T = any>(
  tableName: string,
  selectFields: string,
  filters: Record<string, any>
): Promise<{ data: T | null; error: any }> {
  try {
    // Use 'any' to bypass strict table name checking
    let query = (supabase as any).from(tableName).select(selectFields);

    // Apply filters
    for (const [key, value] of Object.entries(filters)) {
      query = query.eq(key, value);
    }

    const { data, error } = await query.maybeSingle();

    if (error) {
      console.error(`Error in safe single query for ${tableName}:`, error);
      return { data: null, error };
    }

    return { data: data as T, error: null };
  } catch (error) {
    console.error(`Safe single query error for ${tableName}:`, error);
    return { data: null, error };
  }
}

/**
 * Development helper to detect problematic queries
 */
export function detectProblematicQueries() {
  if (import.meta.env.DEV) {
    // Override console.error to catch Supabase errors
    const originalError = console.error;
    console.error = (...args: any[]) => {
      const message = args.join(' ');
      if (message.includes('[object Object]') ||
          message.includes('invalid input syntax for type uuid') ||
          message.includes('PGRST116') ||
          message.includes('JSON object requested, multiple (or no) rows returned')) {
        console.warn('🚨 DETECTED PROBLEMATIC QUERY:', message);
        console.warn('💡 This might be caused by:');
        console.warn('   - Using .single() when 0 rows exist (use .maybeSingle())');
        console.warn('   - Passing objects to Supabase query methods');
        console.warn('🔧 Use safeVenueSubscriptionCheck() or safeSingleQuery() to fix this');
      }
      originalError.apply(console, args);
    };
  }
}
