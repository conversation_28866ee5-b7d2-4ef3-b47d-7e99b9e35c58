
import { Address, Coordinates, GeocodingResponse } from '@/types/location';

const NOMINATIM_BASE_URL = 'https://nominatim.openstreetmap.org';
const REQUEST_DELAY = 1000; // 1 second delay between requests for rate limiting

let lastRequestTime = 0;

const delayRequest = async (): Promise<void> => {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < REQUEST_DELAY) {
    await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY - timeSinceLastRequest));
  }
  
  lastRequestTime = Date.now();
};

export const geocoding = {
  // Convert coordinates to address
  reverseGeocode: async (coordinates: Coordinates): Promise<Address> => {
    await delayRequest();
    
    try {
      const response = await fetch(
        `${NOMINATIM_BASE_URL}/reverse?format=json&lat=${coordinates.latitude}&lon=${coordinates.longitude}&zoom=18&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'Grid2Play Sports Venue App'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Geocoding request failed');
      }

      const data: GeocodingResponse = await response.json();
      
      return {
        street: data.address?.road,
        area: data.address?.suburb,
        city: data.address?.city,
        state: data.address?.state,
        country: data.address?.country,
        postal_code: data.address?.postcode,
        display_name: data.display_name
      };
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      return {
        display_name: `${coordinates.latitude.toFixed(4)}, ${coordinates.longitude.toFixed(4)}`
      };
    }
  },

  // Convert address to coordinates
  forwardGeocode: async (address: string): Promise<Coordinates | null> => {
    await delayRequest();
    
    try {
      const response = await fetch(
        `${NOMINATIM_BASE_URL}/search?format=json&q=${encodeURIComponent(address)}&limit=1`,
        {
          headers: {
            'User-Agent': 'Grid2Play Sports Venue App'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Geocoding request failed');
      }

      const data: GeocodingResponse[] = await response.json();
      
      if (data.length === 0) {
        return null;
      }

      return {
        latitude: parseFloat(data[0].lat),
        longitude: parseFloat(data[0].lon)
      };
    } catch (error) {
      console.error('Forward geocoding failed:', error);
      return null;
    }
  },

  // Search for address suggestions
  searchAddresses: async (query: string): Promise<Address[]> => {
    if (query.length < 3) return [];
    
    await delayRequest();
    
    try {
      const response = await fetch(
        `${NOMINATIM_BASE_URL}/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'Grid2Play Sports Venue App'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Address search failed');
      }

      const data: GeocodingResponse[] = await response.json();
      
      return data.map(item => ({
        street: item.address?.road,
        area: item.address?.suburb,
        city: item.address?.city,
        state: item.address?.state,
        country: item.address?.country,
        postal_code: item.address?.postcode,
        display_name: item.display_name
      }));
    } catch (error) {
      console.error('Address search failed:', error);
      return [];
    }
  }
};
