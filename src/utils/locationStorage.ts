
import { LocationData } from '@/types/location';

const LOCATION_STORAGE_KEY = 'grid2play_location';
const LOCATION_CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

export const locationStorage = {
  save: (location: LocationData): void => {
    try {
      localStorage.setItem(LOCATION_STORAGE_KEY, JSON.stringify(location));
    } catch (error) {
      console.warn('Failed to save location to localStorage:', error);
    }
  },

  load: (): LocationData | null => {
    try {
      const stored = localStorage.getItem(LOCATION_STORAGE_KEY);
      if (!stored) return null;

      const location: LocationData = JSON.parse(stored);
      
      // Check if cache is expired
      if (Date.now() - location.timestamp > LOCATION_CACHE_DURATION) {
        localStorage.removeItem(LOCATION_STORAGE_KEY);
        return null;
      }

      return location;
    } catch (error) {
      console.warn('Failed to load location from localStorage:', error);
      localStorage.removeItem(LOCATION_STORAGE_KEY);
      return null;
    }
  },

  clear: (): void => {
    try {
      localStorage.removeItem(LOCATION_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear location from localStorage:', error);
    }
  },

  isExpired: (location: LocationData): boolean => {
    return Date.now() - location.timestamp > LOCATION_CACHE_DURATION;
  }
};
