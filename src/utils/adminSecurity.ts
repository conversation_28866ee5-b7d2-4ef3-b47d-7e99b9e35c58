/**
 * Admin Security Utilities - Server-side Role Validation
 * 
 * This module provides secure role validation and access control utilities
 * for admin operations. All functions are additive and maintain backwards
 * compatibility with existing systems.
 * 
 * SAFETY: No database schema changes, instant rollback capability
 */

import { supabase } from '@/integrations/supabase/client';

export interface RoleValidationResult {
  isValid: boolean;
  role: 'user' | 'admin' | 'super_admin' | null;
  venues: string[];
  error?: string;
  userId: string;
}

export interface VenueAccessResult {
  hasAccess: boolean;
  accessType: 'super_admin' | 'venue_admin' | 'none';
  error?: string;
}

/**
 * Verify user's admin role and get their venue access
 * Uses existing user_roles and venue_admins tables
 * 
 * @param userId - User ID to verify
 * @returns Role validation result with venue access
 */
export async function verifyAdminRole(userId: string): Promise<RoleValidationResult> {
  try {
    if (!userId) {
      return {
        isValid: false,
        role: null,
        venues: [],
        error: 'User ID is required',
        userId
      };
    }

    // Step 1: Get user role from existing user_roles table
    const { data: roleData, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId);

    if (roleError) {
      console.error('Error fetching user role:', roleError);
      return {
        isValid: false,
        role: null,
        venues: [],
        error: roleError.message,
        userId
      };
    }

    // Step 2: Determine highest role (existing hierarchy logic)
    let userRole: 'user' | 'admin' | 'super_admin' = 'user';
    if (roleData && roleData.length > 0) {
      const roles = roleData.map(r => r.role);
      if (roles.includes('super_admin')) {
        userRole = 'super_admin';
      } else if (roles.includes('admin')) {
        userRole = 'admin';
      }
    }

    // Step 3: Get venue access for admins
    let venueIds: string[] = [];
    if (userRole === 'admin') {
      const { data: venueData, error: venueError } = await supabase
        .from('venue_admins')
        .select('venue_id')
        .eq('user_id', userId);

      if (venueError) {
        console.error('Error fetching venue access:', venueError);
        return {
          isValid: false,
          role: userRole,
          venues: [],
          error: venueError.message,
          userId
        };
      }

      venueIds = venueData?.map(v => v.venue_id) || [];
    } else if (userRole === 'super_admin') {
      // Super admins have access to all venues (handled in queries)
      venueIds = ['*']; // Special marker for all venues
    }

    // Step 4: Validate admin status
    const isValidAdmin = userRole === 'admin' || userRole === 'super_admin';

    return {
      isValid: isValidAdmin,
      role: userRole,
      venues: venueIds,
      userId
    };

  } catch (error) {
    console.error('Error in verifyAdminRole:', error);
    return {
      isValid: false,
      role: null,
      venues: [],
      error: error instanceof Error ? error.message : 'Unknown error',
      userId
    };
  }
}

/**
 * Verify if user has access to a specific venue
 * 
 * @param userId - User ID to verify
 * @param venueId - Venue ID to check access for
 * @returns Venue access result
 */
export async function verifyVenueAccess(userId: string, venueId: string): Promise<VenueAccessResult> {
  try {
    if (!userId || !venueId) {
      return {
        hasAccess: false,
        accessType: 'none',
        error: 'User ID and Venue ID are required'
      };
    }

    const roleValidation = await verifyAdminRole(userId);

    if (!roleValidation.isValid) {
      return {
        hasAccess: false,
        accessType: 'none',
        error: roleValidation.error
      };
    }

    // Super admins have access to all venues
    if (roleValidation.role === 'super_admin') {
      return {
        hasAccess: true,
        accessType: 'super_admin'
      };
    }

    // Check if admin has access to this specific venue
    if (roleValidation.role === 'admin') {
      const hasVenueAccess = roleValidation.venues.includes(venueId);
      return {
        hasAccess: hasVenueAccess,
        accessType: hasVenueAccess ? 'venue_admin' : 'none'
      };
    }

    return {
      hasAccess: false,
      accessType: 'none'
    };

  } catch (error) {
    console.error('Error in verifyVenueAccess:', error);
    return {
      hasAccess: false,
      accessType: 'none',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Security logging utility for admin actions
 * 
 * @param action - Action being performed
 * @param userId - User performing the action
 * @param details - Additional details about the action
 */
export function logSecurityEvent(
  action: string,
  userId: string,
  details: Record<string, unknown> = {}
): void {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    action,
    userId,
    details,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server'
  };

  // For now, log to console. In production, this could be sent to a logging service
  console.log('[SECURITY]', logEntry);
}

/**
 * Validate admin operation before execution
 * This is a wrapper that can be used around sensitive operations
 * 
 * @param userId - User performing the operation
 * @param operation - Description of the operation
 * @param venueId - Optional venue ID for venue-specific operations
 * @returns Validation result
 */
export async function validateAdminOperation(
  userId: string,
  operation: string,
  venueId?: string
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    const roleValidation = await verifyAdminRole(userId);

    if (!roleValidation.isValid) {
      logSecurityEvent('UNAUTHORIZED_ADMIN_ATTEMPT', userId, {
        operation,
        venueId,
        reason: 'Invalid admin role'
      });
      return {
        allowed: false,
        reason: 'Insufficient permissions'
      };
    }

    // If venue-specific operation, check venue access
    if (venueId && roleValidation.role === 'admin') {
      const venueAccess = await verifyVenueAccess(userId, venueId);
      if (!venueAccess.hasAccess) {
        logSecurityEvent('UNAUTHORIZED_VENUE_ACCESS', userId, {
          operation,
          venueId,
          reason: 'No access to venue'
        });
        return {
          allowed: false,
          reason: 'No access to this venue'
        };
      }
    }

    logSecurityEvent('ADMIN_OPERATION_VALIDATED', userId, {
      operation,
      venueId,
      role: roleValidation.role
    });

    return { allowed: true };

  } catch (error) {
    console.error('Error in validateAdminOperation:', error);
    logSecurityEvent('ADMIN_VALIDATION_ERROR', userId, {
      operation,
      venueId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return {
      allowed: false,
      reason: 'Validation error'
    };
  }
}
