/**
 * Centralized logging utility for Grid2Play
 * Provides environment-aware logging with different levels
 *
 * SECURITY FEATURES:
 * - Automatically disables DEBUG/INFO logs in production
 * - Sanitizes sensitive data (tokens, passwords, user IDs)
 * - Prevents auth state logging in production
 * - Configurable via VITE_LOG_LEVEL environment variable
 *
 * USAGE:
 * - Development: All logs visible (DEBUG, INFO, WARN, ERROR)
 * - Production: Only ERROR logs visible for security
 * - Set VITE_LOG_LEVEL=NONE to disable all logging
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: Date;
  component?: string;
}

class Logger {
  private isDevelopment = import.meta.env.DEV;
  private isProduction = import.meta.env.PROD || import.meta.env.MODE === 'production';
  private logLevel = this.getLogLevel();

  private getLogLevel(): LogLevel {
    // Force ERROR level in production to prevent security leaks
    if (this.isProduction) {
      return LogLevel.ERROR;
    }

    // In development, check for explicit log level override
    const envLogLevel = import.meta.env.VITE_LOG_LEVEL;
    if (envLogLevel) {
      switch (envLogLevel.toUpperCase()) {
        case 'DEBUG': return LogLevel.DEBUG;
        case 'INFO': return LogLevel.INFO;
        case 'WARN': return LogLevel.WARN;
        case 'ERROR': return LogLevel.ERROR;
        case 'NONE': return LogLevel.ERROR + 1; // Disable all logging
        default: return LogLevel.DEBUG;
      }
    }

    // Default to DEBUG in development, ERROR in production
    return this.isDevelopment ? LogLevel.DEBUG : LogLevel.ERROR;
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString();
    const level = LogLevel[entry.level];
    const component = entry.component ? `[${entry.component}]` : '';
    return `${timestamp} ${level} ${component} ${entry.message}`;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private sanitizeData(data: any): any {
    // In production, don't log sensitive data
    if (this.isProduction) {
      return undefined;
    }

    // In development, still sanitize sensitive fields
    if (data && typeof data === 'object') {
      const sanitized = { ...data };

      // Remove sensitive fields
      const sensitiveFields = ['password', 'token', 'access_token', 'refresh_token', 'session', 'user_id', 'id'];
      sensitiveFields.forEach(field => {
        if (sanitized[field]) {
          sanitized[field] = '[REDACTED]';
        }
      });

      return sanitized;
    }

    return data;
  }

  debug(message: string, data?: any, component?: string): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const sanitizedData = this.sanitizeData(data);
      const entry: LogEntry = {
        level: LogLevel.DEBUG,
        message,
        data: sanitizedData,
        timestamp: new Date(),
        component,
      };
      console.log(this.formatMessage(entry), sanitizedData || '');
    }
  }

  info(message: string, data?: any, component?: string): void {
    if (this.shouldLog(LogLevel.INFO)) {
      const sanitizedData = this.sanitizeData(data);
      const entry: LogEntry = {
        level: LogLevel.INFO,
        message,
        data: sanitizedData,
        timestamp: new Date(),
        component,
      };
      console.info(this.formatMessage(entry), sanitizedData || '');
    }
  }

  warn(message: string, data?: any, component?: string): void {
    if (this.shouldLog(LogLevel.WARN)) {
      const sanitizedData = this.sanitizeData(data);
      const entry: LogEntry = {
        level: LogLevel.WARN,
        message,
        data: sanitizedData,
        timestamp: new Date(),
        component,
      };
      console.warn(this.formatMessage(entry), sanitizedData || '');
    }
  }

  error(message: string, error?: Error | any, component?: string): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      // For errors, we still want to log them but sanitize sensitive data
      const sanitizedError = this.sanitizeData(error);
      const entry: LogEntry = {
        level: LogLevel.ERROR,
        message,
        data: sanitizedError,
        timestamp: new Date(),
        component,
      };
      console.error(this.formatMessage(entry), sanitizedError || '');
    }
  }

  // Real-time specific logging
  realtime = {
    setup: (component: string, details?: any) => {
      this.debug(`Setting up realtime subscriptions`, details, component);
    },
    
    cleanup: (component: string, details?: any) => {
      this.debug(`Cleaning up realtime subscriptions`, details, component);
    },
    
    event: (component: string, event: string, payload?: any) => {
      this.debug(`Realtime event: ${event}`, payload, component);
    },
  };

  // Auth specific logging - SECURITY SENSITIVE
  auth = {
    stateChange: (event: string, hasSession: boolean) => {
      // Only log auth events in development to prevent security leaks
      if (!this.isProduction) {
        this.debug(`Auth state changed: ${event}`, { hasSession }, 'AuthContext');
      }
    },

    sessionCheck: (hasSession: boolean) => {
      // Only log session checks in development
      if (!this.isProduction) {
        this.debug(`Initial session check`, { hasSession }, 'AuthContext');
      }
    },

    signOut: () => {
      // Only log sign out in development
      if (!this.isProduction) {
        this.debug(`User signing out`, undefined, 'AuthContext');
      }
    },
  };

  // Production warning
  constructor() {
    if (this.isProduction && this.logLevel < LogLevel.ERROR) {
      console.warn('⚠️ Logger: Debug/Info logs are disabled in production for security');
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Convenience exports for common use cases
export const logRealtime = logger.realtime;
export const logAuth = logger.auth;
