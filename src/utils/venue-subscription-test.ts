/**
 * Test utility to verify venue subscription fixes
 * This helps ensure the PGRST116 error is resolved
 */

import { supabase } from '@/integrations/supabase/client';
import { safeVenueSubscriptionCheck, safeSingleQuery } from './supabase-query-validator';

export interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

/**
 * Test the old problematic query (should fail)
 */
export async function testProblematicQuery(userId: string, venueId: string): Promise<TestResult> {
  try {
    const { data, error } = await supabase
      .from('venue_subscriptions')
      .select('id')
      .eq('user_id', userId)
      .eq('venue_id', venueId)
      .single(); // This should fail with PGRST116 if no rows exist

    return {
      testName: 'Problematic .single() Query',
      passed: false,
      error: 'Expected this to fail but it succeeded',
      details: { data, error }
    };
  } catch (error: any) {
    if (error.message?.includes('PGRST116') || error.message?.includes('JSON object requested')) {
      return {
        testName: 'Problematic .single() Query',
        passed: true,
        details: 'Correctly failed with PGRST116 error'
      };
    }
    return {
      testName: 'Problematic .single() Query',
      passed: false,
      error: `Unexpected error: ${error.message}`,
      details: error
    };
  }
}

/**
 * Test the fixed query using .maybeSingle()
 */
export async function testFixedQuery(userId: string, venueId: string): Promise<TestResult> {
  try {
    const { data, error } = await supabase
      .from('venue_subscriptions')
      .select('id')
      .eq('user_id', userId)
      .eq('venue_id', venueId)
      .maybeSingle(); // This should work correctly

    if (error) {
      return {
        testName: 'Fixed .maybeSingle() Query',
        passed: false,
        error: error.message,
        details: error
      };
    }

    return {
      testName: 'Fixed .maybeSingle() Query',
      passed: true,
      details: { 
        hasSubscription: !!data,
        message: data ? 'User is subscribed' : 'User is not subscribed'
      }
    };
  } catch (error: any) {
    return {
      testName: 'Fixed .maybeSingle() Query',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test the safe utility function
 */
export async function testSafeUtility(userId: string, venueId: string): Promise<TestResult> {
  try {
    const result = await safeVenueSubscriptionCheck(userId, venueId);
    
    return {
      testName: 'Safe Utility Function',
      passed: !result.error,
      error: result.error?.message,
      details: {
        isSubscribed: result.isSubscribed,
        hasError: !!result.error
      }
    };
  } catch (error: any) {
    return {
      testName: 'Safe Utility Function',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Test the generic safe single query function
 */
export async function testSafeSingleQuery(userId: string, venueId: string): Promise<TestResult> {
  try {
    const result = await safeSingleQuery<{ id: string }>(
      'venue_subscriptions',
      'id',
      { user_id: userId, venue_id: venueId }
    );
    
    return {
      testName: 'Generic Safe Single Query',
      passed: !result.error,
      error: result.error?.message,
      details: {
        hasData: !!result.data,
        hasError: !!result.error
      }
    };
  } catch (error: any) {
    return {
      testName: 'Generic Safe Single Query',
      passed: false,
      error: error.message,
      details: error
    };
  }
}

/**
 * Run all venue subscription tests
 */
export async function runVenueSubscriptionTests(
  userId: string = '955e48f6-00eb-4024-987a-1b6237518968',
  venueId: string = '2b0a09fe-d6e4-48e2-892c-831ad53cb4ae'
): Promise<TestResult[]> {
  console.log('🧪 Running venue subscription tests...');
  
  const tests = [
    () => testFixedQuery(userId, venueId),
    () => testSafeUtility(userId, venueId),
    () => testSafeSingleQuery(userId, venueId),
    // Note: We skip the problematic query test to avoid errors in production
  ];

  const results: TestResult[] = [];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
      console.log(`${result.passed ? '✅' : '❌'} ${result.testName}: ${result.passed ? 'PASSED' : 'FAILED'}`);
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    } catch (error) {
      results.push({
        testName: 'Unknown Test',
        passed: false,
        error: `Test execution failed: ${error}`,
        details: error
      });
    }
  }

  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Venue subscription queries are working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the details above.');
  }

  return results;
}

/**
 * Quick test function for development
 */
export async function quickVenueSubscriptionTest(): Promise<boolean> {
  try {
    // Use test IDs that are likely to have no subscription
    const testUserId = '00000000-0000-0000-0000-000000000001';
    const testVenueId = '00000000-0000-0000-0000-000000000002';
    
    const result = await safeVenueSubscriptionCheck(testUserId, testVenueId);
    
    // Should return false for subscription but no error
    return !result.error && !result.isSubscribed;
  } catch (error) {
    console.error('Quick test failed:', error);
    return false;
  }
}
