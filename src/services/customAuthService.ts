import { supabase } from '@/integrations/supabase/client';
import { msg91EmailService } from './msg91EmailService';
import { v4 as uuidv4 } from 'uuid';

export interface SignUpData {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

export interface VerificationResult {
  success: boolean;
  error?: string;
  user?: any;
}

class CustomAuthService {
  /**
   * Generate a secure verification token
   */
  private generateVerificationToken(): string {
    return uuidv4().replace(/-/g, '') + Date.now().toString(36);
  }

  /**
   * Create verification link
   */
  private createVerificationLink(token: string): string {
    const baseUrl = window.location.origin;
    return `${baseUrl}/verify-email-token?token=${token}`;
  }

  /**
   * Sign up user with MSG91 email verification
   */
  async signUpWithCustomEmail(userData: SignUpData): Promise<{ error: any }> {
    try {
      // First, check if user already exists
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('id, email')
        .eq('email', userData.email.toLowerCase())
        .maybeSingle();

      if (existingUser) {
        return { error: { message: 'User with this email already exists' } };
      }

      // Generate verification token
      const verificationToken = this.generateVerificationToken();
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Store pending user data
      const { error: pendingError } = await supabase
        .from('pending_users')
        .insert({
          email: userData.email.toLowerCase(),
          password_hash: userData.password, // In production, hash this properly
          full_name: userData.name,
          phone: userData.phone,
          verification_token: verificationToken,
          expires_at: expiresAt.toISOString()
        });

      if (pendingError) {
        console.error('Error storing pending user:', pendingError);
        return { error: pendingError };
      }

      // Create verification link
      const verificationLink = this.createVerificationLink(verificationToken);

      // Send verification email via MSG91
      const emailResult = await msg91EmailService.sendVerificationEmail(
        userData.email,
        userData.name,
        verificationLink,
        false
      );

      if (!emailResult.success) {
        // Clean up pending user if email fails
        await supabase
          .from('pending_users')
          .delete()
          .eq('verification_token', verificationToken);

        return { error: { message: emailResult.error || 'Failed to send verification email' } };
      }

      return { error: null };
    } catch (error) {
      console.error('Error in signUpWithCustomEmail:', error);
      return { error };
    }
  }

  /**
   * Verify email token and complete user registration
   */
  async verifyEmailToken(token: string): Promise<VerificationResult> {
    try {
      console.log('🔍 Starting email verification for token:', token.substring(0, 10) + '...');

      // Get pending user data
      const { data: pendingUser, error: fetchError } = await supabase
        .from('pending_users')
        .select('*')
        .eq('verification_token', token)
        .maybeSingle();

      console.log('📊 Pending user lookup result:', {
        found: !!pendingUser,
        error: fetchError?.message,
        email: pendingUser?.email,
        expiresAt: pendingUser?.expires_at
      });

      if (fetchError || !pendingUser) {
        console.error('❌ Pending user not found:', fetchError);
        return { success: false, error: 'Invalid or expired verification token' };
      }

      // Check if token is expired
      const now = new Date();
      const expiresAt = new Date(pendingUser.expires_at);
      const isExpired = expiresAt < now;

      console.log('⏰ Token expiration check:', {
        now: now.toISOString(),
        expiresAt: expiresAt.toISOString(),
        isExpired: isExpired
      });

      if (isExpired) {
        console.log('⚠️ Token expired, cleaning up...');
        // Clean up expired token
        await supabase
          .from('pending_users')
          .delete()
          .eq('verification_token', token);

        return { success: false, error: 'Verification token has expired' };
      }

      console.log('🚀 Calling create-verified-user Edge Function...');

      // Create the actual Supabase user with email pre-confirmed using admin API
      const { data: createUserResult, error: createUserError } = await supabase.functions.invoke('create-verified-user', {
        body: {
          email: pendingUser.email,
          password: pendingUser.password_hash,
          userData: {
            full_name: pendingUser.full_name,
            phone: pendingUser.phone
          }
        }
      });

      console.log('📊 Edge Function response:', {
        success: createUserResult?.success,
        error: createUserError?.message || createUserResult?.error,
        hasUser: !!createUserResult?.user
      });

      if (createUserError || !createUserResult?.success) {
        console.error('❌ Error creating verified user:', {
          edgeFunctionError: createUserError,
          responseError: createUserResult?.error,
          fullResponse: createUserResult
        });
        return {
          success: false,
          error: createUserError?.message || createUserResult?.error || 'Failed to create user account'
        };
      }

      console.log('Verified user created successfully:', createUserResult.user);

      // Clean up pending user
      await supabase
        .from('pending_users')
        .delete()
        .eq('verification_token', token);

      return { success: true, user: createUserResult.user };
    } catch (error) {
      console.error('Error in verifyEmailToken:', error);
      return { success: false, error: 'Verification failed' };
    }
  }

  /**
   * Resend verification email
   */
  async resendVerificationEmail(email: string): Promise<{ error: any }> {
    try {
      // Get pending user
      const { data: pendingUser, error: fetchError } = await supabase
        .from('pending_users')
        .select('*')
        .eq('email', email.toLowerCase())
        .maybeSingle();

      if (fetchError || !pendingUser) {
        return { error: { message: 'No pending verification found for this email' } };
      }

      // Generate new token
      const verificationToken = this.generateVerificationToken();
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);

      // Update pending user with new token
      const { error: updateError } = await supabase
        .from('pending_users')
        .update({
          verification_token: verificationToken,
          expires_at: expiresAt.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('email', email.toLowerCase());

      if (updateError) {
        return { error: updateError };
      }

      // Create verification link
      const verificationLink = this.createVerificationLink(verificationToken);

      // Send verification email via MSG91
      const emailResult = await msg91EmailService.sendVerificationEmail(
        email,
        pendingUser.full_name || 'User',
        verificationLink,
        true // isResend = true
      );

      if (!emailResult.success) {
        return { error: { message: emailResult.error || 'Failed to send verification email' } };
      }

      return { error: null };
    } catch (error) {
      console.error('Error in resendVerificationEmail:', error);
      return { error };
    }
  }

  /**
   * Send password reset email via MSG91
   */
  async sendPasswordResetEmail(email: string): Promise<{ error: any }> {
    try {
      // Check if user exists
      const { data: profile } = await supabase
        .from('profiles')
        .select('full_name')
        .eq('email', email.toLowerCase())
        .maybeSingle();

      if (!profile) {
        // Don't reveal if user exists or not for security
        return { error: null };
      }

      // Use Supabase's built-in password reset but intercept the email
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (resetError) {
        return { error: resetError };
      }

      // Note: In a full implementation, you'd need to intercept Supabase's email
      // and send via MSG91 instead. This requires webhook setup or custom SMTP.
      
      return { error: null };
    } catch (error) {
      console.error('Error in sendPasswordResetEmail:', error);
      return { error };
    }
  }
}

export const customAuthService = new CustomAuthService();
