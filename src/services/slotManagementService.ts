import { supabase } from '@/integrations/supabase/client';
import SlotManagementPermissions from '@/utils/slotManagementPermissions';

export interface BlockedSlot {
  id: string;
  court_id: string;
  court_name: string;
  venue_id: string;
  venue_name: string;
  date: string;
  start_time: string;
  end_time: string;
  reason: string;
  created_by: string;
  created_by_name: string;
  created_at: string;
}

export interface SlotManagementStats {
  total_blocked_slots: number;
  venues_with_blocks: number;
  courts_with_blocks: number;
  date_range: {
    start_date: string;
    end_date: string;
  };
}

export interface BulkBlockRequest {
  court_ids: string[];
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  reason?: string;
  created_by?: string;
}

export interface VenueBlockRequest {
  venue_id: string;
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  reason?: string;
  created_by?: string;
}

export interface FilterOptions {
  venue_ids?: string[];
  court_ids?: string[];
  start_date?: string;
  end_date?: string;
  created_by_ids?: string[];
  reason_filter?: string;
  limit?: number;
  offset?: number;
}

export class SlotManagementService {
  /**
   * Get blocked slots with detailed information
   */
  static async getBlockedSlotsWithDetails(
    venue_id?: string,
    court_id?: string,
    start_date?: string,
    end_date?: string,
    created_by?: string
  ): Promise<{ data: BlockedSlot[] | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_blocked_slots_with_details', {
        p_venue_id: venue_id || null,
        p_court_id: court_id || null,
        p_start_date: start_date || null,
        p_end_date: end_date || null,
        p_created_by: created_by || null
      });

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get blocked slots with advanced filtering
   */
  static async getFilteredBlockedSlots(
    filters: FilterOptions
  ): Promise<{ data: (BlockedSlot & { total_count: number })[] | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_filtered_blocked_slots', {
        p_venue_ids: filters.venue_ids || null,
        p_court_ids: filters.court_ids || null,
        p_start_date: filters.start_date || null,
        p_end_date: filters.end_date || null,
        p_created_by_ids: filters.created_by_ids || null,
        p_reason_filter: filters.reason_filter || null,
        p_limit: filters.limit || 100,
        p_offset: filters.offset || 0
      });

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get slot management statistics
   */
  static async getSlotManagementStats(
    venue_id?: string,
    start_date?: string,
    end_date?: string
  ): Promise<{ data: SlotManagementStats | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_slot_management_stats', {
        p_venue_id: venue_id || null,
        p_start_date: start_date || null,
        p_end_date: end_date || null
      });

      if (error) return { data: null, error };
      
      return { 
        data: data?.success ? data.stats : null, 
        error: data?.success ? null : data?.error 
      };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Bulk block slots across multiple courts
   */
  static async bulkBlockSlots(
    request: BulkBlockRequest,
    userId?: string
  ): Promise<{ success: boolean; message?: string; error?: string; blocked_count?: number }> {
    try {
      // Check permissions if userId provided
      if (userId) {
        const permissionCheck = await SlotManagementPermissions.canPerformBulkOperations(userId);
        if (!permissionCheck.allowed) {
          return {
            success: false,
            error: permissionCheck.reason || 'Insufficient permissions for bulk operations'
          };
        }
      }

      const { data, error } = await supabase.rpc('bulk_block_slots', {
        p_court_ids: request.court_ids,
        p_start_date: request.start_date,
        p_end_date: request.end_date,
        p_start_time: request.start_time,
        p_end_time: request.end_time,
        p_reason: request.reason || 'Bulk blocked by admin',
        p_created_by: request.created_by || null
      });

      if (error) return { success: false, error: error.message };

      // Log the action if successful and userId provided
      if (data?.success && userId) {
        await SlotManagementPermissions.logSlotManagementAction(userId, 'bulk_block_slots', {
          court_ids: request.court_ids,
          date_range: `${request.start_date} to ${request.end_date}`,
          time_range: `${request.start_time} to ${request.end_time}`,
          reason: request.reason,
          blocked_count: data.blocked_count
        });
      }

      return data || { success: false, error: 'Unknown error' };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Block all slots for a venue on specific dates
   */
  static async blockVenueSlots(
    request: VenueBlockRequest
  ): Promise<{ success: boolean; message?: string; error?: string; blocked_count?: number }> {
    try {
      const { data, error } = await supabase.rpc('block_venue_slots', {
        p_venue_id: request.venue_id,
        p_start_date: request.start_date,
        p_end_date: request.end_date,
        p_start_time: request.start_time || '00:00',
        p_end_time: request.end_time || '23:59',
        p_reason: request.reason || 'Venue maintenance',
        p_created_by: request.created_by || null
      });

      if (error) return { success: false, error: error.message };
      
      return data || { success: false, error: 'Unknown error' };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Unblock multiple slots
   */
  static async unblockSlots(
    blocked_slot_ids: string[],
    userId?: string
  ): Promise<{ success: boolean; message?: string; error?: string; unblocked_count?: number }> {
    try {
      // Check permissions if userId provided
      if (userId) {
        const permissionCheck = await SlotManagementPermissions.canUnblockSlots(userId, blocked_slot_ids);
        if (!permissionCheck.allowed) {
          return {
            success: false,
            error: permissionCheck.reason || 'Insufficient permissions to unblock these slots'
          };
        }
      }

      const { data, error } = await supabase.rpc('unblock_slots', {
        p_blocked_slot_ids: blocked_slot_ids
      });

      if (error) return { success: false, error: error.message };

      // Log the action if successful and userId provided
      if (data?.success && userId) {
        await SlotManagementPermissions.logSlotManagementAction(userId, 'unblock_slots', {
          slot_ids: blocked_slot_ids,
          unblocked_count: data.unblocked_count
        });
      }

      return data || { success: false, error: 'Unknown error' };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if a specific slot is blocked
   */
  static async isSlotBlocked(
    court_id: string,
    date: string,
    start_time: string,
    end_time: string
  ): Promise<{ blocked: boolean; reason?: string; error?: any }> {
    try {
      const { data, error } = await supabase
        .from('blocked_slots')
        .select('reason')
        .eq('court_id', court_id)
        .eq('date', date)
        .eq('start_time', start_time)
        .eq('end_time', end_time)
        .single();

      if (error && error.code !== 'PGRST116') {
        return { blocked: false, error };
      }

      return { 
        blocked: !!data, 
        reason: data?.reason 
      };
    } catch (error) {
      return { blocked: false, error };
    }
  }

  /**
   * Get venues with their court information for slot management
   */
  static async getVenuesWithCourts(): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('venues')
        .select(`
          id,
          name,
          location,
          courts (
            id,
            name,
            venue_id,
            court_group_id,
            is_active
          )
        `)
        .eq('is_active', true)
        .eq('courts.is_active', true)
        .order('name');

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get blocked slots for a specific date range (for calendar view)
   */
  static async getBlockedSlotsForDateRange(
    start_date: string,
    end_date: string,
    venue_id?: string
  ): Promise<{ data: BlockedSlot[] | null; error: any }> {
    try {
      let query = supabase
        .from('blocked_slots')
        .select(`
          id,
          court_id,
          date,
          start_time,
          end_time,
          reason,
          created_by,
          created_at,
          courts (
            name,
            venue_id,
            venues (
              name
            )
          )
        `)
        .gte('date', start_date)
        .lte('date', end_date)
        .order('date')
        .order('start_time');

      if (venue_id) {
        query = query.eq('courts.venue_id', venue_id);
      }

      const { data, error } = await query;

      if (error) return { data: null, error };

      // Transform the data to match BlockedSlot interface
      const transformedData = data?.map((slot: any) => ({
        id: slot.id,
        court_id: slot.court_id,
        court_name: slot.courts?.name || 'Unknown Court',
        venue_id: slot.courts?.venue_id || '',
        venue_name: slot.courts?.venues?.name || 'Unknown Venue',
        date: slot.date,
        start_time: slot.start_time,
        end_time: slot.end_time,
        reason: slot.reason,
        created_by: slot.created_by,
        created_by_name: '', // Would need to join with profiles table
        created_at: slot.created_at
      })) || [];

      return { data: transformedData, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }
}

export default SlotManagementService;
