import { supabase } from '@/integrations/supabase/client';

// Booking email data interface for type safety
interface BookingEmailData {
  booking_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: string;
  status: string;
  payment_reference: string;
  booking_created_at: string;
  confirmation_email_sent: boolean;
  confirmation_email_sent_at: string | null;
  confirmation_email_error: string | null;
  user_id: string | null;
  user_name: string;
  user_email: string;
  user_phone: string;
  guest_name: string | null;
  guest_phone: string | null;
  court_id: string;
  court_name: string;
  venue_id: string;
  venue_name: string;
  venue_address: string;
  venue_phone: string;
  sport_id: string;
  sport_name: string;
  duration_hours: number;
  booking_reference: string;
  recipient_email: string;
  recipient_name: string;
  formatted_date: string;
  formatted_start_time: string;
  formatted_end_time: string;
  formatted_duration: string;
}

// Booking update interface for email tracking
interface BookingEmailUpdate {
  confirmation_email_sent: boolean;
  confirmation_email_sent_at?: string;
  confirmation_email_error?: string | null;
}

export interface MSG91EmailRequest {
  to: string;
  name: string;
  templateId: 'authentication_grid2play' | 'resend_grid2play' | 'booking_confirmation_grid2play' | 'settlement_notification_grid2play2';
  variables: {
    firstName?: string;
    name?: string;
    verificationLink?: string;
    // Booking confirmation variables
    userName?: string;
    bookingReference?: string;
    courtName?: string;
    venueName?: string;
    bookingDate?: string;
    startTime?: string;
    endTime?: string;
    duration?: string;
    venueAddress?: string;
    venuePhone?: string;
    viewBookingLink?: string;
    venueDirectionsLink?: string;
    totalPrice?: string;
    sportName?: string;
    // Settlement notification variables
    venueAdminName?: string;
    settlementReference?: string;
    settlementPeriod?: string;
    totalBookings?: string;
    grossRevenue?: string;
    platformFees?: string;
    netRevenue?: string;
    dashboardLink?: string;
  };
}

export interface BookingConfirmationRequest {
  to: string;
  name: string;
  templateId: 'booking_confirmation_grid2play';
  variables: {
    userName: string;
    bookingReference: string;
    courtName: string;
    venueName: string;
    bookingDate: string;
    startTime: string;
    endTime: string;
    duration: string;
    venueAddress: string;
    venuePhone: string;
    viewBookingLink: string;
    venueDirectionsLink: string;
    totalPrice: string;
    sportName: string;
  };
}

export interface SettlementNotificationRequest {
  to: string;
  name: string;
  templateId: 'settlement_notification_grid2play2';
  variables: {
    venueAdminName: string;
    venueName: string;
    settlementReference: string;
    settlementPeriod: string;
    totalBookings: string;
    grossRevenue: string;
    platformFees: string;
    netRevenue: string;
    dashboardLink: string;
  };
}

export interface MSG91Config {
  authKey: string;
  domain: string;
}

class MSG91EmailService {
  private config: MSG91Config | null = null;

  private async getConfig(): Promise<MSG91Config> {
    if (this.config) {
      return this.config;
    }

    try {
      // Get MSG91 configuration from Supabase secrets
      const { data, error } = await supabase.functions.invoke('get-msg91-config', {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (error) {
        console.error('Failed to get MSG91 config:', error);
        console.error('Error details:', error);
        throw new Error(`MSG91 configuration not available: ${error.message}`);
      }

      if (!data || !data.authKey || !data.domain) {
        console.error('Invalid MSG91 config response:', data);
        throw new Error('MSG91 configuration incomplete');
      }

      this.config = {
        authKey: data.authKey,
        domain: data.domain
      };

      console.log('MSG91 config loaded successfully');
      return this.config;
    } catch (error) {
      console.error('Error getting MSG91 config:', error);
      throw new Error(`Failed to initialize MSG91 service: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async sendEmail(request: MSG91EmailRequest): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Sending email via MSG91 Edge Function:', { to: request.to, templateId: request.templateId });

      // Use Supabase Edge Function to send email (avoids CORS issues)
      const { data, error } = await supabase.functions.invoke('send-msg91-email', {
        body: {
          to: request.to,
          name: request.name,
          templateId: request.templateId,
          variables: request.variables
        }
      });

      if (error) {
        console.error('MSG91 Edge Function error:', error);
        return {
          success: false,
          error: `Failed to send email: ${error.message}`
        };
      }

      if (!data.success) {
        console.error('MSG91 API error:', data);
        return {
          success: false,
          error: data.error || 'Unknown MSG91 error'
        };
      }

      console.log('Email sent successfully via MSG91:', data);
      return { success: true };
    } catch (error) {
      console.error('Error sending email via MSG91:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async sendVerificationEmail(
    email: string, 
    name: string, 
    verificationLink: string,
    isResend: boolean = false
  ): Promise<{ success: boolean; error?: string }> {
    const templateId = isResend ? 'resend_grid2play' : 'authentication_grid2play';
    
    const variables = isResend 
      ? { name, verificationLink }
      : { firstName: name, verificationLink };

    return this.sendEmail({
      to: email,
      name,
      templateId,
      variables
    });
  }

  async sendPasswordResetEmail(
    email: string,
    name: string,
    resetLink: string
  ): Promise<{ success: boolean; error?: string }> {
    // For now, use the resend template for password reset
    // You can create a dedicated template later
    return this.sendEmail({
      to: email,
      name,
      templateId: 'resend_grid2play',
      variables: {
        name,
        verificationLink: resetLink
      }
    });
  }

  async sendSettlementNotificationEmail(
    request: SettlementNotificationRequest
  ): Promise<{ success: boolean; error?: string }> {
    console.log('Sending settlement notification email:', {
      to: request.to,
      settlementReference: request.variables.settlementReference
    });

    return this.sendEmail({
      to: request.to,
      name: request.name,
      templateId: request.templateId,
      variables: request.variables
    });
  }

  async sendBookingConfirmationEmail(
    bookingId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Fetching booking data for confirmation email:', bookingId);

      // Fetch booking data from our security definer function
      // @ts-expect-error - Custom RPC function not in generated types
      const { data: bookingData, error: fetchError } = await supabase
        .rpc('get_booking_email_data', { booking_id_param: bookingId });

      if (fetchError || !bookingData || (bookingData as BookingEmailData[]).length === 0) {
        console.error('Failed to fetch booking data:', fetchError);
        return { success: false, error: 'Booking not found or not eligible for email' };
      }

      const booking = (bookingData as BookingEmailData[])[0]; // Function returns array

      // Check if email already sent
      if (booking.confirmation_email_sent) {
        console.log('Confirmation email already sent for booking:', bookingId);
        return { success: true, error: 'Email already sent' };
      }

      // Only send to registered users with email addresses
      if (!booking.user_id || !booking.user_email) {
        console.log('Booking not eligible for email - no user or email:', {
          user_id: booking.user_id,
          has_email: !!booking.user_email
        });
        return { success: false, error: 'No email address available for this booking' };
      }

      // Prepare template variables
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://grid2play.com';
      const templateVariables = {
        userName: booking.recipient_name,
        bookingReference: booking.booking_reference,
        courtName: booking.court_name,
        venueName: booking.venue_name,
        bookingDate: booking.formatted_date,
        startTime: booking.formatted_start_time,
        endTime: booking.formatted_end_time,
        duration: booking.formatted_duration,
        venueAddress: booking.venue_address || 'Address not available',
        venuePhone: booking.venue_phone || 'Contact venue directly',
        viewBookingLink: `${baseUrl}/bookings`,
        venueDirectionsLink: `https://maps.google.com/?q=${encodeURIComponent(booking.venue_address || booking.venue_name)}`,
        totalPrice: `₹${booking.total_price}`,
        sportName: booking.sport_name || 'Sport'
      };

      console.log('Sending booking confirmation email:', {
        to: booking.user_email,
        bookingReference: booking.booking_reference,
        venueName: booking.venue_name
      });

      // Send email using existing sendEmail method
      const emailResult = await this.sendEmail({
        to: booking.user_email,
        name: booking.recipient_name,
        templateId: 'booking_confirmation_grid2play',
        variables: templateVariables
      });

      // Update booking record with email status
      const updateData: BookingEmailUpdate = emailResult.success
        ? {
            confirmation_email_sent: true,
            confirmation_email_sent_at: new Date().toISOString(),
            confirmation_email_error: null
          }
        : {
            confirmation_email_sent: false,
            confirmation_email_error: emailResult.error || 'Unknown error'
          };

      const { error: updateError } = await supabase
        .from('bookings')
        .update(updateData as unknown as Record<string, unknown>)
        .eq('id', bookingId);

      if (updateError) {
        console.error('Failed to update booking email status:', updateError);
        // Don't fail the whole operation if status update fails
      }

      return emailResult;
    } catch (error) {
      console.error('Error in sendBookingConfirmationEmail:', error);

      // Log error to booking record
      try {
        const errorUpdate: BookingEmailUpdate = {
          confirmation_email_sent: false,
          confirmation_email_error: error instanceof Error ? error.message : 'Unknown error'
        };
        await supabase
          .from('bookings')
          .update(errorUpdate as unknown as Record<string, unknown>)
          .eq('id', bookingId);
      } catch (updateError) {
        console.error('Failed to log error to booking record:', updateError);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

export const msg91EmailService = new MSG91EmailService();
