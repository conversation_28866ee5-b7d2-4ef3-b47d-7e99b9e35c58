import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { supabase } from '@/integrations/supabase/client';
import SlotManagementService from '@/services/slotManagementService';
import SlotManagementPermissions from '@/utils/slotManagementPermissions';

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          order: vi.fn()
        })),
        in: vi.fn(() => ({
          single: vi.fn()
        })),
        insert: vi.fn(),
        update: vi.fn(),
        delete: vi.fn()
      }))
    }))
  }
}));

describe('SlotManagementService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getBlockedSlotsWithDetails', () => {
    it('should fetch blocked slots with venue and court details', async () => {
      const mockData = [
        {
          id: '123',
          court_id: 'court-1',
          court_name: 'Court 1',
          venue_id: 'venue-1',
          venue_name: 'Test Venue',
          date: '2024-01-15',
          start_time: '09:00',
          end_time: '10:00',
          reason: 'Maintenance',
          created_by: 'admin-1',
          created_by_name: 'Admin User',
          created_at: '2024-01-14T10:00:00Z'
        }
      ];

      (supabase.rpc as any).mockResolvedValue({
        data: mockData,
        error: null
      });

      const result = await SlotManagementService.getBlockedSlotsWithDetails(
        'venue-1',
        'court-1',
        '2024-01-15',
        '2024-01-15'
      );

      expect(result.data).toEqual(mockData);
      expect(result.error).toBeNull();
      expect(supabase.rpc).toHaveBeenCalledWith('get_blocked_slots_with_details', {
        p_venue_id: 'venue-1',
        p_court_id: 'court-1',
        p_start_date: '2024-01-15',
        p_end_date: '2024-01-15',
        p_created_by: null
      });
    });

    it('should handle errors gracefully', async () => {
      const mockError = new Error('Database connection failed');
      (supabase.rpc as any).mockResolvedValue({
        data: null,
        error: mockError
      });

      const result = await SlotManagementService.getBlockedSlotsWithDetails();

      expect(result.data).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });

  describe('bulkBlockSlots', () => {
    it('should successfully block multiple slots', async () => {
      const mockResponse = {
        success: true,
        blocked_count: 5,
        message: 'Successfully blocked 5 slots'
      };

      (supabase.rpc as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const request = {
        court_ids: ['court-1', 'court-2'],
        start_date: '2024-01-15',
        end_date: '2024-01-17',
        start_time: '09:00',
        end_time: '10:00',
        reason: 'Maintenance work'
      };

      const result = await SlotManagementService.bulkBlockSlots(request);

      expect(result.success).toBe(true);
      expect(result.blocked_count).toBe(5);
      expect(supabase.rpc).toHaveBeenCalledWith('bulk_block_slots', {
        p_court_ids: request.court_ids,
        p_start_date: request.start_date,
        p_end_date: request.end_date,
        p_start_time: request.start_time,
        p_end_time: request.end_time,
        p_reason: request.reason,
        p_created_by: null
      });
    });

    it('should check permissions when userId is provided', async () => {
      const mockPermissionCheck = {
        allowed: false,
        reason: 'Insufficient permissions'
      };

      vi.spyOn(SlotManagementPermissions, 'canPerformBulkOperations')
        .mockResolvedValue(mockPermissionCheck);

      const request = {
        court_ids: ['court-1'],
        start_date: '2024-01-15',
        end_date: '2024-01-15',
        start_time: '09:00',
        end_time: '10:00'
      };

      const result = await SlotManagementService.bulkBlockSlots(request, 'user-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Insufficient permissions');
      expect(supabase.rpc).not.toHaveBeenCalled();
    });
  });

  describe('unblockSlots', () => {
    it('should successfully unblock multiple slots', async () => {
      const mockResponse = {
        success: true,
        unblocked_count: 3,
        message: 'Successfully unblocked 3 slots'
      };

      (supabase.rpc as any).mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const slotIds = ['slot-1', 'slot-2', 'slot-3'];
      const result = await SlotManagementService.unblockSlots(slotIds);

      expect(result.success).toBe(true);
      expect(result.unblocked_count).toBe(3);
      expect(supabase.rpc).toHaveBeenCalledWith('unblock_slots', {
        p_blocked_slot_ids: slotIds
      });
    });

    it('should check permissions when userId is provided', async () => {
      const mockPermissionCheck = {
        allowed: false,
        reason: 'No access to these slots'
      };

      vi.spyOn(SlotManagementPermissions, 'canUnblockSlots')
        .mockResolvedValue(mockPermissionCheck);

      const slotIds = ['slot-1', 'slot-2'];
      const result = await SlotManagementService.unblockSlots(slotIds, 'user-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('No access to these slots');
      expect(supabase.rpc).not.toHaveBeenCalled();
    });
  });

  describe('isSlotBlocked', () => {
    it('should return true when slot is blocked', async () => {
      const mockData = {
        reason: 'Maintenance'
      };

      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: mockData,
                    error: null
                  })
                })
              })
            })
          })
        })
      });

      const result = await SlotManagementService.isSlotBlocked(
        'court-1',
        '2024-01-15',
        '09:00',
        '10:00'
      );

      expect(result.blocked).toBe(true);
      expect(result.reason).toBe('Maintenance');
    });

    it('should return false when slot is not blocked', async () => {
      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: null,
                    error: { code: 'PGRST116' } // No rows found
                  })
                })
              })
            })
          })
        })
      });

      const result = await SlotManagementService.isSlotBlocked(
        'court-1',
        '2024-01-15',
        '09:00',
        '10:00'
      );

      expect(result.blocked).toBe(false);
    });
  });
});

describe('SlotManagementPermissions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getUserPermissions', () => {
    it('should return super admin permissions', async () => {
      (supabase.from as any).mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { role: 'super_admin' },
              error: null
            })
          })
        })
      });

      const permissions = await SlotManagementPermissions.getUserPermissions('user-1');

      expect(permissions).toEqual({
        canViewBlockedSlots: true,
        canBlockSlots: true,
        canUnblockSlots: true,
        canBulkBlockSlots: true,
        canBlockVenueSlots: true,
        canViewAllVenues: true,
        canViewSlotManagementDashboard: true,
        canManageSlotPermissions: true,
        allowedVenueIds: [],
        role: 'super_admin'
      });
    });

    it('should return regular admin permissions with venue restrictions', async () => {
      (supabase.from as any)
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: { role: 'admin' },
                error: null
              })
            })
          })
        })
        .mockReturnValueOnce({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockResolvedValue({
              data: [{ venue_id: 'venue-1' }, { venue_id: 'venue-2' }],
              error: null
            })
          })
        });

      const permissions = await SlotManagementPermissions.getUserPermissions('user-1');

      expect(permissions).toEqual({
        canViewBlockedSlots: true,
        canBlockSlots: true,
        canUnblockSlots: true,
        canBulkBlockSlots: false,
        canBlockVenueSlots: false,
        canViewAllVenues: false,
        canViewSlotManagementDashboard: false,
        canManageSlotPermissions: false,
        allowedVenueIds: ['venue-1', 'venue-2'],
        role: 'admin'
      });
    });
  });

  describe('canBlockSlotsForVenue', () => {
    it('should allow super admin to block slots for any venue', async () => {
      vi.spyOn(SlotManagementPermissions, 'getUserPermissions')
        .mockResolvedValue({
          canViewBlockedSlots: true,
          canBlockSlots: true,
          canUnblockSlots: true,
          canBulkBlockSlots: true,
          canBlockVenueSlots: true,
          canViewAllVenues: true,
          canViewSlotManagementDashboard: true,
          canManageSlotPermissions: true,
          allowedVenueIds: [],
          role: 'super_admin'
        });

      const result = await SlotManagementPermissions.canBlockSlotsForVenue('user-1', 'venue-1');

      expect(result.allowed).toBe(true);
    });

    it('should restrict regular admin to their assigned venues', async () => {
      vi.spyOn(SlotManagementPermissions, 'getUserPermissions')
        .mockResolvedValue({
          canViewBlockedSlots: true,
          canBlockSlots: true,
          canUnblockSlots: true,
          canBulkBlockSlots: false,
          canBlockVenueSlots: false,
          canViewAllVenues: false,
          canViewSlotManagementDashboard: false,
          canManageSlotPermissions: false,
          allowedVenueIds: ['venue-1'],
          role: 'admin'
        });

      const allowedResult = await SlotManagementPermissions.canBlockSlotsForVenue('user-1', 'venue-1');
      expect(allowedResult.allowed).toBe(true);

      const deniedResult = await SlotManagementPermissions.canBlockSlotsForVenue('user-1', 'venue-2');
      expect(deniedResult.allowed).toBe(false);
      expect(deniedResult.reason).toBe('No access to this venue');
    });
  });
});
