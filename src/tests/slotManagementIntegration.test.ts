import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { supabase } from '@/integrations/supabase/client';
import SlotManagementService from '@/services/slotManagementService';

// Integration tests - these require a test database
describe('Slot Management Integration Tests', () => {
  let testVenueId: string;
  let testCourtIds: string[] = [];
  let testUserId: string;
  let createdSlotIds: string[] = [];

  beforeAll(async () => {
    // Create test data
    await setupTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
  });

  beforeEach(async () => {
    // Clear any blocked slots created during tests
    if (createdSlotIds.length > 0) {
      await supabase
        .from('blocked_slots')
        .delete()
        .in('id', createdSlotIds);
      createdSlotIds = [];
    }
  });

  const setupTestData = async () => {
    // Create test venue
    const { data: venue, error: venueError } = await supabase
      .from('venues')
      .insert({
        name: 'Test Venue for Slot Management',
        location: 'Test Location',
        is_active: true
      })
      .select()
      .single();

    if (venueError) throw venueError;
    testVenueId = venue.id;

    // Create test courts
    const courtsData = [
      { name: 'Test Court 1', venue_id: testVenueId, is_active: true },
      { name: 'Test Court 2', venue_id: testVenueId, is_active: true }
    ];

    const { data: courts, error: courtsError } = await supabase
      .from('courts')
      .insert(courtsData)
      .select();

    if (courtsError) throw courtsError;
    testCourtIds = courts.map(court => court.id);

    // Create test user
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .insert({
        full_name: 'Test Admin User',
        email: '<EMAIL>'
      })
      .select()
      .single();

    if (userError) throw userError;
    testUserId = user.id;

    // Assign super admin role
    await supabase
      .from('user_roles')
      .insert({
        user_id: testUserId,
        role: 'super_admin'
      });
  };

  const cleanupTestData = async () => {
    // Delete in reverse order due to foreign key constraints
    await supabase.from('blocked_slots').delete().in('court_id', testCourtIds);
    await supabase.from('user_roles').delete().eq('user_id', testUserId);
    await supabase.from('courts').delete().in('id', testCourtIds);
    await supabase.from('venues').delete().eq('id', testVenueId);
    await supabase.from('profiles').delete().eq('id', testUserId);
  };

  describe('Bulk Slot Blocking', () => {
    it('should successfully block multiple slots across courts', async () => {
      const request = {
        court_ids: testCourtIds,
        start_date: '2024-06-25',
        end_date: '2024-06-27',
        start_time: '09:00',
        end_time: '10:00',
        reason: 'Integration test blocking',
        created_by: testUserId
      };

      const result = await SlotManagementService.bulkBlockSlots(request, testUserId);

      expect(result.success).toBe(true);
      expect(result.blocked_count).toBeGreaterThan(0);
      expect(result.message).toContain('Successfully blocked');

      // Verify slots were actually created
      const { data: blockedSlots } = await supabase
        .from('blocked_slots')
        .select('*')
        .in('court_id', testCourtIds)
        .eq('start_time', '09:00')
        .eq('end_time', '10:00')
        .gte('date', '2024-06-25')
        .lte('date', '2024-06-27');

      expect(blockedSlots).toBeDefined();
      expect(blockedSlots!.length).toBe(result.blocked_count);

      // Store IDs for cleanup
      createdSlotIds = blockedSlots!.map(slot => slot.id);
    });

    it('should prevent double-booking of slots', async () => {
      // First, block some slots
      const request = {
        court_ids: [testCourtIds[0]],
        start_date: '2024-06-28',
        end_date: '2024-06-28',
        start_time: '14:00',
        end_time: '15:00',
        reason: 'First blocking',
        created_by: testUserId
      };

      const firstResult = await SlotManagementService.bulkBlockSlots(request, testUserId);
      expect(firstResult.success).toBe(true);

      // Try to block the same slots again
      const secondResult = await SlotManagementService.bulkBlockSlots(request, testUserId);
      expect(secondResult.success).toBe(true);
      expect(secondResult.blocked_count).toBe(0); // Should not block already blocked slots

      // Get created slot IDs for cleanup
      const { data: blockedSlots } = await supabase
        .from('blocked_slots')
        .select('id')
        .eq('court_id', testCourtIds[0])
        .eq('date', '2024-06-28')
        .eq('start_time', '14:00');

      if (blockedSlots) {
        createdSlotIds.push(...blockedSlots.map(slot => slot.id));
      }
    });
  });

  describe('Slot Unblocking', () => {
    it('should successfully unblock multiple slots', async () => {
      // First, create some blocked slots
      const { data: blockedSlots, error } = await supabase
        .from('blocked_slots')
        .insert([
          {
            court_id: testCourtIds[0],
            date: '2024-06-29',
            start_time: '10:00',
            end_time: '11:00',
            reason: 'Test blocking for unblock',
            created_by: testUserId
          },
          {
            court_id: testCourtIds[1],
            date: '2024-06-29',
            start_time: '10:00',
            end_time: '11:00',
            reason: 'Test blocking for unblock',
            created_by: testUserId
          }
        ])
        .select();

      expect(error).toBeNull();
      expect(blockedSlots).toBeDefined();

      const slotIds = blockedSlots!.map(slot => slot.id);
      createdSlotIds.push(...slotIds);

      // Now unblock them
      const result = await SlotManagementService.unblockSlots(slotIds, testUserId);

      expect(result.success).toBe(true);
      expect(result.unblocked_count).toBe(2);

      // Verify slots were actually deleted
      const { data: remainingSlots } = await supabase
        .from('blocked_slots')
        .select('*')
        .in('id', slotIds);

      expect(remainingSlots).toBeDefined();
      expect(remainingSlots!.length).toBe(0);

      // Clear from cleanup list since they're already deleted
      createdSlotIds = createdSlotIds.filter(id => !slotIds.includes(id));
    });
  });

  describe('Slot Availability Checking', () => {
    it('should correctly identify blocked slots', async () => {
      // Create a blocked slot
      const { data: blockedSlot, error } = await supabase
        .from('blocked_slots')
        .insert({
          court_id: testCourtIds[0],
          date: '2024-06-30',
          start_time: '16:00',
          end_time: '17:00',
          reason: 'Test availability check',
          created_by: testUserId
        })
        .select()
        .single();

      expect(error).toBeNull();
      createdSlotIds.push(blockedSlot!.id);

      // Check if slot is blocked
      const result = await SlotManagementService.isSlotBlocked(
        testCourtIds[0],
        '2024-06-30',
        '16:00',
        '17:00'
      );

      expect(result.blocked).toBe(true);
      expect(result.reason).toBe('Test availability check');

      // Check a non-blocked slot
      const nonBlockedResult = await SlotManagementService.isSlotBlocked(
        testCourtIds[0],
        '2024-06-30',
        '17:00',
        '18:00'
      );

      expect(nonBlockedResult.blocked).toBe(false);
    });
  });

  describe('Data Retrieval', () => {
    it('should fetch blocked slots with detailed information', async () => {
      // Create test blocked slots
      const { data: blockedSlots, error } = await supabase
        .from('blocked_slots')
        .insert([
          {
            court_id: testCourtIds[0],
            date: '2024-07-01',
            start_time: '09:00',
            end_time: '10:00',
            reason: 'Morning maintenance',
            created_by: testUserId
          },
          {
            court_id: testCourtIds[1],
            date: '2024-07-01',
            start_time: '14:00',
            end_time: '15:00',
            reason: 'Afternoon cleaning',
            created_by: testUserId
          }
        ])
        .select();

      expect(error).toBeNull();
      createdSlotIds.push(...blockedSlots!.map(slot => slot.id));

      // Fetch with details
      const result = await SlotManagementService.getBlockedSlotsWithDetails(
        testVenueId,
        undefined,
        '2024-07-01',
        '2024-07-01'
      );

      expect(result.error).toBeNull();
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(2);

      const slot = result.data![0];
      expect(slot).toHaveProperty('venue_name');
      expect(slot).toHaveProperty('court_name');
      expect(slot).toHaveProperty('created_by_name');
      expect(slot.venue_name).toBe('Test Venue for Slot Management');
    });

    it('should fetch statistics correctly', async () => {
      // Create some test data
      const { data: blockedSlots, error } = await supabase
        .from('blocked_slots')
        .insert([
          {
            court_id: testCourtIds[0],
            date: '2024-07-02',
            start_time: '10:00',
            end_time: '11:00',
            reason: 'Stats test 1',
            created_by: testUserId
          },
          {
            court_id: testCourtIds[1],
            date: '2024-07-02',
            start_time: '11:00',
            end_time: '12:00',
            reason: 'Stats test 2',
            created_by: testUserId
          }
        ])
        .select();

      expect(error).toBeNull();
      createdSlotIds.push(...blockedSlots!.map(slot => slot.id));

      // Fetch stats
      const result = await SlotManagementService.getSlotManagementStats(
        testVenueId,
        '2024-07-02',
        '2024-07-02'
      );

      expect(result.error).toBeNull();
      expect(result.data).toBeDefined();
      expect(result.data!.total_blocked_slots).toBe(2);
      expect(result.data!.venues_with_blocks).toBe(1);
      expect(result.data!.courts_with_blocks).toBe(2);
    });
  });
});
