
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for enhanced UI */
@keyframes shine {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(46, 125, 50, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(46, 125, 50, 0.6);
  }
}

.animate-shine {
  animation: shine 2s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

@layer base {
  :root {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Add animation for typing indicator */
.dot-typing {
  position: relative;
  left: -9999px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #8B5CF6;
  color: #8B5CF6;
  box-shadow: 9999px 0 0 0 #8B5CF6;
  animation: dot-typing 1.5s infinite linear;
}

.dot-typing::before,
.dot-typing::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-typing::before {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #8B5CF6;
  color: #8B5CF6;
  animation: dot-typing-before 1.5s infinite linear;
  animation-delay: 0.25s;
}

.dot-typing::after {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #8B5CF6;
  color: #8B5CF6;
  animation: dot-typing-after 1.5s infinite linear;
  animation-delay: 0.5s;
}

@keyframes dot-typing {
  0% {
    box-shadow: 9999px 0 0 0 #8B5CF6;
  }
  25% {
    box-shadow: 9999px 0 0 0 rgba(139, 92, 246, 0.4);
  }
  50% {
    box-shadow: 9999px 0 0 0 rgba(139, 92, 246, 0.2);
  }
  75% {
    box-shadow: 9999px 0 0 0 rgba(139, 92, 246, 0.1);
  }
  100% {
    box-shadow: 9999px 0 0 0 #8B5CF6;
  }
}

@keyframes dot-typing-before {
  0% {
    box-shadow: 9984px 0 0 0 #8B5CF6;
  }
  25% {
    box-shadow: 9984px 0 0 0 rgba(139, 92, 246, 0.4);
  }
  50% {
    box-shadow: 9984px 0 0 0 rgba(139, 92, 246, 0.2);
  }
  75% {
    box-shadow: 9984px 0 0 0 rgba(139, 92, 246, 0.1);
  }
  100% {
    box-shadow: 9984px 0 0 0 #8B5CF6;
  }
}

@keyframes dot-typing-after {
  0% {
    box-shadow: 9969px 0 0 0 #8B5CF6;
  }
  25% {
    box-shadow: 9969px 0 0 0 rgba(139, 92, 246, 0.4);
  }
  50% {
    box-shadow: 9969px 0 0 0 rgba(139, 92, 246, 0.2);
  }
  75% {
    box-shadow: 9969px 0 0 0 rgba(139, 92, 246, 0.1);
  }
  100% {
    box-shadow: 9969px 0 0 0 #8B5CF6;
  }
}

/* Hero video styles */
.hero-section {
  position: relative;
  height: 80vh;
  min-height: 600px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.8));
}

.dark-gradient-overlay {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.9));
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 0 20px;
}

/* Buttons */
.dynamic-button {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  background: linear-gradient(to right, #6366f1, #4f46e5);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.dynamic-button:hover {
  box-shadow: 0 15px 30px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

.nike-button {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

/* Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s forwards;
}

@keyframes reveal {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-reveal {
  animation: reveal 0.8s forwards;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
}

/* Glass card effect */
.glass-card {
  background-color: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
}

/* Pattern background */
.pattern-dots {
  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Infinite scroll animation for partners section */
@keyframes infinite-scroll {
  from { transform: translateX(0); }
  to { transform: translateX(-50%); }
}

.animate-infinite-scroll {
  animation: infinite-scroll 30s linear infinite;
}

/* Pulse animation for green dots */
@keyframes pulse-light {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

.animate-pulse-light {
  animation: pulse-light 3s infinite;
}

/* Custom Sport Cards */
.venue-card, .sport-card {
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.venue-card:hover, .sport-card:hover {
  border-color: rgba(99, 102, 241, 0.3);
}

@keyframes blink {
  0%, 100% { opacity: 1 }
  50% { opacity: 0 }
}
.animate-blink {
  animation: blink 1s step-end infinite;
}

