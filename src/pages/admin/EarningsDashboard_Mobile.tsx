
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Link, useNavigate } from 'react-router-dom';
import {
  ArrowLeft, Calendar, DollarSign, TrendingUp, 
  Clock, Banknote, Activity, Eye, ChevronRight,
  Loader2, AlertCircle, CheckCircle, RefreshCw, Info
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { format, startOfWeek, endOfWeek, addDays, subDays } from 'date-fns';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from "react-hot-toast";
import { getActiveDaysDisplay, getActiveDaysLabel } from '@/utils/weeklyUtils';

interface DailyEarnings {
  venue_id: string;
  venue_name: string;
  cycle_date: string;
  cycle_day_name: string;
  total_bookings: number;
  confirmed_bookings: number;
  gross_revenue: number;
  platform_fee_amount: number;
  net_revenue: number;
  is_current_day: boolean;
  is_frozen: boolean;
}

interface WeeklySummary {
  week_start: string;
  week_end: string;
  total_gross: number;
  total_net: number;
  total_bookings: number;
  days_with_data: number;
}

const EarningsDashboard_Mobile = () => {
  const { user, userRole } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dailyEarnings, setDailyEarnings] = useState<DailyEarnings[]>([]);
  const [weeklySummary, setWeeklySummary] = useState<WeeklySummary | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));

  // Redirect to desktop if not mobile
  useEffect(() => {
    if (!isMobile) {
      navigate('/admin/earnings');
    }
  }, [isMobile, navigate]);

  // Check if we're viewing current week or historical week
  const isCurrentWeek = () => {
    const today = new Date();
    const currentWeekStartDate = startOfWeek(today, { weekStartsOn: 1 });
    return format(currentWeekStart, 'yyyy-MM-dd') === format(currentWeekStartDate, 'yyyy-MM-dd');
  };

  // Fetch earnings data
  const fetchEarningsData = async (showRefreshIndicator = false) => {
    if (!user?.id) return;
    
    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // TEMPORARY: Direct query until database functions are fixed
      // Get admin venues first
      const { data: adminVenuesData, error: venuesError } = await supabase
        .rpc('get_admin_venues');

      if (venuesError) {
        console.error('Error fetching admin venues:', venuesError);
        throw venuesError;
      }

      const adminVenueIds = adminVenuesData?.map(v => v.venue_id) || [];

      if (adminVenueIds.length === 0) {
        console.log('No admin venues found for user');
        setDailyEarnings([]);
        setWeeklySummary({
          week_start: format(currentWeekStart, 'yyyy-MM-dd'),
          week_end: format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'yyyy-MM-dd'),
          total_gross: 0,
          total_net: 0,
          total_bookings: 0,
          days_with_data: 0
        });
        return;
      }

      // Fetch ALL week's bookings, not just today
      const weekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
      const { data: weekBookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          total_price,
          status,
          payment_status,
          court:courts!inner(
            id,
            name,
            venue:venues!inner(
              id,
              name,
              platform_fee_percentage
            )
          )
        `)
        .gte('booking_date', format(currentWeekStart, 'yyyy-MM-dd'))
        .lte('booking_date', format(weekEnd, 'yyyy-MM-dd'))
        .in('court.venue.id', adminVenueIds)
        .in('status', ['confirmed', 'completed']);

      if (bookingsError) {
        console.error('Error fetching bookings:', bookingsError);
        throw bookingsError;
      }

      console.log('Week\'s bookings found:', weekBookings);

      // Generate all days in the week
      const weekDays = [];
      for (let i = 0; i < 7; i++) {
        const currentDate = addDays(currentWeekStart, i);
        weekDays.push({
          date: currentDate,
          dateStr: format(currentDate, 'yyyy-MM-dd'),
          dayName: format(currentDate, 'EEEE'),
          isToday: format(currentDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
        });
      }

      // Process the data for each day and venue
      const processedEarnings = [];

      for (const venueId of adminVenueIds) {
        for (const day of weekDays) {
          const dayBookings = weekBookings?.filter(b =>
            b.court?.venue?.id === venueId &&
            b.booking_date === day.dateStr
          ) || [];

          const venue = weekBookings?.find(b => b.court?.venue?.id === venueId)?.court?.venue;
          const grossRevenue = dayBookings.reduce((sum, b) => sum + (parseFloat(b.total_price) || 0), 0);
          const platformFeePercent = venue?.platform_fee_percentage || 5;
          const platformFeeAmount = grossRevenue * (platformFeePercent / 100);
          const netRevenue = grossRevenue - platformFeeAmount;

          processedEarnings.push({
            venue_id: venueId,
            venue_name: venue?.name || 'Unknown Venue',
            cycle_date: day.dateStr,
            cycle_day_name: day.dayName,
            total_bookings: dayBookings.length,
            confirmed_bookings: dayBookings.filter(b => ['confirmed', 'completed'].includes(b.status)).length,
            gross_revenue: grossRevenue,
            platform_fee_amount: platformFeeAmount,
            net_revenue: netRevenue,
            is_current_day: day.isToday,
            is_frozen: day.date < new Date()
          });
        }
      }

      // Calculate weekly summary - group by date to avoid double counting across venues
      if (processedEarnings && processedEarnings.length > 0) {
        // Group by date first to get daily totals
        const dailyTotals = processedEarnings.reduce((acc: any, day: any) => {
          if (!acc[day.cycle_date]) {
            acc[day.cycle_date] = {
              gross_revenue: 0,
              net_revenue: 0,
              total_bookings: 0,
              platform_fee_amount: 0
            };
          }
          acc[day.cycle_date].gross_revenue += day.gross_revenue || 0;
          acc[day.cycle_date].net_revenue += day.net_revenue || 0;
          acc[day.cycle_date].total_bookings += day.total_bookings || 0;
          acc[day.cycle_date].platform_fee_amount += day.platform_fee_amount || 0;
          return acc;
        }, {});

        const summary: WeeklySummary = {
          week_start: format(currentWeekStart, 'yyyy-MM-dd'),
          week_end: format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'yyyy-MM-dd'),
          total_gross: Object.values(dailyTotals).reduce((sum: number, day: any) => sum + day.gross_revenue, 0),
          total_net: Object.values(dailyTotals).reduce((sum: number, day: any) => sum + day.net_revenue, 0),
          total_bookings: Object.values(dailyTotals).reduce((sum: number, day: any) => sum + day.total_bookings, 0),
          days_with_data: Object.values(dailyTotals).filter((day: any) => day.total_bookings > 0).length
        };
        setWeeklySummary(summary);

        // Create daily earnings array for the Daily Earnings section
        const dailyEarningsArray = Object.entries(dailyTotals).map(([date, totals]: [string, any]) => {
          const dayData = processedEarnings.find((e: any) => e.cycle_date === date);
          return {
            ...totals,
            cycle_date: date,
            cycle_day_name: dayData?.cycle_day_name || format(new Date(date), 'EEEE'),
            venue_name: 'All Venues', // Since we're aggregating across venues
            venue_id: 'all',
            is_current_day: dayData?.is_current_day || false,
            is_frozen: dayData?.is_frozen || false
          };
        }).sort((a, b) => new Date(a.cycle_date).getTime() - new Date(b.cycle_date).getTime());

        setDailyEarnings(dailyEarningsArray);
      } else {
        setWeeklySummary({
          week_start: format(currentWeekStart, 'yyyy-MM-dd'),
          week_end: format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'yyyy-MM-dd'),
          total_gross: 0,
          total_net: 0,
          total_bookings: 0,
          days_with_data: 0
        });
        setDailyEarnings([]);
      }

    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchEarningsData();
  }, [user?.id, currentWeekStart]);

  // Navigate to previous/next week
  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeekStart = direction === 'prev' 
      ? subDays(currentWeekStart, 7)
      : addDays(currentWeekStart, 7);
    setCurrentWeekStart(newWeekStart);
  };

  // Get today's earnings
  const todayEarnings = dailyEarnings.find(day => day.is_current_day);

  if (loading) {
    return (
      <div className="min-h-screen bg-navy-dark text-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-500 mx-auto mb-4" />
          <p className="text-emerald-200">Loading earnings data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-navy-dark text-white">
      {/* Header */}
      <div className="bg-black/80 shadow-md sticky top-0 z-10">
        <div className="px-4 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/mobile-home')}
              className="mr-2 text-white hover:bg-white/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="font-bold text-lg text-white">Earnings Dashboard</h1>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fetchEarningsData(true)}
            disabled={refreshing}
            className="text-white hover:bg-white/10"
          >
            <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Time Period Context Banner */}
        <Card className={`border-2 ${
          isCurrentWeek() 
            ? 'bg-emerald-500/10 border-emerald-500/30' 
            : 'bg-blue-500/10 border-blue-500/30'
        }`}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${
                isCurrentWeek() 
                  ? 'bg-emerald-500/20' 
                  : 'bg-blue-500/20'
              }`}>
                {isCurrentWeek() ? (
                  <Activity className="w-5 h-5 text-emerald-400" />
                ) : (
                  <Clock className="w-5 h-5 text-blue-400" />
                )}
              </div>
              <div className="flex-1">
                <div className={`font-semibold ${
                  isCurrentWeek() ? 'text-emerald-300' : 'text-blue-300'
                }`}>
                  {isCurrentWeek() ? 'Current Week Earnings' : 'Historical Week Data'}
                </div>
                <div className="text-sm text-gray-300">
                  {isCurrentWeek() 
                    ? 'Live earnings data - updates in real-time as bookings are made'
                    : 'Historical data from completed week - final amounts may differ from settlements'
                  }
                </div>
              </div>
              {!isCurrentWeek() && (
                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                  <Info className="w-3 h-3 mr-1" />
                  Historical
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Today's Performance - only show for current week */}
        {isCurrentWeek() && (
          <Card className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-emerald-500/30">
            <CardHeader className="pb-3">
              <CardTitle className="text-emerald-300 flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Today's Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              {todayEarnings ? (
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">₹{todayEarnings.gross_revenue.toFixed(0)}</div>
                    <div className="text-sm text-emerald-200">Gross Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">₹{todayEarnings.net_revenue.toFixed(0)}</div>
                    <div className="text-sm text-emerald-200">Net Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-white">{todayEarnings.total_bookings}</div>
                    <div className="text-sm text-emerald-200">Bookings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-white">₹{todayEarnings.platform_fee_amount.toFixed(0)}</div>
                    <div className="text-sm text-emerald-200">Platform Fee</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="text-lg text-emerald-200">No bookings today yet</div>
                  <div className="text-sm text-emerald-300">Revenue will appear when bookings are made</div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Week Navigation */}
        <div className="flex items-center justify-between bg-black/40 rounded-lg p-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateWeek('prev')}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Prev Week
          </Button>
          <div className="text-center">
            <div className="font-semibold text-white">
              {format(currentWeekStart, 'MMM dd')} - {format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'MMM dd, yyyy')}
            </div>
            <div className={`text-sm ${
              isCurrentWeek() ? 'text-emerald-300' : 'text-blue-300'
            }`}>
              {isCurrentWeek() ? 'Current Weekly Cycle' : 'Historical Weekly Cycle'}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateWeek('next')}
            className="text-white hover:bg-white/10"
          >
            Next Week
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>

        {/* Weekly Summary */}
        {weeklySummary && (
          <Card className="bg-black/40 border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Week Summary
                {!isCurrentWeek() && (
                  <Badge className="ml-2 bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
                    Historical Data
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-emerald-500/20 rounded-lg p-3 text-center">
                  <div className="text-xl font-bold text-emerald-300">₹{weeklySummary.total_gross.toFixed(0)}</div>
                  <div className="text-sm text-emerald-200">Gross Revenue</div>
                </div>
                <div className="bg-green-500/20 rounded-lg p-3 text-center">
                  <div className="text-xl font-bold text-green-300">₹{weeklySummary.total_net.toFixed(0)}</div>
                  <div className="text-sm text-green-200">Net Revenue</div>
                </div>
                <div className="bg-blue-500/20 rounded-lg p-3 text-center">
                  <div className="text-xl font-bold text-blue-300">{weeklySummary.total_bookings}</div>
                  <div className="text-sm text-blue-200">Total Bookings</div>
                </div>
                <div className="bg-purple-500/20 rounded-lg p-3 text-center">
                  <div className="text-xl font-bold text-purple-300">{getActiveDaysDisplay(currentWeekStart, weeklySummary.days_with_data)}</div>
                  <div className="text-sm text-purple-200">{getActiveDaysLabel(currentWeekStart)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Daily Earnings Section */}
        <Card className="bg-black/40 border-white/20">
          <CardHeader className="pb-3">
            <CardTitle className="text-white flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Daily Earnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dailyEarnings.map((day, index) => (
                <div
                  key={`${day.venue_id}-${day.cycle_date}`}
                  className={`rounded-lg p-4 border ${
                    day.is_current_day
                      ? 'bg-emerald-500/20 border-emerald-500/50'
                      : day.total_bookings > 0
                        ? 'bg-white/5 border-white/20'
                        : 'bg-gray-500/10 border-gray-500/20'
                  }`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="text-white font-semibold">
                        {day.cycle_day_name}
                      </div>
                      <div className="text-sm text-gray-300 ml-2">
                        {format(new Date(day.cycle_date), 'MMM dd')}
                      </div>
                      {day.is_current_day && (
                        <span className="ml-2 px-2 py-1 bg-emerald-500 text-white text-xs rounded-full">
                          Today
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-white font-bold">
                        ₹{day.gross_revenue.toFixed(0)}
                      </div>
                      <div className="text-xs text-gray-300">
                        {day.total_bookings} booking{day.total_bookings !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>

                  {day.total_bookings > 0 ? (
                    <div className="grid grid-cols-3 gap-3 text-sm">
                      <div className="text-center">
                        <div className="text-emerald-300 font-semibold">₹{day.gross_revenue.toFixed(0)}</div>
                        <div className="text-gray-400 text-xs">Gross</div>
                      </div>
                      <div className="text-center">
                        <div className="text-green-300 font-semibold">₹{day.net_revenue.toFixed(0)}</div>
                        <div className="text-gray-400 text-xs">Net</div>
                      </div>
                      <div className="text-center">
                        <div className="text-blue-300 font-semibold">₹{day.platform_fee_amount.toFixed(0)}</div>
                        <div className="text-gray-400 text-xs">Fee</div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-gray-400 text-sm py-2">
                      No bookings
                    </div>
                  )}

                  <div className="text-xs text-gray-400 mt-2">
                    {day.venue_name}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Link
            to="/admin/settlements-mobile"
            className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 rounded-lg p-4 flex flex-col items-center text-center hover:bg-blue-500/30 transition-colors"
          >
            <Banknote className="w-8 h-8 text-blue-400 mb-2" />
            <div className="font-semibold text-white">Settlements</div>
            <div className="text-sm text-blue-200">View weekly settlements</div>
          </Link>

          <Link
            to="/admin/analytics-mobile"
            className="bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-500/30 rounded-lg p-4 flex flex-col items-center text-center hover:bg-purple-500/30 transition-colors"
          >
            <Eye className="w-8 h-8 text-purple-400 mb-2" />
            <div className="font-semibold text-white">Analytics</div>
            <div className="text-sm text-purple-200">Detailed insights</div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default EarningsDashboard_Mobile;
