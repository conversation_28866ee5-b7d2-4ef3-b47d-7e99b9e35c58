
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { format, startOfMonth, endOfMonth, subMonths, parseISO, isWithinInterval, addDays } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { Download, Loader2, DollarSign, Banknote } from 'lucide-react';
import * as XLSX from 'xlsx';
import {
  getOptimizedDashboardStats,
  getOptimizedPopularCourts,
  getOptimizedVenuesWithStats
} from '@/utils/dashboardOptimization';

interface QuickStats {
  todayBookings: number;
  averageRating: number;
  occupancyRate: number;
  isLoading: boolean;
  pendingBookings: number;
  upcomingBookings: number;
  recentReviews: number;
}

interface VenueWithBookingStats {
  id: string;
  name: string;
  bookings_count: number;
  total_revenue: number;
  platform_fee_percentage: number;
}

interface CourtStats {
  court_name: string;
  bookings_percentage: number;
}

const Dashboard: React.FC = () => {
  const { user, userRole } = useAuth();
  const [stats, setStats] = useState<QuickStats>({
    todayBookings: 0,
    averageRating: 0,
    occupancyRate: 0,
    isLoading: true,
    pendingBookings: 0,
    upcomingBookings: 0,
    recentReviews: 0
  });
  const [adminVenues, setAdminVenues] = useState<Array<{ venue_id: string }>>([]);
  const [customStartDate, setCustomStartDate] = useState<Date | null>(null);
  const [customEndDate, setCustomEndDate] = useState<Date | null>(null);
  const [customRevenue, setCustomRevenue] = useState(0);
  const [todaysRevenue, setTodaysRevenue] = useState(0);
  const [popularCourts, setPopularCourts] = useState<CourtStats[]>([]);
  const [courtDataLoading, setCourtDataLoading] = useState(true);
  const [venuesWithStats, setVenuesWithStats] = useState<VenueWithBookingStats[]>([]);
  const [downloadingReport, setDownloadingReport] = useState(false);

  // Fetch admin venues first
  useEffect(() => {
    const fetchAdminVenues = async () => {
      try {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        if (!currentUser) return;

        const { data: userRoles, error: roleError } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', currentUser.id);

        if (roleError) {
          console.error('Error fetching user role:', roleError);
          return;
        }

        let highestRole = null;
        if (userRoles && userRoles.length > 0) {
          if (userRoles.some(r => r.role === 'super_admin')) {
            highestRole = 'super_admin';
          } else if (userRoles.some(r => r.role === 'admin')) {
            highestRole = 'admin';
          } else {
            highestRole = 'user';
          }
        }

        if (highestRole === 'admin') {
          const { data: venues } = await supabase.rpc('get_admin_venues');
          setAdminVenues(venues || []);
        } else if (highestRole === 'super_admin') {
          setAdminVenues([]);
        }
      } catch (error) {
        console.error('Error fetching admin venues:', error);
      }
    };
    fetchAdminVenues();
  }, []);

  // Fetch venues with stats using optimized function
  useEffect(() => {
    const fetchVenuesWithStats = async () => {
      if (!user?.id || !userRole) return;

      try {
        const venuesWithData = await getOptimizedVenuesWithStats(
          user.id,
          userRole,
          adminVenues
        );
        setVenuesWithStats(venuesWithData);
      } catch (error) {
        console.error('Error fetching optimized venues with stats:', error);
      }
    };

    if (adminVenues.length > 0 || userRole === 'super_admin') {
      fetchVenuesWithStats();
    }
  }, [adminVenues, userRole, user?.id]);

  // Fetch dashboard metrics with single query
  useEffect(() => {
    const fetchDashboardMetrics = async () => {
      if (!user?.id || !userRole) return;

      try {
        setStats(prev => ({ ...prev, isLoading: true }));
        setCourtDataLoading(true);

        const dashboardStats = await getOptimizedDashboardStats(user.id);
        const popularCourtsData = await getOptimizedPopularCourts(user.id, 30);

        setTodaysRevenue(dashboardStats.todaysRevenue);
        setPopularCourts(popularCourtsData);
        setCourtDataLoading(false);

        setStats({
          todayBookings: dashboardStats.todayBookings,
          averageRating: dashboardStats.averageRating,
          occupancyRate: dashboardStats.occupancyRate,
          isLoading: false,
          pendingBookings: dashboardStats.pendingBookings,
          upcomingBookings: dashboardStats.upcomingBookings,
          recentReviews: dashboardStats.recentReviews
        });
      } catch (error) {
        console.error('Error fetching dashboard metrics:', error);
        setStats(prev => ({ ...prev, isLoading: false }));
      }
    };

    fetchDashboardMetrics();
    
    const bookingsChannel = supabase.channel('public:bookings')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'bookings' }, 
        () => {
          fetchDashboardMetrics();
        }
      )
      .subscribe();

    const reviewsChannel = supabase.channel('public:reviews')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'reviews' }, 
        () => {
          fetchDashboardMetrics();
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(bookingsChannel);
      supabase.removeChannel(reviewsChannel);
    };
  }, [user?.id, userRole]);

  // Custom revenue calculation
  useEffect(() => {
    const calculateCustomRevenue = async () => {
      if (!customStartDate || !customEndDate || !user?.id) {
        setCustomRevenue(0);
        return;
      }

      try {
        const startDateStr = format(customStartDate, 'yyyy-MM-dd');
        const endDateStr = format(customEndDate, 'yyyy-MM-dd');

        const { data: bookingsData, error } = await supabase
          .from('bookings')
          .select(`
            total_price,
            court:courts (
              venue:venues (
                id
              )
            )
          `)
          .gte('booking_date', startDateStr)
          .lte('booking_date', endDateStr)
          .in('status', ['confirmed', 'completed']);

        if (error) throw error;

        const totalRevenue = (bookingsData || [])
          .filter(booking => {
            if (userRole === 'super_admin') return true;
            return adminVenues.some(v => v.venue_id === booking.court?.venue?.id);
          })
          .reduce((sum, booking) => {
            const price = Number(booking.total_price) || 0;
            return sum + price;
          }, 0);

        setCustomRevenue(totalRevenue);
      } catch (error) {
        setCustomRevenue(0);
      }
    };

    const timeoutId = setTimeout(calculateCustomRevenue, 100);
    return () => clearTimeout(timeoutId);
  }, [customStartDate, customEndDate, user?.id, userRole, adminVenues]);

  const downloadRevenueReport = async () => {
    if (!customStartDate || !customEndDate || !user?.id) {
      toast({
        title: 'Error',
        description: 'Please select both start and end dates',
        variant: 'destructive',
      });
      return;
    }

    setDownloadingReport(true);

    try {
      const startDate = typeof customStartDate === 'string' ? new Date(customStartDate) : customStartDate;
      const endDate = typeof customEndDate === 'string' ? new Date(customEndDate) : customEndDate;
      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      // Get bookings for the selected date range with venue platform fee info
      const { data: bookingsData, error } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          total_price,
          payment_method,
          status,
          created_at,
          user_id,
          court:courts (
            name,
            venue:venues (
              id,
              name,
              platform_fee_percentage
            )
          )
        `)
        .gte('booking_date', startDateStr)
        .lte('booking_date', endDateStr)
        .in('status', ['confirmed', 'completed'])
        .order('booking_date', { ascending: false });

      if (error) throw error;

      // Filter by admin venues if needed
      const filteredBookings = (bookingsData || []).filter(booking => {
        if (userRole === 'super_admin') return true;
        return adminVenues.some(v => v.venue_id === booking.court?.venue?.id);
      });

      // Get unique user IDs
      const userIds = [...new Set(filteredBookings.map(booking => booking.user_id).filter(Boolean))];

      // Batch fetch profiles
      let profilesMap: Record<string, any> = {};
      if (userIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, full_name, phone, email')
          .in('id', userIds);

        if (!profilesError && profiles) {
          profilesMap = profiles.reduce((acc, profile) => {
            acc[profile.id] = profile;
            return acc;
          }, {} as Record<string, any>);
        }
      }

      // Prepare Excel data with profile information
      const excelData = filteredBookings.map(booking => {
        const grossAmount = booking.total_price || 0;
        const feePercent = booking.court?.venue?.platform_fee_percentage ?? 5;
        const platformFee = grossAmount * (feePercent / 100);
        const netAmount = grossAmount - platformFee;
        
        const profile = profilesMap[booking.user_id] || {};

        return {
          'Booking Date': booking.booking_date,
          'Time Slot': `${booking.start_time} - ${booking.end_time}`,
          'Venue': booking.court?.venue?.name || 'N/A',
          'Court': booking.court?.name || 'N/A',
          'Customer Name': profile.full_name || 'N/A',
          'Customer Email': profile.email || 'N/A',
          'Customer Phone': profile.phone || 'N/A',
          'Gross Amount (₹)': grossAmount,
          'Platform Fee (₹)': platformFee.toFixed(2),
          'Net Amount (₹)': netAmount.toFixed(2),
          'Payment Method': booking.payment_method || 'N/A',
          'Payment Status': booking.status,
          'Created At': new Date(booking.created_at).toLocaleString(),
          'Platform Fee %': feePercent
        };
      });

      // Add summary rows
      const totalGrossRevenue = filteredBookings.reduce((sum, b) => sum + (b.total_price || 0), 0);
      const totalPlatformFee = filteredBookings.reduce((sum, b) => {
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        return sum + ((b.total_price || 0) * (feePercent / 100));
      }, 0);
      const totalNetRevenue = totalGrossRevenue - totalPlatformFee;
      const avgFee = filteredBookings.length > 0 ? (filteredBookings.reduce((sum, b) => sum + (b.court?.venue?.platform_fee_percentage ?? 5), 0) / filteredBookings.length) : 5;

      const summaryRows = [
        {},
        { 'Booking Date': 'SUMMARY' },
        { 'Booking Date': 'Total Bookings', 'Time Slot': filteredBookings.length },
        { 'Booking Date': 'Gross Revenue', 'Time Slot': `₹${totalGrossRevenue.toFixed(2)}` },
        { 'Booking Date': 'Platform Fee', 'Time Slot': `₹${totalPlatformFee.toFixed(2)}` },
        { 'Booking Date': 'Net Revenue', 'Time Slot': `₹${totalNetRevenue.toFixed(2)}` },
        { 'Booking Date': 'Avg Platform Fee %', 'Time Slot': `${avgFee.toFixed(2)}%` },
        { 'Booking Date': 'Date Range', 'Time Slot': `${format(startDate, 'dd/MM/yyyy')} to ${format(endDate, 'dd/MM/yyyy')}` }
      ];

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet([...excelData, ...summaryRows]);
      XLSX.utils.book_append_sheet(wb, ws, 'Revenue Report');

      // Generate filename with date range
      const filename = `revenue_report_${startDateStr}_to_${endDateStr}.xlsx`;
      XLSX.writeFile(wb, filename);

      toast({
        title: 'Success',
        description: 'Revenue report downloaded successfully!',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to download report',
        variant: 'destructive',
      });
    } finally {
      setDownloadingReport(false);
    }
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value ? parseISO(e.target.value) : null;
    setCustomStartDate(newDate);
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value ? parseISO(e.target.value) : null;
    setCustomEndDate(newDate);
  };

  // Revenue display component
  const RevenueDisplay = () => {
    const revenue = Number(customRevenue);
    const safeRevenue = !isNaN(revenue) ? revenue : 0;
    const feePercent = venuesWithStats[0]?.platform_fee_percentage ?? 5;
    const netRevenue = safeRevenue * (1 - feePercent / 100);

    return (
      <div className="bg-gradient-to-r from-emerald-100 to-green-100 rounded-lg p-4 border border-emerald-200">
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-emerald-800">₹{safeRevenue.toFixed(0)}</div>
            <Banknote className="h-6 w-6 text-emerald-600" />
          </div>
          <div className="text-sm text-emerald-700 font-medium">Gross Revenue</div>
          <div className="flex items-center justify-between mt-2 pt-2 border-t border-emerald-200">
            <div className="text-xl font-semibold text-emerald-600">₹{netRevenue.toFixed(0)}</div>
            <span className="text-sm text-emerald-600">Net Revenue</span>
          </div>
          <div className="text-xs text-emerald-500">After {feePercent}% platform fee</div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Overview of your venue performance</p>
        </div>
        
        {/* Today's Performance */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today's Bookings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.todayBookings}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{todaysRevenue.toFixed(0)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageRating}</div>
            </CardContent>
          </Card>
        </div>

        {/* Revenue Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Revenue Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h4 className="text-lg font-medium">Custom Date Range Analysis</h4>
              <div className="flex gap-4 items-end">
                <div className="flex flex-col gap-2">
                  <label className="text-sm font-medium">Start Date</label>
                  <input 
                    type="date" 
                    value={customStartDate ? format(customStartDate, 'yyyy-MM-dd') : ''} 
                    onChange={handleStartDateChange} 
                    className="px-3 py-2 border rounded-md" 
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <label className="text-sm font-medium">End Date</label>
                  <input 
                    type="date" 
                    value={customEndDate ? format(customEndDate, 'yyyy-MM-dd') : ''} 
                    onChange={handleEndDateChange} 
                    className="px-3 py-2 border rounded-md" 
                  />
                </div>
              </div>
              
              {customStartDate && customEndDate && (
                <div className="mt-4">
                  <RevenueDisplay />
                  
                  <div className="mt-4">
                    <Button
                      onClick={downloadRevenueReport}
                      disabled={downloadingReport}
                      className="w-full md:w-auto"
                    >
                      {downloadingReport ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Generating Report...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Download Revenue Report
                        </>
                      )}
                    </Button>
                    <p className="text-sm text-muted-foreground mt-2">
                      Excel sheet with detailed booking analytics & revenue calculations
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Courts</CardTitle>
          </CardHeader>
          <CardContent>
            {courtDataLoading || stats.isLoading ? (
              <div className="flex justify-center items-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : popularCourts.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                <p>No booking data available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {popularCourts.map((court, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-muted rounded-lg">
                    <span className="font-medium">{court.court_name}</span>
                    <span className="font-bold text-primary">{court.bookings_percentage}% utilized</span>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
