import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { format, subMonths, parseISO, eachDayOfInterval, startOfMonth, endOfMonth, isWithinInterval, addDays } from 'date-fns';
import { Calendar, Users, Loader2 } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import PaymentMethodFilter, { PaymentMethodFilterType } from '@/components/admin/PaymentMethodFilter';
import { LineChart as RechartsLine<PERSON>hart, <PERSON>, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Bar, Pie<PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

interface BookingData {
  id: string;
  venue_id: string;
  venue_name: string;
  court_name: string;
  sport_name: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: number;
  payment_method: string;
  status: string;
  created_at: string;
  customer_name: string;
  customer_phone: string;
  court?: {
    name: string;
    venue_id: string;
    sport_id: string;
    venue?: {
      id: string;
      name: string;
      platform_fee_percentage?: number;
    };
    sport?: {
      id: string;
      name: string;
    };
  };
  platform_fee_percentage?: number;
}

interface AdminVenue {
  venue_id: string;
}

interface Venue {
  id: string;
  name: string;
}

const AnalyticsDashboard = () => {
  const { user, userRole } = useAuth();
  const [loading, setLoading] = useState(true);
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [adminVenues, setAdminVenues] = useState<AdminVenue[]>([]);
  const [venues, setVenues] = useState<Venue[]>([]);
  const [activeTab, setActiveTab] = useState('overview');

  // Filters and controls
  const [timeRange, setTimeRange] = useState('month');
  const [selectedVenueId, setSelectedVenueId] = useState<string>('all');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<PaymentMethodFilterType>('online');

  // Fetch admin venues
  useEffect(() => {
    const fetchAdminVenues = async () => {
      if (!user?.id) return;

      try {
        if (userRole === 'super_admin') {
          const { data: allVenues, error } = await supabase
            .from('venues')
            .select('id, name, platform_fee_percentage')
            .eq('is_active', true);

          if (error) throw error;

          setVenues(allVenues || []);
          setAdminVenues(allVenues?.map(v => ({ venue_id: v.id })) || []);
        } else {
          const { data: assignedVenues, error } = await supabase
            .rpc('get_admin_venues');

          if (error) throw error;

          setAdminVenues(assignedVenues || []);

          // Fetch venue details for assigned venues
          if (assignedVenues && assignedVenues.length > 0) {
            const venueIds = assignedVenues.map(v => v.venue_id);
            const { data: venueDetails, error: venueError } = await supabase
              .from('venues')
              .select('id, name, platform_fee_percentage')
              .in('id', venueIds)
              .eq('is_active', true);

            if (venueError) throw venueError;
            setVenues(venueDetails || []);
          }
        }
      } catch (error) {
        console.error('Error fetching admin venues:', error);
      }
    };

    fetchAdminVenues();
  }, [user?.id, userRole]);

  // Fetch bookings data
  useEffect(() => {
    const fetchBookings = async () => {
      if (!user?.id) return;

      setLoading(true);
      try {
        let query = supabase
          .from('bookings')
          .select(`
            id,
            booking_date,
            start_time,
            end_time,
            total_price,
            payment_method,
            status,
            created_at,
            guest_name,
            guest_phone,
            court:courts!inner (
              name,
              venue_id,
              sport_id,
              venue:venues!inner (
                id,
                name,
                platform_fee_percentage
              ),
              sport:sports (
                id,
                name
              )
            )
          `)
          .eq('status', 'confirmed');

        // Filter by admin venues if not super_admin
        if (userRole !== 'super_admin' && adminVenues.length > 0) {
          const venueIds = adminVenues.map(v => v.venue_id);
          query = query.in('court.venue.id', venueIds);
        }

        const { data: bookingsData, error } = await query;

        if (error) throw error;

        // Transform data to include venue_name and sport_name
        const transformedBookings = bookingsData?.map(booking => ({
          ...booking,
          venue_id: booking.court?.venue?.id || '',
          venue_name: booking.court?.venue?.name || 'Unknown Venue',
          sport_name: booking.court?.sport?.name || 'Unknown Sport',
          court_name: booking.court?.name || 'Unknown Court',
          customer_name: booking.guest_name || 'Unknown Customer',
          customer_phone: booking.guest_phone || '',
          platform_fee_percentage: booking.court?.venue?.platform_fee_percentage ?? 5
        })) || [];

        setBookings(transformedBookings);
      } catch (error) {
        console.error('Error fetching bookings:', error);
      } finally {
        setLoading(false);
      }
    };

    if (adminVenues.length > 0 || userRole === 'super_admin') {
      fetchBookings();
    }
  }, [user?.id, userRole, adminVenues]);

  // Helper functions for time range calculations
  const getTimeRangeLabel = () => {
    if (timeRange === 'week') {
      return `Week of ${format(currentDate, 'MMM dd, yyyy')}`;
    } else if (timeRange === 'month') {
      return format(currentDate, 'MMMM yyyy');
    } else {
      return format(currentDate, 'yyyy');
    }
  };

  const handlePreviousPeriod = () => {
    if (timeRange === 'week') {
      setCurrentDate(addDays(currentDate, -7));
    } else if (timeRange === 'month') {
      setCurrentDate(subMonths(currentDate, 1));
    } else {
      setCurrentDate(new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate()));
    }
  };

  const handleNextPeriod = () => {
    if (timeRange === 'week') {
      setCurrentDate(addDays(currentDate, 7));
    } else if (timeRange === 'month') {
      setCurrentDate(addDays(currentDate, 30));
    } else {
      setCurrentDate(new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), currentDate.getDate()));
    }
  };

  // Filter bookings based on current filters
  const getFilteredBookings = () => {
    let filtered = bookings;

    // Filter by payment method
    if (paymentMethodFilter !== 'all') {
      filtered = filtered.filter(booking => booking.payment_method === paymentMethodFilter);
    }

    // Filter by venue
    if (selectedVenueId !== 'all') {
      filtered = filtered.filter(booking => booking.venue_id === selectedVenueId);
    }

    // Filter by time range
    let startDate: Date, endDate: Date;

    if (timeRange === 'week') {
      startDate = addDays(currentDate, -7);
      endDate = currentDate;
    } else if (timeRange === 'month') {
      startDate = startOfMonth(currentDate);
      endDate = endOfMonth(currentDate);
    } else {
      startDate = new Date(currentDate.getFullYear(), 0, 1);
      endDate = new Date(currentDate.getFullYear(), 11, 31);
    }

    filtered = filtered.filter(booking => {
      const bookingDate = parseISO(booking.booking_date);
      return isWithinInterval(bookingDate, { start: startDate, end: endDate });
    });

    return filtered;
  };

  const filteredBookings = getFilteredBookings();
  const totalRevenue = filteredBookings.reduce((sum, booking) => sum + booking.total_price, 0);
  const totalPlatformFee = filteredBookings.reduce((sum, booking) => {
    const feePercent = booking.platform_fee_percentage ?? 5;
    return sum + (booking.total_price * (feePercent / 100));
  }, 0);
  const totalNetRevenue = totalRevenue - totalPlatformFee;
  const avgFee = filteredBookings.length > 0 ? (filteredBookings.reduce((sum, b) => sum + (b.platform_fee_percentage ?? 5), 0) / filteredBookings.length) : 5;

  // Generate booking trends data
  const generateBookingTrends = () => {
    let rangeStart: Date, rangeEnd: Date;

    if (timeRange === 'week') {
      rangeStart = addDays(currentDate, -7);
      rangeEnd = currentDate;
    } else if (timeRange === 'month') {
      rangeStart = startOfMonth(currentDate);
      rangeEnd = endOfMonth(currentDate);
    } else {
      rangeStart = new Date(currentDate.getFullYear(), 0, 1);
      rangeEnd = new Date(currentDate.getFullYear(), 11, 31);
    }

    const days = eachDayOfInterval({ start: rangeStart, end: rangeEnd });
    return days.map(day => {
      const dayStr = format(day, 'yyyy-MM-dd');
      const dayBookings = filteredBookings.filter(booking => booking.booking_date === dayStr);
      return {
        date: format(day, 'MMM dd'),
        count: dayBookings.length,
        revenue: dayBookings.reduce((sum, booking) => sum + booking.total_price, 0)
      };
    });
  };

  // Generate sport popularity data
  const generateSportPopularityData = () => {
    const sportCounts: Record<string, number> = {};
    filteredBookings.forEach(booking => {
      const sportName = booking.sport_name || 'Unknown';
      sportCounts[sportName] = (sportCounts[sportName] || 0) + 1;
    });
    return Object.entries(sportCounts).map(([name, value]) => ({ name, value }));
  };

  // Generate peak hours data
  const generateTimeSlotPopularity = () => {
    const hourCounts: Record<string, number> = {};
    filteredBookings.forEach(booking => {
      const startHour = booking.start_time.split(':')[0];
      const hourKey = `${startHour}:00`;
      hourCounts[hourKey] = (hourCounts[hourKey] || 0) + 1;
    });
    return Object.entries(hourCounts)
      .map(([hour, count]) => ({ hour, count }))
      .sort((a, b) => parseInt(a.hour) - parseInt(b.hour));
  };

  const trendData = generateBookingTrends();
  const sportPopularityData = generateSportPopularityData();
  const timeSlotData = generateTimeSlotPopularity();

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">📊 Business Insights</h1>
          <p className="text-muted-foreground">Comprehensive analytics dashboard with detailed insights</p>
        </div>

        {/* Filters Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <PaymentMethodFilter
            selectedFilter={paymentMethodFilter}
            onFilterChange={setPaymentMethodFilter}
          />

          <select
            value={selectedVenueId}
            onChange={(e) => setSelectedVenueId(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            {userRole === 'super_admin' && (
              <option value="all">All Venues</option>
            )}
            {venues
              .filter(venue =>
                userRole === 'super_admin' ||
                adminVenues.some(v => v.venue_id === venue.id)
              )
              .map(venue => (
                <option key={venue.id} value={venue.id}>{venue.name}</option>
              ))
            }
          </select>

          <div className="flex items-center border rounded-md overflow-hidden">
            <Button
              variant="ghost"
              onClick={handlePreviousPeriod}
              className="border-r"
            >
              ←
            </Button>
            <div className="px-3 flex-1 text-center">
              <span className="text-sm font-medium">{getTimeRangeLabel()}</span>
            </div>
            <Button
              variant="ghost"
              onClick={handleNextPeriod}
              className="border-l"
              disabled={
                (timeRange === 'month' &&
                  currentDate.getMonth() === new Date().getMonth() &&
                  currentDate.getFullYear() === new Date().getFullYear()) ||
                (timeRange === 'year' &&
                  currentDate.getFullYear() === new Date().getFullYear())
              }
            >
              →
            </Button>
          </div>
        </div>

        <div className="flex rounded-md overflow-hidden border w-full">
          <Button
            variant={timeRange === 'week' ? 'default' : 'ghost'}
            className="rounded-none flex-1"
            onClick={() => setTimeRange('week')}
          >
            Week
          </Button>
          <Button
            variant={timeRange === 'month' ? 'default' : 'ghost'}
            className="rounded-none border-l border-r flex-1"
            onClick={() => setTimeRange('month')}
          >
            Month
          </Button>
          <Button
            variant={timeRange === 'year' ? 'default' : 'ghost'}
            className="rounded-none flex-1"
            onClick={() => setTimeRange('year')}
          >
            Year
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="booking-trends">Booking Trends</TabsTrigger>
            <TabsTrigger value="popular-sports">Popular Sports</TabsTrigger>
            <TabsTrigger value="peak-hours">Peak Hours</TabsTrigger>
            <TabsTrigger value="recent-bookings">Recent Bookings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Total Bookings</CardTitle>
                  <div className="flex items-baseline space-x-2">
                    <h3 className="text-3xl font-bold">{filteredBookings.length}</h3>
                    {timeRange === 'month' && (
                      <p className="text-sm text-muted-foreground">bookings this month</p>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>For {getTimeRangeLabel()}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Total Revenue</CardTitle>
                  <div className="flex flex-col gap-1">
                    <div className="flex items-baseline space-x-2">
                      <h3 className="text-3xl font-bold">₹{totalRevenue.toLocaleString()}</h3>
                      <span className="text-sm text-muted-foreground">(Gross)</span>
                    </div>
                    <div className="flex items-baseline space-x-2">
                      <h3 className="text-2xl font-semibold text-emerald-600">
                        ₹{totalNetRevenue.toLocaleString()}
                      </h3>
                      <span className="text-sm text-muted-foreground">(Net after {avgFee.toFixed(2)}% fee)</span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>For {getTimeRangeLabel()}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Average Booking Value</CardTitle>
                  <div className="flex items-baseline space-x-2">
                    <h3 className="text-3xl font-bold">
                      ₹{filteredBookings.length > 0
                        ? (totalRevenue / filteredBookings.length).toLocaleString(undefined, {
                            maximumFractionDigits: 2
                          })
                        : 0}
                    </h3>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    <span>Per booking</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="booking-trends" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Booking & Revenue Trends</CardTitle>
                <p className="text-muted-foreground">Track daily bookings and revenue over time</p>
              </CardHeader>
              <CardContent>
                <div className="min-h-[400px] h-[450px] w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart
                      data={trendData}
                      margin={{ top: 16, right: 24, left: 8, bottom: 16 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" interval="preserveStartEnd" minTickGap={8} />
                      <YAxis yAxisId="left" allowDecimals={false} />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="count"
                        stroke="#8884d8"
                        name="Bookings"
                        strokeWidth={3}
                        activeDot={{ r: 10 }}
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="revenue"
                        stroke="#82ca9d"
                        name="Revenue (₹)"
                        strokeWidth={3}
                        dot={{ r: 4 }}
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="popular-sports" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Sport Distribution</CardTitle>
                  <p className="text-muted-foreground">Percentage breakdown of bookings by sport</p>
                </CardHeader>
                <CardContent>
                  <div className="min-h-[300px] h-[350px] w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={sportPopularityData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {sportPopularityData.map((_, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Sport Popularity</CardTitle>
                  <p className="text-muted-foreground">Number of bookings by sport type</p>
                </CardHeader>
                <CardContent>
                  <div className="min-h-[300px] h-[350px] w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsBarChart
                        data={sportPopularityData}
                        layout="vertical"
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis dataKey="name" type="category" width={80} />
                        <Tooltip />
                        <Bar dataKey="value" fill="#8884d8" name="Bookings">
                          {sportPopularityData.map((_, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Bar>
                      </RechartsBarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="peak-hours" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Peak Hours Analysis</CardTitle>
                <p className="text-muted-foreground">Busiest times and booking patterns throughout the day</p>
              </CardHeader>
              <CardContent>
                <div className="min-h-[400px] h-[450px] w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={timeSlotData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#8884d8" name="Bookings" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>

                {/* Peak hours summary */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-semibold text-blue-900">Most Popular Hour</h4>
                    <p className="text-2xl font-bold text-blue-600">
                      {timeSlotData.length > 0
                        ? timeSlotData.reduce((max, slot) => slot.count > max.count ? slot : max, timeSlotData[0]).hour
                        : 'N/A'
                      }
                    </p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <h4 className="font-semibold text-green-900">Peak Bookings</h4>
                    <p className="text-2xl font-bold text-green-600">
                      {timeSlotData.length > 0
                        ? Math.max(...timeSlotData.map(slot => slot.count))
                        : 0
                      }
                    </p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-semibold text-purple-900">Active Hours</h4>
                    <p className="text-2xl font-bold text-purple-600">
                      {timeSlotData.filter(slot => slot.count > 0).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recent-bookings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Bookings</CardTitle>
                <p className="text-muted-foreground">Latest booking activity and details</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {filteredBookings.slice(0, 10).map((booking) => (
                    <div key={booking.id} className="flex justify-between items-center p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{booking.venue_name}</p>
                        <p className="text-sm text-muted-foreground">
                          {booking.sport_name} • {format(parseISO(booking.booking_date), 'MMM dd, yyyy')}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">₹{booking.total_price}</p>
                        <p className="text-sm text-muted-foreground">{booking.payment_method}</p>
                      </div>
                    </div>
                  ))}
                  {filteredBookings.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No bookings found for the selected filters</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
