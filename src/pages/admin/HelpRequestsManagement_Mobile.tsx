
import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Loader2, MessageCircle, CheckCircle, Clock, AlertCircle, MapPin, Tag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { AdminHelpChatInterface } from '@/components/AdminHelpChatInterface';
import { HelpRequest, GetHelpRequestsResult, UpdateHelpRequestStatusResult, HELP_STATUS } from '@/types/help';

const CATEGORY_LABELS = {
  booking_issues: 'Booking',
  facility_questions: 'Facilities', 
  payment_problems: 'Payment',
  general: 'General'
};

const HelpRequestsManagement_Mobile: React.FC = () => {
  const { userRole } = useAuth();
  const [helpRequests, setHelpRequests] = useState<HelpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);
  const [showChat, setShowChat] = useState(false);

  // Function to fetch help requests
  const fetchHelpRequests = async () => {
    if (userRole !== 'super_admin') {
      setHelpRequests([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .rpc('get_help_requests')
        .returns<GetHelpRequestsResult>();

      if (error) throw error;
      setHelpRequests(data || []);
    } catch (error) {
      console.error('Error fetching help requests:', error);
      toast({
        title: 'Error',
        description: 'Failed to load help requests',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to mark a request as resolved
  const markAsResolved = async (requestId: string) => {
    try {
      console.log('Calling update_help_request_status function...');
      const { data, error } = await supabase
        .rpc('update_help_request_status', {
          p_help_request_id: requestId,
          p_status: HELP_STATUS.RESOLVED
        })
        .returns<UpdateHelpRequestStatusResult>();

      console.log('RPC response:', { data, error });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Help request marked as resolved'
      });

      console.log('Refreshing help requests...');
      await fetchHelpRequests();

      // Close the chat modal after successful resolution
      setShowChat(false);
      setSelectedRequest(null);
    } catch (error) {
      console.error('Error updating help request:', error);
      toast({
        title: 'Error',
        description: `Failed to update help request: ${error.message || 'Unknown error'}`,
        variant: 'destructive'
      });
    }
  };

  useEffect(() => {
    fetchHelpRequests();
  }, [userRole]);

  if (userRole !== 'super_admin') {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg">
        <p className="text-gray-600">Only super admins can access help requests</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-indigo" />
      </div>
    );
  }

  return (
    <div className="p-2 max-w-md mx-auto">
      <h2 className="text-lg font-bold mb-3 text-white">Help Requests</h2>
      <div className="flex flex-col gap-2">
        {helpRequests.length === 0 ? (
          <div className="bg-emerald-800 rounded-lg p-8 text-center">
            <AlertCircle className="h-12 w-12 mx-auto text-gray-400 mb-3" />
            <h3 className="text-lg font-medium text-white mb-1">No help requests found</h3>
          </div>
        ) : (
          helpRequests.map(request => (
            <button 
              key={request.id} 
              className={`flex flex-col items-start bg-navy-800 rounded-lg shadow px-3 py-3 gap-2 ${
                selectedRequest === request.id ? 'ring-2 ring-indigo-400' : ''
              }`} 
              onClick={() => { 
                setSelectedRequest(request.id); 
                setShowChat(true); 
              }}
            >
              <div className="flex items-center justify-between w-full">
                <span className="font-semibold text-white text-sm truncate flex-1">
                  {request.subject}
                </span>
                <div className="flex gap-1 ml-2">
                  <Badge className={`text-[10px] px-2 py-0.5 rounded-full font-semibold ${
                    request.status === 'resolved' 
                      ? 'bg-green-900 text-green-300' 
                      : 'bg-yellow-900 text-yellow-300'
                  }`}>
                    {request.status}
                  </Badge>
                </div>
              </div>
              
              <div className="w-full space-y-1">
                <div className="text-xs text-gray-300">
                  {request.user_name} ({request.user_email})
                </div>
                
                {request.venue_name && (
                  <div className="flex items-center text-xs text-gray-400">
                    <MapPin className="h-3 w-3 mr-1" />
                    <span>{request.venue_name}</span>
                  </div>
                )}
                
                <div className="flex items-center text-xs text-gray-400">
                  <Tag className="h-3 w-3 mr-1" />
                  <span>{CATEGORY_LABELS[request.category as keyof typeof CATEGORY_LABELS] || request.category}</span>
                </div>
              </div>
              
              <div className="flex items-center mt-1 text-xs w-full">
                {request.status === 'pending' ? (
                  <Clock className="h-3 w-3 mr-1 text-yellow-500" />
                ) : (
                  <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                )}
                <span className="text-gray-500">
                  Last activity: {new Date(request.last_message_at).toLocaleString('en-US', { 
                    month: 'short', 
                    day: 'numeric', 
                    hour: 'numeric', 
                    minute: 'numeric', 
                    hour12: true 
                  })}
                </span>
              </div>
            </button>
          ))
        )}
      </div>
      
      {/* Chat Drawer/Modal */}
      {showChat && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-2 z-50 overflow-y-auto">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto flex flex-col">
            <div className="flex items-center justify-between px-4 py-2 border-b">
              <div className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5 text-indigo-400" />
                <span className="font-semibold truncate">
                  {helpRequests.find(r => r.id === selectedRequest)?.subject}
                </span>
              </div>
              <button 
                onClick={() => setShowChat(false)} 
                className="text-gray-500 hover:text-red-500 text-lg"
              >
                ×
              </button>
            </div>
            <div className="flex-1 overflow-y-auto">
              <AdminHelpChatInterface
                selectedRequestId={selectedRequest}
                onMarkResolved={() => markAsResolved(selectedRequest)}
                helpRequests={helpRequests}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HelpRequestsManagement_Mobile;
