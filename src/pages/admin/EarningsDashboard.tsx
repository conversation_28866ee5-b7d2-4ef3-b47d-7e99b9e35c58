import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  Calendar, DollarSign, TrendingUp, Clock,
  Banknote, Activity, Eye, ChevronRight,
  Loader2, AlertCircle, CheckCircle, RefreshCw,
  ArrowLeft, ArrowRight, Download, Filter, Settings
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { format, startOfWeek, endOfWeek, addDays, subDays, parseISO } from 'date-fns';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "react-hot-toast";
import SettlementManagementModal from '@/components/admin/SettlementManagementModal';
import * as XLSX from 'xlsx';
import { getActiveDaysDisplay, getActiveDaysLabel } from '@/utils/weeklyUtils';

interface DailyEarnings {
  venue_id: string;
  venue_name: string;
  cycle_date: string;
  cycle_day_name: string;
  total_bookings: number;
  confirmed_bookings: number;
  gross_revenue: number;
  platform_fee_amount: number;
  net_revenue: number;
  is_current_day: boolean;
  is_frozen: boolean;
}

interface Settlement {
  settlement_id: string;
  venue_id: string;
  venue_name: string;
  settlement_week_start: string;
  settlement_week_end: string;
  settlement_reference: string;
  total_gross_revenue: number;
  total_platform_fees: number;
  total_net_revenue: number;
  total_bookings: number;
  status: 'pending' | 'processed' | 'settled';
  expected_settlement_date: string;
  created_at: string;
  notes?: string;
}

interface EarningsDashboardProps {
  userRole: string;
  adminVenues: Array<{ venue_id: string }>;
}

const EarningsDashboard: React.FC<EarningsDashboardProps> = ({ userRole, adminVenues }) => {
  const { user } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dailyEarnings, setDailyEarnings] = useState<DailyEarnings[]>([]);
  const [settlements, setSettlements] = useState<Settlement[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [selectedSettlement, setSelectedSettlement] = useState<Settlement | null>(null);
  const [settlementDetailsOpen, setSettlementDetailsOpen] = useState(false);
  const [managementOpen, setManagementOpen] = useState(false);
  const [downloadingReport, setDownloadingReport] = useState(false);

  // Fetch earnings data
  const fetchEarningsData = async (showRefreshIndicator = false) => {
    if (!user?.id) return;
    
    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      const weekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
      
      // Fetch daily earnings for current week
      const { data: earningsData, error: earningsError } = await supabase
        .rpc('get_admin_daily_earnings', {
          p_start_date: format(currentWeekStart, 'yyyy-MM-dd'),
          p_end_date: format(weekEnd, 'yyyy-MM-dd'),
          p_venue_id: selectedVenue === 'all' ? null : selectedVenue
        });

      if (earningsError) throw earningsError;

      setDailyEarnings(earningsData || []);

      // Fetch settlements
      const { data: settlementsData, error: settlementsError } = await supabase
        .rpc('get_admin_settlements', {
          p_venue_id: selectedVenue === 'all' ? null : selectedVenue,
          p_status: null,
          p_limit: 20
        });

      if (settlementsError) throw settlementsError;

      setSettlements(settlementsData || []);

    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchEarningsData();
  }, [user?.id, currentWeekStart, selectedVenue]);

  // Settlement management functions
  const openManagement = (settlement: Settlement) => {
    setSelectedSettlement(settlement);
    setManagementOpen(true);
  };

  const handleSettlementUpdate = () => {
    fetchEarningsData(true);
    setManagementOpen(false);
    setSelectedSettlement(null);
  };

  // Download settlement report function (standardized to match mobile format exactly)
  const downloadSettlementReport = async (settlement: Settlement) => {
    if (!settlement || (settlement.status !== 'processed' && settlement.status !== 'settled')) {
      toast.error('Report is only available for processed or settled settlements');
      return;
    }

    setDownloadingReport(true);
    try {
      console.log('Downloading settlement report for:', settlement.settlement_reference);

      // Step 1: Fetch booking data WITHOUT user join (to avoid foreign key error) - EXACT MOBILE MATCH
      const { data: bookingsData, error: bookingError } = await supabase
        .from('bookings')
        .select(`
          *,
          court:courts!inner(
            id,
            name,
            venue:venues!inner(
              id,
              name,
              platform_fee_percentage
            )
          )
        `)
        .gte('booking_date', settlement.settlement_week_start)
        .lte('booking_date', settlement.settlement_week_end)
        .eq('court.venue.id', settlement.venue_id)
        .in('status', ['confirmed', 'completed']);

      if (bookingError) {
        console.error('Error fetching booking data:', bookingError);
        throw bookingError;
      }

      console.log('Fetched booking data:', bookingsData);

      // Step 2: Extract unique user IDs and batch fetch profiles - EXACT MOBILE MATCH
      const userIds = [...new Set(
        bookingsData
          ?.filter(booking => booking.user_id)
          .map(booking => booking.user_id)
      )].filter(Boolean);

      let profilesMap = new Map();

      if (userIds.length > 0) {
        const { data: profilesData, error: profileError } = await supabase
          .from('profiles')
          .select('id, full_name, phone')
          .in('id', userIds);

        if (profileError) {
          console.error('Error fetching profiles:', profileError);
          throw profileError;
        }

        console.log('Fetched profiles:', profilesData);

        // Create lookup map for O(1) access
        profilesData?.forEach(profile => {
          profilesMap.set(profile.id, profile);
        });
      }

      // Step 3: Filter bookings to only include those with valid user profiles - EXACT MOBILE MATCH
      const bookingsWithProfiles = bookingsData?.filter(booking =>
        booking.user_id && profilesMap.has(booking.user_id)
      ) || [];

      console.log('Bookings with valid profiles:', bookingsWithProfiles);

      // Prepare Excel data using EXACT MOBILE FORMAT (Array of Arrays)
      const excelData = [];

      // Add settlement summary header - EXACT MOBILE MATCH
      excelData.push([
        'SETTLEMENT SUMMARY',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
      ]);

      excelData.push([
        'Settlement Reference',
        'Venue',
        'Period',
        'Status',
        'Total Bookings',
        'Gross Revenue',
        'Platform Fees',
        'Net Revenue',
        'Expected Settlement Date',
        '',
        '',
        ''
      ]);

      excelData.push([
        settlement.settlement_reference,
        settlement.venue_name,
        `${format(parseISO(settlement.settlement_week_start), 'dd MMM')} - ${format(parseISO(settlement.settlement_week_end), 'dd MMM, yyyy')}`,
        settlement.status.charAt(0).toUpperCase() + settlement.status.slice(1),
        settlement.total_bookings,
        settlement.total_gross_revenue,
        settlement.total_platform_fees,
        settlement.total_net_revenue,
        format(parseISO(settlement.expected_settlement_date), 'dd MMM, yyyy'),
        '',
        '',
        ''
      ]);

      // Add empty row - EXACT MOBILE MATCH
      excelData.push(['', '', '', '', '', '', '', '', '', '', '', '']);

      // Add booking details header - EXACT MOBILE MATCH
      excelData.push([
        'BOOKING DETAILS',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
      ]);

      excelData.push([
        'Date',
        'Customer Name',
        'Customer Phone',
        'Court',
        'Start Time',
        'End Time',
        'Amount',
        'Platform Fee',
        'Net Amount',
        'Payment Method',
        'Booking Reference'
      ]);

      // Add individual booking rows (only for bookings with valid profiles) - EXACT MOBILE MATCH
      if (bookingsWithProfiles.length > 0) {
        bookingsWithProfiles.forEach(booking => {
          const profile = profilesMap.get(booking.user_id);
          const platformFeePercent = booking.court?.venue?.platform_fee_percentage || 5;
          const totalPrice = parseFloat(booking.total_price) || 0;
          const platformFee = totalPrice * (platformFeePercent / 100);
          const netAmount = totalPrice - platformFee;

          excelData.push([
            format(parseISO(booking.booking_date), 'dd MMM, yyyy'),
            profile?.full_name || 'N/A',
            profile?.phone || 'N/A',
            booking.court?.name || 'N/A',
            booking.start_time || 'N/A',
            booking.end_time || 'N/A',
            totalPrice.toFixed(2),
            platformFee.toFixed(2),
            netAmount.toFixed(2),
            booking.payment_method || 'N/A',
            booking.booking_reference || 'N/A'
          ]);
        });
      } else {
        excelData.push([
          'No bookings with valid user profiles found for this settlement period',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          ''
        ]);
      }

      // Create and download Excel file using EXACT MOBILE METHOD
      const ws = XLSX.utils.aoa_to_sheet(excelData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Settlement Report');

      // Generate filename using EXACT MOBILE FORMAT
      const filename = `${settlement.settlement_reference}_Settlement_Report.xlsx`;

      // Download file
      XLSX.writeFile(wb, filename);

      toast.success('Settlement report downloaded successfully!');
    } catch (error) {
      console.error('Error downloading settlement report:', error);
      toast.error('Failed to download settlement report');
    } finally {
      setDownloadingReport(false);
    }
  };

  // Navigate to previous/next week
  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeekStart = direction === 'prev' 
      ? subDays(currentWeekStart, 7)
      : addDays(currentWeekStart, 7);
    setCurrentWeekStart(newWeekStart);
  };

  // Get status badge for settlements
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return { color: 'bg-yellow-500', icon: <Clock className="w-3 h-3" />, text: 'Pending' };
      case 'processed':
        return { color: 'bg-blue-500', icon: <AlertCircle className="w-3 h-3" />, text: 'Processed' };
      case 'settled':
        return { color: 'bg-green-500', icon: <CheckCircle className="w-3 h-3" />, text: 'Settled' };
      default:
        return { color: 'bg-gray-500', icon: <Clock className="w-3 h-3" />, text: status };
    }
  };

  // Calculate weekly summary
  const weeklySummary = {
    total_gross: dailyEarnings.reduce((sum, day) => sum + (day.gross_revenue || 0), 0),
    total_net: dailyEarnings.reduce((sum, day) => sum + (day.net_revenue || 0), 0),
    total_bookings: dailyEarnings.reduce((sum, day) => sum + (day.total_bookings || 0), 0),
    days_with_data: dailyEarnings.filter(day => day.total_bookings > 0).length
  };

  // Get today's earnings
  const todayEarnings = dailyEarnings.find(day => day.is_current_day);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-500 mx-auto mb-4" />
          <p className="text-emerald-200">Loading earnings data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Earnings & Settlements</h1>
          <p className="text-emerald-200">Track daily earnings and weekly settlements</p>
        </div>
        <div className="flex items-center space-x-3">
          <Select value={selectedVenue} onValueChange={setSelectedVenue}>
            <SelectTrigger className="w-48 bg-black/40 border-white/20 text-white">
              <SelectValue placeholder="Select venue" />
            </SelectTrigger>
            <SelectContent className="bg-navy-dark border-white/20">
              <SelectItem value="all" className="text-white">All Venues</SelectItem>
              {adminVenues.map((venue) => (
                <SelectItem key={venue.venue_id} value={venue.venue_id} className="text-white">
                  Venue {venue.venue_id.slice(0, 8)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fetchEarningsData(true)}
            disabled={refreshing}
            className="text-white hover:bg-white/10"
          >
            <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-emerald-900/50 rounded-xl">
          <TabsTrigger value="overview" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white text-emerald-200">
            Overview
          </TabsTrigger>
          <TabsTrigger value="daily" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white text-emerald-200">
            Daily Earnings
          </TabsTrigger>
          <TabsTrigger value="settlements" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white text-emerald-200">
            Settlements
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Today's Performance */}
            <Card className="bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-emerald-500/30">
              <CardHeader className="pb-3">
                <CardTitle className="text-emerald-300 flex items-center text-sm">
                  <Activity className="w-4 h-4 mr-2" />
                  Today's Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">
                  ₹{todayEarnings?.gross_revenue?.toFixed(0) || '0'}
                </div>
                <div className="text-sm text-emerald-200">
                  Net: ₹{todayEarnings?.net_revenue?.toFixed(0) || '0'}
                </div>
              </CardContent>
            </Card>

            {/* Weekly Summary Cards */}
            <Card className="bg-black/40 border-white/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Week Gross
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-300">
                  ₹{weeklySummary.total_gross.toFixed(0)}
                </div>
                <div className="text-sm text-gray-400">
                  {weeklySummary.total_bookings} bookings
                </div>
              </CardContent>
            </Card>

            <Card className="bg-black/40 border-white/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center text-sm">
                  <Banknote className="w-4 h-4 mr-2" />
                  Week Net
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-300">
                  ₹{weeklySummary.total_net.toFixed(0)}
                </div>
                <div className="text-sm text-gray-400">
                  After platform fees
                </div>
              </CardContent>
            </Card>

            <Card className="bg-black/40 border-white/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center text-sm">
                  <Calendar className="w-4 h-4 mr-2" />
                  Active Days
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-300">
                  {getActiveDaysDisplay(currentWeekStart, weeklySummary.days_with_data)}
                </div>
                <div className="text-sm text-gray-400">
                  {getActiveDaysLabel(currentWeekStart)}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="daily" className="mt-6">
          {/* Week Navigation */}
          <div className="flex items-center justify-between bg-black/40 rounded-lg p-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateWeek('prev')}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Previous Week
            </Button>
            <div className="text-center">
              <div className="font-semibold text-white">
                {format(currentWeekStart, 'MMM dd')} - {format(endOfWeek(currentWeekStart, { weekStartsOn: 1 }), 'MMM dd, yyyy')}
              </div>
              <div className="text-sm text-emerald-300">Weekly Cycle</div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateWeek('next')}
              className="text-white hover:bg-white/10"
            >
              Next Week
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </div>

          {/* Daily Earnings Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {dailyEarnings.map((day) => (
              <Card key={`${day.venue_id}-${day.cycle_date}`} className="bg-black/40 border-white/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white text-sm flex items-center justify-between">
                    <span>{day.cycle_day_name}</span>
                    {day.is_current_day && <Badge className="bg-emerald-500 text-white text-xs">Today</Badge>}
                  </CardTitle>
                  <div className="text-xs text-gray-400">{format(parseISO(day.cycle_date), 'MMM dd')}</div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-300">Gross:</span>
                      <span className="text-sm font-semibold text-emerald-300">₹{day.gross_revenue.toFixed(0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-300">Net:</span>
                      <span className="text-sm font-semibold text-green-300">₹{day.net_revenue.toFixed(0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-300">Bookings:</span>
                      <span className="text-sm font-semibold text-blue-300">{day.total_bookings}</span>
                    </div>
                    {day.venue_name && (
                      <div className="text-xs text-gray-400 truncate">{day.venue_name}</div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settlements" className="mt-6">
          {/* Settlements List */}
          <div className="space-y-4">
            {settlements.length === 0 ? (
              <Card className="bg-black/40 border-white/20">
                <CardContent className="p-8 text-center">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-lg text-gray-300 mb-2">No settlements found</div>
                  <div className="text-sm text-gray-400">Settlements will appear here once weekly cycles are processed</div>
                </CardContent>
              </Card>
            ) : (
              settlements.map((settlement) => {
                const statusBadge = getStatusBadge(settlement.status);
                
                return (
                  <Card 
                    key={settlement.settlement_id}
                    className="bg-black/40 border-white/20 cursor-pointer hover:bg-black/60 transition-colors"
                    onClick={() => {
                      setSelectedSettlement(settlement);
                      setSettlementDetailsOpen(true);
                    }}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-3">
                            <Badge className={`${statusBadge.color} text-white flex items-center space-x-1`}>
                              {statusBadge.icon}
                              <span>{statusBadge.text}</span>
                            </Badge>
                            <span className="text-sm font-mono text-gray-300">{settlement.settlement_reference}</span>
                          </div>
                          
                          <div className="text-white font-semibold">{settlement.venue_name}</div>
                          
                          <div className="text-sm text-gray-400">
                            {format(parseISO(settlement.settlement_week_start), 'MMM dd')} - {format(parseISO(settlement.settlement_week_end), 'MMM dd, yyyy')}
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-2xl font-bold text-emerald-400">₹{settlement.total_net_revenue.toFixed(0)}</div>
                          <div className="text-sm text-gray-400">Net Revenue</div>
                          <div className="text-xs text-gray-500">{settlement.total_bookings} bookings</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Enhanced Settlement Details Modal */}
      <Dialog open={settlementDetailsOpen} onOpenChange={setSettlementDetailsOpen}>
        <DialogContent className="bg-navy-dark border-white/20 text-white max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="text-emerald-300 flex items-center justify-between">
              Settlement Details
              {userRole === 'super_admin' && selectedSettlement && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSettlementDetailsOpen(false);
                    openManagement(selectedSettlement);
                  }}
                  className="text-yellow-300 hover:bg-yellow-500/20"
                >
                  <Settings className="w-4 h-4 mr-1" />
                  Manage
                </Button>
              )}
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className="flex-1 overflow-y-auto pr-4">
            {selectedSettlement && (
              <div className="space-y-6">
                {/* Super Admin Management Notice */}
                {userRole === 'super_admin' && (
                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <Settings className="w-4 h-4 text-yellow-400 mt-0.5 shrink-0" />
                      <div className="text-xs text-yellow-300">
                        <strong>Super Admin:</strong> You can manage this settlement's status, notes, and dates using the "Manage" button above.
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm text-gray-400">Reference</label>
                      <div className="font-mono text-sm">{selectedSettlement.settlement_reference}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Venue</label>
                      <div className="font-semibold">{selectedSettlement.venue_name}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Settlement Period</label>
                      <div>
                        {format(parseISO(selectedSettlement.settlement_week_start), 'MMM dd')} - {format(parseISO(selectedSettlement.settlement_week_end), 'MMM dd, yyyy')}
                      </div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Status</label>
                      <div className="mt-1">
                        <Badge className={`${getStatusBadge(selectedSettlement.status).color} text-white`}>
                          {getStatusBadge(selectedSettlement.status).text}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="text-sm text-gray-400">Expected Settlement Date</label>
                      <div>{format(parseISO(selectedSettlement.expected_settlement_date), 'MMM dd, yyyy')}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Total Bookings</label>
                      <div className="font-semibold text-blue-300">{selectedSettlement.total_bookings}</div>
                    </div>

                    <div>
                      <label className="text-sm text-gray-400">Created</label>
                      <div>{format(parseISO(selectedSettlement.created_at), 'MMM dd, yyyy HH:mm')}</div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Revenue Breakdown */}
                <div className="bg-emerald-500/20 rounded-lg p-6 space-y-4">
                  <h3 className="text-lg font-semibold text-emerald-300 mb-4">Financial Summary</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-emerald-300">₹{selectedSettlement.total_gross_revenue.toFixed(0)}</div>
                      <div className="text-sm text-emerald-200">Gross Revenue</div>
                    </div>

                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-300">₹{selectedSettlement.total_platform_fees.toFixed(0)}</div>
                      <div className="text-sm text-red-200">Platform Fees</div>
                    </div>

                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-300">₹{selectedSettlement.total_net_revenue.toFixed(0)}</div>
                      <div className="text-sm text-green-200">Net Revenue</div>
                    </div>
                  </div>

                  <div className="border-t border-emerald-400/30 pt-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-emerald-200">Platform Fee Rate:</span>
                      <span className="font-semibold">{((selectedSettlement.total_platform_fees / selectedSettlement.total_gross_revenue) * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-emerald-200">Average per Booking:</span>
                      <span className="font-semibold">₹{(selectedSettlement.total_gross_revenue / selectedSettlement.total_bookings).toFixed(0)}</span>
                    </div>
                  </div>
                </div>

                {selectedSettlement.notes && (
                  <div className="bg-gray-500/20 rounded-lg p-4">
                    <label className="text-sm text-gray-400">Admin Notes</label>
                    <div className="text-white mt-2">{selectedSettlement.notes}</div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-3">
                  {/* Download Report Button */}
                  {(selectedSettlement.status === 'processed' || selectedSettlement.status === 'settled') && (
                    <Button
                      onClick={() => downloadSettlementReport(selectedSettlement)}
                      disabled={downloadingReport}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                      {downloadingReport ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Generating Report...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Download Detailed Report
                        </>
                      )}
                    </Button>
                  )}

                  <Button
                    onClick={() => window.alert('Feature coming soon!')}
                    className="w-full"
                    variant="outline"
                  >
                    Contact Support
                  </Button>
                </div>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Settlement Management Modal (Super Admin Only) */}
      <SettlementManagementModal
        isOpen={managementOpen}
        onClose={() => {
          setManagementOpen(false);
          setSelectedSettlement(null);
        }}
        settlement={selectedSettlement}
        onUpdate={handleSettlementUpdate}
      />
    </div>
  );
};

export default EarningsDashboard;
