import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { AdminBookingInfo, Booking, BookingStatus } from '@/types/help';
import BookingsList from '@/components/admin/BookingsList';
import { protectedBookingUpdate } from '@/utils/protectedSupabase';
import { logSecurityEvent } from '@/utils/adminSecurity';
import { Search, X } from 'lucide-react';

const Bookings_Mobile: React.FC = () => {
  const { userRole, user } = useAuth();
  const [adminVenues, setAdminVenues] = useState<{ venue_id: string }[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | BookingStatus>('all');
  const [paymentFilter, setPaymentFilter] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<'all' | 'cash' | 'online' | 'card' | 'free'>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    const fetchAdminVenues = async () => {
      if (userRole === 'admin') {
        const { data, error } = await supabase.rpc('get_admin_venues');
        if (!error) setAdminVenues(data || []);
      } else if (userRole === 'super_admin') {
        setAdminVenues([]); // super_admin can see all venues
      }
    };
    fetchAdminVenues();
  }, [userRole]);

  const fetchBookings = useCallback(async () => {
    setLoading(true);
    try {
      console.log('🔒 Fetching bookings with security and performance optimization...');

      // ✅ PERFORMANCE OPTIMIZED: Single query with INNER joins for venue filtering
      let query = supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          total_price,
          status,
          booking_reference,
          payment_reference,
          payment_status,
          payment_method,
          user_id,
          guest_name,
          guest_phone,
          created_at,
          booked_by_admin_id,
          cancellation_reason,
          court:courts!inner (
            id,
            name,
            venue_id,
            venue:venues!inner (
              id,
              name
            ),
            sport:sports (
              id,
              name
            )
          ),
          admin_booking:admin_bookings(
            id,
            booking_id,
            admin_id,
            customer_name,
            customer_phone,
            payment_method,
            payment_status,
            amount_collected,
            created_at,
            notes
          )
        `)
        .order('booking_date', { ascending: false })
        .order('created_at', { ascending: false });

      // ✅ SECURITY & PERFORMANCE: Apply venue filtering with joins for admin users
      if (userRole === 'admin' && adminVenues.length > 0) {
        const venueIds = adminVenues.map(v => v.venue_id);
        // Use the joined venue table for filtering - single query, no round trips
        query = query.in('court.venue.id', venueIds);
      }

      // Apply other filters
      if (filter !== 'all') {
        query = query.eq('status', filter);
      }
      if (paymentFilter !== 'all') {
        query = query.eq('payment_status', paymentFilter);
      }
      if (paymentMethodFilter !== 'all') {
        query = query.eq('payment_method', paymentMethodFilter);
      }

      // Add search functionality for booking reference
      if (searchQuery.trim()) {
        const trimmedQuery = searchQuery.trim().toUpperCase();
        // Search by booking reference (exact match or partial)
        if (trimmedQuery.startsWith('GR2P-')) {
          query = query.ilike('booking_reference', `%${trimmedQuery}%`);
        } else {
          // If user types without GR2P- prefix, add it
          query = query.ilike('booking_reference', `%GR2P-${trimmedQuery}%`);
        }
      }

      const { data, error } = await query;
      if (error) throw error;

      // ✅ PERFORMANCE OPTIMIZED: Batch profile queries instead of N+1
      if (data && data.length > 0) {
        // Get unique user IDs and admin IDs
        const userIds = [...new Set(data.map(b => b.user_id).filter(Boolean))];
        const adminIds = [...new Set(data.map(b => b.booked_by_admin_id).filter(Boolean))];

        // ✅ PRIVACY COMPLIANT: Batch fetch user profiles (name + phone, NO email)
        const userProfilesPromise = userIds.length > 0
          ? supabase.from('profiles').select('id, full_name, phone').in('id', userIds)
          : Promise.resolve({ data: [], error: null });

        // ✅ PRIVACY COMPLIANT: Batch fetch admin profiles (name only, NO email)
        const adminProfilesPromise = adminIds.length > 0
          ? supabase.from('profiles').select('id, full_name').in('id', adminIds)
          : Promise.resolve({ data: [], error: null });

        // Execute batch queries in parallel
        const [userProfilesResult, adminProfilesResult] = await Promise.all([
          userProfilesPromise,
          adminProfilesPromise
        ]);

        // Create lookup maps for O(1) access
        const userProfileMap = new Map();
        const adminProfileMap = new Map();

        userProfilesResult.data?.forEach(profile => {
          userProfileMap.set(profile.id, profile);
        });

        adminProfilesResult.data?.forEach(profile => {
          adminProfileMap.set(profile.id, profile);
        });

        // Process bookings with profile data
        const processedBookings = data.map(booking => {
          const processedBooking: Booking = {
            ...booking,
            admin_booking: null,
            court: booking.court,
            status: booking.status as BookingStatus,
          };

          // Handle admin_booking - if it's an array with elements, take the first one
          if (booking.admin_booking && Array.isArray(booking.admin_booking) && booking.admin_booking.length > 0) {
            processedBooking.admin_booking = booking.admin_booking[0] as AdminBookingInfo;
          }

          // ✅ PRIVACY COMPLIANT: Add user profile data from lookup map (name + phone, NO email)
          if (booking.user_id && userProfileMap.has(booking.user_id)) {
            const userProfile = userProfileMap.get(booking.user_id);
            processedBooking.user_info = {
              full_name: userProfile.full_name,
              phone: userProfile.phone,
              email: null // ✅ EMAIL EXCLUDED for admin privacy compliance
            };
          }

          // ✅ PRIVACY COMPLIANT: Add admin profile data from lookup map (name only, NO email)
          if (booking.booked_by_admin_id && adminProfileMap.has(booking.booked_by_admin_id)) {
            const adminProfile = adminProfileMap.get(booking.booked_by_admin_id);
            processedBooking.admin_info = {
              full_name: adminProfile.full_name,
              email: null // ✅ EMAIL EXCLUDED for privacy
            };
          }

          return processedBooking;
        });

        setBookings(processedBookings);
      } else {
        setBookings([]);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load bookings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [filter, paymentFilter, paymentMethodFilter, searchQuery, userRole, adminVenues]);

  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  const updateBookingStatus = async (bookingId: string, status: BookingStatus, cancellationReason?: string) => {
    try {
      // Security check: Only allow cancellation and completion
      if (status !== 'cancelled' && status !== 'completed') {
        throw new Error('Invalid status change. Only cancellation and completion are allowed.');
      }

      if (!user?.id) {
        throw new Error('User authentication required');
      }

      // ✅ SECURITY ENHANCEMENT: Use protected booking update with role validation
      await protectedBookingUpdate(
        bookingId,
        status,
        {
          userId: user.id,
          fallbackToUnsafe: true, // Maintain backwards compatibility
          logOnly: true, // Only log security events for now
        },
        cancellationReason
      );

      // Log the security-validated operation
      logSecurityEvent('MOBILE_BOOKING_STATUS_UPDATE', user.id, {
        bookingId,
        status,
        cancellationReason,
        userRole
      });

      toast({
        title: 'Status Updated',
        description: status === 'cancelled'
          ? `Booking has been cancelled. Reason: ${cancellationReason}`
          : `Booking has been marked as ${status}`,
      });

      fetchBookings();
    } catch (error) {
      console.error('Error updating booking:', error);
      toast({
        title: 'Error',
        description: 'Failed to update booking status',
        variant: 'destructive',
      });
      throw error; // Re-throw to handle in the modal
    }
  };

  return (
    <div className="p-4 max-w-md mx-auto">
      <h2 className="text-xl font-semibold mb-4">Booking Management</h2>

      {/* Search by Booking Reference */}
      <div className="mb-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Search by Booking Reference:</h3>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Enter booking reference (e.g., GR2P-1431D93D or 1431D93D)"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-sport-green focus:border-transparent"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={() => setFilter('all')}
          className={`px-3 py-1 text-sm rounded-md ${
            filter === 'all' 
              ? 'bg-sport-green text-white' 
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          All
        </button>
        <button
          onClick={() => setFilter('confirmed')}
          className={`px-3 py-1 text-sm rounded-md ${
            filter === 'confirmed' 
              ? 'bg-green-600 text-white' 
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          Confirmed
        </button>
        <button
          onClick={() => setFilter('cancelled')}
          className={`px-3 py-1 text-sm rounded-md ${
            filter === 'cancelled' 
              ? 'bg-red-600 text-white' 
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          Cancelled
        </button>
        <button
          onClick={() => setFilter('completed')}
          className={`px-3 py-1 text-sm rounded-md ${
            filter === 'completed' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          Completed
        </button>
        <button
          onClick={() => setFilter('pending')}
          className={`px-3 py-1 text-sm rounded-md ${
            filter === 'pending' 
              ? 'bg-yellow-600 text-white' 
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          Pending
        </button>
      </div>
      {/* Payment filter controls */}
      <div className="mb-3">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Filter by Payment Status:</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setPaymentFilter('all')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentFilter === 'all' 
                ? 'bg-sport-green text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            All Payments
          </button>
          <button
            onClick={() => setPaymentFilter('completed')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentFilter === 'completed' 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            Completed
          </button>
          <button
            onClick={() => setPaymentFilter('pending')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentFilter === 'pending' 
                ? 'bg-yellow-600 text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            Pending
          </button>
          <button
            onClick={() => setPaymentFilter('failed')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentFilter === 'failed' 
                ? 'bg-red-600 text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            Failed
          </button>
        </div>
      </div>
      {/* Payment method filter controls */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Filter by Payment Method:</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setPaymentMethodFilter('all')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentMethodFilter === 'all' 
                ? 'bg-sport-green text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            All Methods
          </button>
          <button
            onClick={() => setPaymentMethodFilter('cash')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentMethodFilter === 'cash' 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            Cash
          </button>
          <button
            onClick={() => setPaymentMethodFilter('online')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentMethodFilter === 'online' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            Online
          </button>
          <button
            onClick={() => setPaymentMethodFilter('card')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentMethodFilter === 'card' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            Card
          </button>
          <button
            onClick={() => setPaymentMethodFilter('free')}
            className={`px-3 py-1 text-xs rounded-md ${
              paymentMethodFilter === 'free' 
                ? 'bg-gray-600 text-white' 
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            Free
          </button>
        </div>
      </div>
      <BookingsList 
        bookings={bookings} 
        isLoading={loading} 
        onStatusUpdate={updateBookingStatus} 
      />
    </div>
  );
};

export default Bookings_Mobile; 
