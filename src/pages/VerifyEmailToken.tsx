import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import Header from '../components/Header';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';

const VerifyEmailToken: React.FC = () => {
  const isMobile = useIsMobile();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [verificationState, setVerificationState] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const verifyToken = async () => {
      const token = searchParams.get('token');
      
      if (!token) {
        setVerificationState('error');
        setErrorMessage('No verification token provided');
        return;
      }

      try {
        // Use the new WhatsApp email verification system
        const { data, error } = await supabase.functions.invoke('verify-whatsapp-email-token', {
          body: { token }
        });

        const result = error ? { success: false, error: error.message } : data;

        if (result.success) {
          setVerificationState('success');
          toast({
            title: "Email verified successfully!",
            description: "Your account has been created. You can now sign in.",
          });
          
          // Redirect to login after a delay
          setTimeout(() => {
            navigate('/login');
          }, 3000);
        } else {
          setVerificationState('error');
          setErrorMessage(result.error || 'Verification failed');
          toast({
            title: "Verification failed",
            description: result.error || 'Please try again or request a new verification email.',
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Verification error:', error);
        setVerificationState('error');
        setErrorMessage('An unexpected error occurred');
        toast({
          title: "Verification failed",
          description: "An unexpected error occurred. Please try again.",
          variant: "destructive",
        });
      }
    };

    verifyToken();
  }, [searchParams, navigate]);

  const handleGoToLogin = () => {
    navigate('/login');
  };

  const handleGoToRegister = () => {
    navigate('/register');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900">
      <Header />
      
      <div className="pt-32 pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-lg mx-auto backdrop-blur-sm bg-white/10 rounded-xl shadow-xl overflow-hidden border border-white/20">
            <div className="p-8 text-center">
              {verificationState === 'loading' && (
                <>
                  <div className="w-20 h-20 bg-indigo-light bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Loader2 className="h-10 w-10 text-indigo-light animate-spin" />
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-4">Verifying Email</h1>
                  <p className="text-gray-300 mb-6">
                    Please wait while we verify your email address...
                  </p>
                </>
              )}

              {verificationState === 'success' && (
                <>
                  <div className="w-20 h-20 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="h-10 w-10 text-green-500" />
                  </div>
                  <h1 className="text-3xl font-bold text-green-500 mb-4">Email Verified!</h1>
                  <p className="text-gray-300 mb-6">
                    Your email has been successfully verified. Your account is now active and you can sign in.
                  </p>
                  <p className="text-indigo-light mb-4 text-sm">
                    Redirecting to login page in a few seconds...
                  </p>
                  <button
                    onClick={handleGoToLogin}
                    className="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-md font-bold shadow-lg hover:from-green-700 hover:to-green-800 transition-all"
                  >
                    Go to Login
                  </button>
                </>
              )}

              {verificationState === 'error' && (
                <>
                  <div className="w-20 h-20 bg-red-500 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <XCircle className="h-10 w-10 text-red-500" />
                  </div>
                  <h1 className="text-3xl font-bold text-red-500 mb-4">Verification Failed</h1>
                  <p className="text-gray-300 mb-6">
                    {errorMessage || 'We could not verify your email address. The link may be expired or invalid.'}
                  </p>
                  
                  <div className="space-y-3">
                    <button
                      onClick={handleGoToRegister}
                      className="w-full py-3 px-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-md font-bold shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all"
                    >
                      Try Registration Again
                    </button>
                    
                    <button
                      onClick={handleGoToLogin}
                      className="w-full py-3 px-4 bg-transparent border border-white/20 text-white rounded-md font-bold hover:bg-white/10 transition-all"
                    >
                      Back to Login
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmailToken;
