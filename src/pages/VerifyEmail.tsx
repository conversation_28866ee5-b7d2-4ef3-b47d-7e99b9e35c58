
import React, { useState, useEffect } from 'react';
import { Mail } from 'lucide-react';
import Header from '../components/Header';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { useLocation } from 'react-router-dom';
import { customAuthService } from '@/services/customAuthService';

const VerifyEmail: React.FC = () => {
  const isMobile = useIsMobile();
  const [isResending, setIsResending] = useState(false);
  const [userEmail, setUserEmail] = useState<string>('');
  const location = useLocation();
  
  useEffect(() => {
    // Try to get email from multiple sources
    const getEmailAddress = async () => {
      // First, try from location state (if passed from registration)
      const emailFromState = location.state?.email;
      if (emailFromState) {
        setUserEmail(emailFromState);
        return;
      }
      
      // Then try from current user session
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.email) {
          setUserEmail(user.email);
          return;
        }
      } catch (error) {
        console.log('No user session found');
      }
      
      // Finally, try from localStorage as fallback
      const storedEmail = localStorage.getItem('pendingVerificationEmail');
      if (storedEmail) {
        setUserEmail(storedEmail);
      }
    };
    
    getEmailAddress();
  }, [location.state]);
  
  const handleResendVerification = async () => {
    setIsResending(true);
    
    try {
      let emailToUse = userEmail;
      
      // If we don't have email stored, try to get from current session one more time
      if (!emailToUse) {
        const { data: { user } } = await supabase.auth.getUser();
        emailToUse = user?.email || '';
      }
      
      if (!emailToUse) {
        toast({
          title: "Email not found",
          description: "Please go back to the registration page and sign up again.",
          variant: "destructive",
        });
        return;
      }

      // Try custom auth service first (for pending users)
      const { error: customError } = await customAuthService.resendVerificationEmail(emailToUse);

      if (!customError) {
        toast({
          title: "Email sent!",
          description: "We've sent a new verification link to your email address.",
        });
        return;
      }

      // Fallback to Supabase resend for existing users
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: emailToUse
      });

      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Email sent!",
          description: "We've sent a new verification link to your email address.",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to resend verification email. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsResending(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-navy-dark to-indigo/30">
      <Header />
      
      <div className="pt-32 pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-lg mx-auto backdrop-blur-sm bg-white/10 rounded-xl shadow-xl overflow-hidden border border-white/20">
            <div className="p-8 text-center">
              <div className="w-20 h-20 bg-indigo-light bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Mail className="h-10 w-10 text-indigo-light" />
              </div>
              
              <h1 className="text-3xl font-bold text-white mb-4">Check Your Email</h1>
              
              <p className="text-gray-300 mb-6">
                We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.
              </p>
              
              {userEmail && (
                <p className="text-indigo-light mb-4 text-sm">
                  Verification email sent to: {userEmail}
                </p>
              )}
              
              <div className="bg-navy-light/50 backdrop-blur-sm p-4 rounded-md mb-6 border border-indigo/20">
                <p className="text-sm text-gray-300">
                  If you don't see the email, check your spam folder or click the button below to request a new verification link.
                </p>
              </div>
              
              <button
                onClick={handleResendVerification}
                disabled={isResending}
                className="mb-4 w-full py-3 px-4 bg-gradient-to-r from-indigo to-indigo-dark text-white rounded-md hover:from-indigo-dark hover:to-indigo transition-all font-semibold transform hover:scale-[1.02] shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isResending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Sending...
                  </>
                ) : (
                  'Request New Link'
                )}
              </button>
              
              <p className="text-gray-300">
                <a href="/login" className="text-indigo-light hover:text-white hover:underline transition-colors">
                  Return to Login
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <footer className="bg-navy-dark/50 backdrop-blur-sm py-6">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-400">&copy; 2025 Grid2Play. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default VerifyEmail;
