
import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Clock, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const CancellationRefunds: React.FC = () => {
  return (
    <div className="min-h-screen bg-navy-dark text-white">
      <Header />
      
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Link 
            to="/" 
            className="inline-flex items-center text-indigo-light hover:text-indigo-dark transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Cancellation & Refunds</h1>
            <p className="text-gray-300 text-lg">
              Understanding our cancellation policy and refund process
            </p>
          </div>

          {/* Quick Info Cards */}
          <div className="grid md:grid-cols-2 gap-6 mb-12">
            <div className="bg-navy-light rounded-lg p-6 border border-indigo/20">
              <div className="flex items-center mb-4">
                <Clock className="w-6 h-6 text-indigo-light mr-3" />
                <h3 className="text-xl font-semibold">Cancellation Period</h3>
              </div>
              <p className="text-gray-300 text-lg font-medium">7-15 days</p>
              <p className="text-sm text-gray-400 mt-1">From booking date</p>
            </div>

            <div className="bg-navy-light rounded-lg p-6 border border-indigo/20">
              <div className="flex items-center mb-4">
                <CreditCard className="w-6 h-6 text-green-400 mr-3" />
                <h3 className="text-xl font-semibold">Refund Settlement</h3>
              </div>
              <p className="text-gray-300 text-lg font-medium">7-15 days</p>
              <p className="text-sm text-gray-400 mt-1">After cancellation approval</p>
            </div>
          </div>

          {/* Policy Details */}
          <div className="bg-navy-light rounded-lg p-8 mb-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <AlertCircle className="w-6 h-6 text-yellow-400 mr-3" />
              Important Policy Guidelines
            </h2>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <div className="bg-indigo/20 rounded-full p-2 mr-4 mt-1">
                  <span className="text-indigo-light font-bold">1</span>
                </div>
                <div>
                  <p className="text-gray-300">
                    Cancellation requests must be made at least <strong className="text-white">8 hours prior</strong> to the scheduled game start time to be eligible for a refund.
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-indigo/20 rounded-full p-2 mr-4 mt-1">
                  <span className="text-indigo-light font-bold">2</span>
                </div>
                <div>
                  <p className="text-gray-300">
                    We believe in keeping the game spirit alive. No-shows and last-minute cancellations affect everyone's experience, so we appreciate your commitment to confirmed bookings.
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-indigo/20 rounded-full p-2 mr-4 mt-1">
                  <span className="text-indigo-light font-bold">3</span>
                </div>
                <div>
                  <p className="text-gray-300">
                    All payments must be completed through our platform. <strong className="text-white">No cash payments</strong> will be accepted at the venue. Every booking is confirmed digitally with payment confirmation.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Refund Process */}
          <div className="bg-gradient-to-r from-navy to-navy-light rounded-lg p-8 mb-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <CheckCircle className="w-6 h-6 text-green-400 mr-3" />
              Refund Process
            </h2>
            
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-green-500/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-green-400 font-bold text-lg">1</span>
                </div>
                <h3 className="font-semibold mb-2">Submit Request</h3>
                <p className="text-sm text-gray-300">Cancel your booking through your profile or contact support</p>
              </div>

              <div className="text-center">
                <div className="bg-yellow-500/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-yellow-400 font-bold text-lg">2</span>
                </div>
                <h3 className="font-semibold mb-2">Review Process</h3>
                <p className="text-sm text-gray-300">We review your request within 24-48 hours</p>
              </div>

              <div className="text-center">
                <div className="bg-indigo-500/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-indigo-400 font-bold text-lg">3</span>
                </div>
                <h3 className="font-semibold mb-2">Refund Processed</h3>
                <p className="text-sm text-gray-300">Refund credited to your account within 7-15 days</p>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-indigo/10 rounded-lg p-6 text-center">
            <h3 className="text-xl font-semibold mb-3">Need Help with Cancellations?</h3>
            <p className="text-gray-300 mb-4">
              Our support team is here to assist you with any cancellation or refund queries.
            </p>
            <Link 
              to="/contact" 
              className="inline-flex items-center px-6 py-3 bg-indigo text-white rounded-lg hover:bg-indigo-dark transition-colors"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default CancellationRefunds;
