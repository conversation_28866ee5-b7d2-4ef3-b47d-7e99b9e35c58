
import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Eye, EyeOff, Lock, CheckCircle, AlertCircle } from 'lucide-react';
import Header from '../components/Header';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useIsMobile } from '@/hooks/use-mobile';
import { validatePassword, sanitizeInput, handleSecureError } from '@/utils/security';

const ResetPassword: React.FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(true);
  const [isValidSession, setIsValidSession] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const isMobile = useIsMobile();

  useEffect(() => {
    const validateResetSession = async () => {
      setIsValidating(true);
      setErrorMessage('');

      try {
        // Check if we have the required URL parameters from Supabase
        const token = searchParams.get('token');
        const type = searchParams.get('type');

        console.log('Reset URL params:', { token: !!token, type });

        if (type !== 'recovery') {
          setErrorMessage('Invalid reset link type. Please request a new password reset.');
          setIsValidating(false);
          return;
        }

        if (!token) {
          setErrorMessage('Invalid reset link. Please request a new password reset.');
          setIsValidating(false);
          return;
        }

        // First, ensure we don't have any existing session that might interfere
        await supabase.auth.signOut();

        // Verify the token with Supabase - this should establish a temporary session
        const { data, error } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: 'recovery'
        });

        if (error) {
          console.error('Token verification error:', error);
          setErrorMessage('Invalid or expired reset link. Please request a new password reset.');
          setIsValidating(false);
          return;
        }

        if (data.session && data.user) {
          console.log('Valid reset session established for user:', data.user.email);
          setIsValidSession(true);
        } else {
          setErrorMessage('Unable to validate reset session. Please try again.');
        }
      } catch (error: any) {
        console.error('Reset validation error:', error);
        setErrorMessage('An error occurred while validating your reset link. Please try again.');
      } finally {
        setIsValidating(false);
      }
    };

    validateResetSession();
  }, [searchParams]);

  const validateForm = () => {
    const passwordValidation = validatePassword(password);
    setPasswordErrors(passwordValidation.errors);

    if (!passwordValidation.isValid) {
      return false;
    }

    if (password !== confirmPassword) {
      setErrorMessage('Passwords do not match.');
      return false;
    }

    setErrorMessage('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage('');

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Update the user's password
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        console.error('Password update error:', error);
        setErrorMessage('Failed to update password. Please try again.');
        return;
      }

      console.log('Password updated successfully');
      setIsSuccess(true);
      
      toast({
        title: "Password updated successfully",
        description: "Your password has been reset. You can now sign in with your new password.",
      });
      
      // Sign out the user so they can sign in with new password
      await supabase.auth.signOut();
      
      // Redirect to login after a delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);

    } catch (error: any) {
      console.error('Unexpected error:', error);
      setErrorMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state while validating
  if (isValidating) {
    return (
      <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden flex items-center justify-center">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2E7D32] mx-auto mb-4"></div>
          <p>Validating reset link...</p>
        </div>
      </div>
    );
  }

  // Error state for invalid session
  if (!isValidSession) {
    return (
      <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden">
        <Header />
        <div className="pt-8 pb-4 sm:pt-24 sm:pb-16 relative z-10 w-full flex-1 flex flex-col justify-center items-center sm:block" style={{ minHeight: 'calc(100vh - 64px)' }}>
          <div className="container mx-auto px-2 sm:px-4">
            <div className="w-full mx-0 sm:max-w-md sm:mx-auto bg-black/80 border-2 border-red-500/60 shadow-xl sm:shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden animate-fade-in backdrop-blur-lg">
              <div className="p-4 sm:p-8 text-center">
                <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertCircle className="h-8 w-8 text-red-500" />
                </div>
                <h1 className="text-xl sm:text-2xl font-bold text-red-500 mb-4">Invalid Reset Link</h1>
                <p className="text-gray-300 mb-6">{errorMessage}</p>
                <button
                  onClick={() => navigate('/login')}
                  className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-[#1E3B2C] via-[#2E7D32] to-[#1E3B2C] text-white rounded-md font-bold shadow-md sm:shadow-lg hover:from-[#2E7D32] hover:to-[#1E3B2C] transition-all"
                >
                  Back to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Success state
  if (isSuccess) {
    return (
      <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden">
        <Header />
        <div className="pt-8 pb-4 sm:pt-24 sm:pb-16 relative z-10 w-full flex-1 flex flex-col justify-center items-center sm:block" style={{ minHeight: 'calc(100vh - 64px)' }}>
          <div className="container mx-auto px-2 sm:px-4">
            <div className="w-full mx-0 sm:max-w-md sm:mx-auto bg-black/80 border-2 border-[#1E3B2C]/60 shadow-xl sm:shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden animate-fade-in backdrop-blur-lg">
              <div className="p-4 sm:p-8 text-center">
                <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
                <h1 className="text-xl sm:text-2xl font-bold text-[#2E7D32] mb-4">Password Reset Successful!</h1>
                <p className="text-gray-300 mb-6">Your password has been updated successfully. You will be redirected to the login page shortly.</p>
                <button
                  onClick={() => navigate('/login')}
                  className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-[#1E3B2C] via-[#2E7D32] to-[#1E3B2C] text-white rounded-md font-bold shadow-md sm:shadow-lg hover:from-[#2E7D32] hover:to-[#1E3B2C] transition-all"
                >
                  Go to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main reset password form
  return (
    <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden">
      {/* Floating dark green SVG accent, hidden on mobile */}
      <div className="hidden sm:block absolute -top-32 -left-32 w-[400px] h-[400px] pointer-events-none opacity-20 animate-float z-0">
        <svg viewBox="0 0 400 400" fill="none">
          <ellipse cx="200" cy="200" rx="200" ry="200" fill="#1E3B2C" />
        </svg>
      </div>
      <Header />

      <div className="pt-8 pb-4 sm:pt-24 sm:pb-16 relative z-10 w-full flex-1 flex flex-col justify-center items-center sm:block" style={{ minHeight: 'calc(100vh - 64px)' }}>
        <div className="container mx-auto px-2 sm:px-4">
          <div className="w-full mx-0 sm:max-w-md sm:mx-auto bg-black/80 border-2 border-[#1E3B2C]/60 shadow-xl sm:shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden animate-fade-in backdrop-blur-lg">
            <div className="p-4 sm:p-8">
              <div className="text-center mb-6 sm:mb-8">
                <div className="w-12 h-12 sm:w-20 sm:h-20 bg-[#1E3B2C]/30 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                  <Lock className="h-6 w-6 sm:h-10 sm:w-10 text-[#2E7D32]" />
                </div>
                <h1 className="text-xl sm:text-3xl font-extrabold text-[#2E7D32] drop-shadow">Reset Your Password</h1>
                <p className="text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">Enter your new password below</p>
              </div>

              {errorMessage && (
                <div className="mb-4 p-3 bg-red-500/20 border border-red-500/60 rounded-md">
                  <p className="text-red-400 text-sm">{errorMessage}</p>
                </div>
              )}

              {passwordErrors.length > 0 && (
                <div className="mb-4 p-3 bg-red-500/20 border border-red-500/60 rounded-md">
                  <p className="text-red-400 text-sm font-medium mb-2">Password requirements:</p>
                  <ul className="text-red-400 text-xs space-y-1">
                    {passwordErrors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                <div>
                  <label htmlFor="password" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                    New Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                    </div>
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value);
                        setPasswordErrors([]);
                      }}
                      className="pl-10 w-full p-2 sm:p-3 border border-[#1E3B2C]/60 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base"
                      placeholder="Enter new password"
                      required
                      minLength={8}
                      maxLength={128}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-[#2E7D32] hover:text-white focus:outline-none"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" /> : <Eye className="h-4 w-4 sm:h-5 sm:w-5" />}
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <label htmlFor="confirm-password" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                    Confirm New Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                    </div>
                    <input
                      id="confirm-password"
                      type={showConfirmPassword ? "text" : "password"}
                      value={confirmPassword}
                      onChange={(e) => {
                        setConfirmPassword(e.target.value);
                        setErrorMessage('');
                      }}
                      className="pl-10 w-full p-2 sm:p-3 border border-[#1E3B2C]/60 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base"
                      placeholder="Confirm new password"
                      required
                      maxLength={128}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="text-[#2E7D32] hover:text-white focus:outline-none"
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" /> : <Eye className="h-4 w-4 sm:h-5 sm:w-5" />}
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-[#1E3B2C] via-[#2E7D32] to-[#1E3B2C] text-white rounded-md font-bold shadow-md sm:shadow-lg hover:from-[#2E7D32] hover:to-[#1E3B2C] hover:shadow-[#2E7D32]/40 transition-all flex justify-center items-center transform hover:scale-[1.03] focus:outline-none focus:ring-2 focus:ring-[#2E7D32] text-sm sm:text-lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Updating Password...
                      </span>
                    ) : (
                      'Update Password'
                    )}
                  </button>
                </div>
              </form>

              <div className="mt-6 text-center">
                <button
                  onClick={() => navigate('/login')}
                  className="text-[#2E7D32] hover:text-white font-medium transition-colors text-sm"
                >
                  Back to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
