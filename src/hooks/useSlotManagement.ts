import { useState, useCallback } from 'react';
import SlotManagementService, { 
  BlockedSlot, 
  SlotManagementStats, 
  BulkBlockRequest, 
  VenueBlockRequest,
  FilterOptions 
} from '@/services/slotManagementService';
import { toast } from '@/components/ui/use-toast';

export interface UseSlotManagementReturn {
  // State
  blockedSlots: BlockedSlot[];
  stats: SlotManagementStats | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchBlockedSlots: (
    venue_id?: string,
    court_id?: string,
    start_date?: string,
    end_date?: string,
    created_by?: string
  ) => Promise<void>;
  
  fetchFilteredBlockedSlots: (filters: FilterOptions) => Promise<void>;
  fetchStats: (venue_id?: string, start_date?: string, end_date?: string) => Promise<void>;
  bulkBlockSlots: (request: BulkBlockRequest) => Promise<boolean>;
  blockVenueSlots: (request: VenueBlockRequest) => Promise<boolean>;
  unblockSlots: (slot_ids: string[]) => Promise<boolean>;
  isSlotBlocked: (court_id: string, date: string, start_time: string, end_time: string) => Promise<boolean>;
  
  // Utilities
  clearError: () => void;
  refreshData: () => Promise<void>;
}

export const useSlotManagement = (): UseSlotManagementReturn => {
  const [blockedSlots, setBlockedSlots] = useState<BlockedSlot[]>([]);
  const [stats, setStats] = useState<SlotManagementStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Store last fetch parameters for refresh functionality
  const [lastFetchParams, setLastFetchParams] = useState<{
    type: 'basic' | 'filtered' | 'stats';
    params: any;
  } | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const fetchBlockedSlots = useCallback(async (
    venue_id?: string,
    court_id?: string,
    start_date?: string,
    end_date?: string,
    created_by?: string
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await SlotManagementService.getBlockedSlotsWithDetails(
        venue_id,
        court_id,
        start_date,
        end_date,
        created_by
      );

      if (error) {
        throw new Error(error.message || 'Failed to fetch blocked slots');
      }

      setBlockedSlots(data || []);
      setLastFetchParams({
        type: 'basic',
        params: { venue_id, court_id, start_date, end_date, created_by }
      });
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch blocked slots';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchFilteredBlockedSlots = useCallback(async (filters: FilterOptions) => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await SlotManagementService.getFilteredBlockedSlots(filters);

      if (error) {
        throw new Error(error.message || 'Failed to fetch filtered blocked slots');
      }

      setBlockedSlots(data || []);
      setLastFetchParams({
        type: 'filtered',
        params: filters
      });
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch filtered blocked slots';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchStats = useCallback(async (
    venue_id?: string,
    start_date?: string,
    end_date?: string
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await SlotManagementService.getSlotManagementStats(
        venue_id,
        start_date,
        end_date
      );

      if (error) {
        throw new Error(error.message || 'Failed to fetch stats');
      }

      setStats(data);
      setLastFetchParams({
        type: 'stats',
        params: { venue_id, start_date, end_date }
      });
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch stats';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkBlockSlots = useCallback(async (request: BulkBlockRequest): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const result = await SlotManagementService.bulkBlockSlots(request, user?.id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to block slots');
      }

      toast({
        title: 'Success',
        description: result.message || `Successfully blocked ${result.blocked_count || 0} slots`,
        variant: 'default'
      });

      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to block slots';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  const blockVenueSlots = useCallback(async (request: VenueBlockRequest): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await SlotManagementService.blockVenueSlots(request);

      if (!result.success) {
        throw new Error(result.error || 'Failed to block venue slots');
      }

      toast({
        title: 'Success',
        description: result.message || `Successfully blocked ${result.blocked_count || 0} venue slots`,
        variant: 'default'
      });

      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to block venue slots';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const unblockSlots = useCallback(async (slot_ids: string[]): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const result = await SlotManagementService.unblockSlots(slot_ids, user?.id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to unblock slots');
      }

      toast({
        title: 'Success',
        description: result.message || `Successfully unblocked ${result.unblocked_count || 0} slots`,
        variant: 'default'
      });

      // Remove unblocked slots from current state
      setBlockedSlots(prev => prev.filter(slot => !slot_ids.includes(slot.id)));

      return true;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to unblock slots';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  const isSlotBlocked = useCallback(async (
    court_id: string,
    date: string,
    start_time: string,
    end_time: string
  ): Promise<boolean> => {
    try {
      const { blocked, error } = await SlotManagementService.isSlotBlocked(
        court_id,
        date,
        start_time,
        end_time
      );

      if (error) {
        console.error('Error checking if slot is blocked:', error);
        return false;
      }

      return blocked;
    } catch (err) {
      console.error('Error checking if slot is blocked:', err);
      return false;
    }
  }, []);

  const refreshData = useCallback(async () => {
    if (!lastFetchParams) return;

    switch (lastFetchParams.type) {
      case 'basic':
        await fetchBlockedSlots(
          lastFetchParams.params.venue_id,
          lastFetchParams.params.court_id,
          lastFetchParams.params.start_date,
          lastFetchParams.params.end_date,
          lastFetchParams.params.created_by
        );
        break;
      case 'filtered':
        await fetchFilteredBlockedSlots(lastFetchParams.params);
        break;
      case 'stats':
        await fetchStats(
          lastFetchParams.params.venue_id,
          lastFetchParams.params.start_date,
          lastFetchParams.params.end_date
        );
        break;
    }
  }, [lastFetchParams, fetchBlockedSlots, fetchFilteredBlockedSlots, fetchStats]);

  return {
    // State
    blockedSlots,
    stats,
    loading,
    error,
    
    // Actions
    fetchBlockedSlots,
    fetchFilteredBlockedSlots,
    fetchStats,
    bulkBlockSlots,
    blockVenueSlots,
    unblockSlots,
    isSlotBlocked,
    
    // Utilities
    clearError,
    refreshData
  };
};

export default useSlotManagement;
