import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import SlotManagementPermissions, { 
  UserPermissions, 
  PermissionCheckResult 
} from '@/utils/slotManagementPermissions';

export interface UseSlotManagementPermissionsReturn {
  permissions: UserPermissions | null;
  loading: boolean;
  error: string | null;
  
  // Permission checks
  checkPermission: (action: keyof UserPermissions, resourceId?: string) => Promise<PermissionCheckResult>;
  canAccessSlotManagement: () => Promise<boolean>;
  canPerformBulkOperations: () => Promise<boolean>;
  canAccessDashboard: () => Promise<boolean>;
  canBlockSlotsForVenue: (venueId: string) => Promise<boolean>;
  canUnblockSlots: (slotIds: string[]) => Promise<boolean>;
  
  // Utilities
  getAccessibleVenues: () => Promise<string[]>;
  refreshPermissions: () => Promise<void>;
  logAction: (action: string, details: Record<string, any>) => Promise<void>;
}

export const useSlotManagementPermissions = (): UseSlotManagementPermissionsReturn => {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPermissions = useCallback(async () => {
    if (!user?.id) {
      setPermissions(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const userPermissions = await SlotManagementPermissions.getUserPermissions(user.id);
      setPermissions(userPermissions);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch permissions');
      console.error('Error fetching slot management permissions:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  const checkPermission = useCallback(async (
    action: keyof UserPermissions,
    resourceId?: string
  ): Promise<PermissionCheckResult> => {
    if (!user?.id) {
      return {
        allowed: false,
        reason: 'User not authenticated'
      };
    }

    return SlotManagementPermissions.checkPermission(user.id, action, resourceId);
  }, [user?.id]);

  const canAccessSlotManagement = useCallback(async (): Promise<boolean> => {
    if (!user?.id) return false;
    
    const result = await SlotManagementPermissions.canAccessSlotManagement(user.id);
    return result.allowed;
  }, [user?.id]);

  const canPerformBulkOperations = useCallback(async (): Promise<boolean> => {
    if (!user?.id) return false;
    
    const result = await SlotManagementPermissions.canPerformBulkOperations(user.id);
    return result.allowed;
  }, [user?.id]);

  const canAccessDashboard = useCallback(async (): Promise<boolean> => {
    if (!user?.id) return false;
    
    const result = await SlotManagementPermissions.canAccessDashboard(user.id);
    return result.allowed;
  }, [user?.id]);

  const canBlockSlotsForVenue = useCallback(async (venueId: string): Promise<boolean> => {
    if (!user?.id) return false;
    
    const result = await SlotManagementPermissions.canBlockSlotsForVenue(user.id, venueId);
    return result.allowed;
  }, [user?.id]);

  const canUnblockSlots = useCallback(async (slotIds: string[]): Promise<boolean> => {
    if (!user?.id) return false;
    
    const result = await SlotManagementPermissions.canUnblockSlots(user.id, slotIds);
    return result.allowed;
  }, [user?.id]);

  const getAccessibleVenues = useCallback(async (): Promise<string[]> => {
    if (!user?.id) return [];
    
    return SlotManagementPermissions.getAccessibleVenues(user.id);
  }, [user?.id]);

  const refreshPermissions = useCallback(async (): Promise<void> => {
    await fetchPermissions();
  }, [fetchPermissions]);

  const logAction = useCallback(async (
    action: string,
    details: Record<string, any>
  ): Promise<void> => {
    if (!user?.id) return;
    
    await SlotManagementPermissions.logSlotManagementAction(user.id, action, details);
  }, [user?.id]);

  return {
    permissions,
    loading,
    error,
    
    // Permission checks
    checkPermission,
    canAccessSlotManagement,
    canPerformBulkOperations,
    canAccessDashboard,
    canBlockSlotsForVenue,
    canUnblockSlots,
    
    // Utilities
    getAccessibleVenues,
    refreshPermissions,
    logAction
  };
};

export default useSlotManagementPermissions;
