
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { validateEmail, validatePassword, authRateLimiter, handleSecureError } from '@/utils/security';
import { toast } from '@/components/ui/use-toast';

export const useSecureAuth = () => {
  const [isLoading, setIsLoading] = useState(false);

  const secureSignIn = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    
    try {
      // Input validation
      if (!validateEmail(email)) {
        throw new Error('Please enter a valid email address');
      }

      if (!password || password.length < 6) {
        throw new Error('Please enter a valid password');
      }

      // Rate limiting check
      const clientId = email.toLowerCase();
      if (authRateLimiter.isBlocked(clientId)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(clientId) / 1000 / 60);
        throw new Error(`Too many failed attempts. Please try again in ${remainingTime} minutes.`);
      }

      const { error } = await supabase.auth.signInWithPassword({ 
        email: email.toLowerCase().trim(), 
        password 
      });

      if (error) {
        authRateLimiter.recordAttempt(clientId, false);
        
        // Generic error message to prevent email enumeration
        if (error.message.includes('Invalid login credentials')) {
          throw new Error('Invalid email or password. Please check your credentials and try again.');
        }
        
        throw new Error(error.message);
      }

      // Success
      authRateLimiter.recordAttempt(clientId, true);
      return { error: null };

    } catch (error: any) {
      handleSecureError(error, error.message);
      return { error };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const secureSignUp = useCallback(async (email: string, password: string, userData: any) => {
    setIsLoading(true);
    
    try {
      // Input validation
      if (!validateEmail(email)) {
        throw new Error('Please enter a valid email address');
      }

      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        throw new Error(passwordValidation.errors.join('. '));
      }

      // Sanitize user data
      const sanitizedUserData = {
        name: userData.name?.trim().slice(0, 100) || '',
        phone: userData.phone?.replace(/[^\d+\-\s()]/g, '').slice(0, 20) || ''
      };

      const { error } = await supabase.auth.signUp({ 
        email: email.toLowerCase().trim(), 
        password,
        options: {
          data: sanitizedUserData
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      return { error: null };

    } catch (error: any) {
      handleSecureError(error, error.message);
      return { error };
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    secureSignIn,
    secureSignUp,
    isLoading
  };
};
