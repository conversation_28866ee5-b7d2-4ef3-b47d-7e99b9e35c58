import { useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';
import { toast } from '@/components/ui/use-toast';

export interface SlotUpdateEvent {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: 'blocked_slots' | 'bookings';
  record: any;
  old_record?: any;
}

export interface UseRealTimeSlotUpdatesOptions {
  onSlotBlocked?: (slot: any) => void;
  onSlotUnblocked?: (slotId: string) => void;
  onSlotUpdated?: (slot: any, oldSlot?: any) => void;
  onBookingCreated?: (booking: any) => void;
  onBookingCancelled?: (booking: any) => void;
  venueIds?: string[];
  showNotifications?: boolean;
}

export const useRealTimeSlotUpdates = (options: UseRealTimeSlotUpdatesOptions = {}) => {
  const {
    onSlotBlocked,
    onSlotUnblocked,
    onSlotUpdated,
    onBookingCreated,
    onBookingCancelled,
    venueIds,
    showNotifications = true
  } = options;

  const channelRef = useRef<RealtimeChannel | null>(null);
  const isSubscribedRef = useRef(false);

  const handleBlockedSlotsChange = useCallback((payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    // Filter by venue if specified
    if (venueIds && venueIds.length > 0) {
      // We'll need to check if the slot belongs to one of the specified venues
      // This would require additional data fetching or including venue info in the payload
    }

    switch (eventType) {
      case 'INSERT':
        if (onSlotBlocked) {
          onSlotBlocked(newRecord);
        }
        if (showNotifications) {
          toast({
            title: 'Slot Blocked',
            description: `A slot has been blocked`,
            variant: 'default'
          });
        }
        break;

      case 'DELETE':
        if (onSlotUnblocked) {
          onSlotUnblocked(oldRecord.id);
        }
        if (showNotifications) {
          toast({
            title: 'Slot Unblocked',
            description: `A slot has been unblocked`,
            variant: 'default'
          });
        }
        break;

      case 'UPDATE':
        if (onSlotUpdated) {
          onSlotUpdated(newRecord, oldRecord);
        }
        if (showNotifications) {
          toast({
            title: 'Slot Updated',
            description: `A blocked slot has been updated`,
            variant: 'default'
          });
        }
        break;
    }
  }, [onSlotBlocked, onSlotUnblocked, onSlotUpdated, venueIds, showNotifications]);

  const handleBookingsChange = useCallback((payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    switch (eventType) {
      case 'INSERT':
        if (onBookingCreated) {
          onBookingCreated(newRecord);
        }
        if (showNotifications) {
          toast({
            title: 'New Booking',
            description: `A new booking has been created`,
            variant: 'default'
          });
        }
        break;

      case 'UPDATE':
        // Check if booking was cancelled
        if (oldRecord.status !== 'cancelled' && newRecord.status === 'cancelled') {
          if (onBookingCancelled) {
            onBookingCancelled(newRecord);
          }
          if (showNotifications) {
            toast({
              title: 'Booking Cancelled',
              description: `A booking has been cancelled`,
              variant: 'default'
            });
          }
        }
        break;
    }
  }, [onBookingCreated, onBookingCancelled, showNotifications]);

  const subscribe = useCallback(() => {
    if (isSubscribedRef.current || channelRef.current) {
      return;
    }

    try {
      const channel = supabase
        .channel('slot-management-updates')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'blocked_slots'
          },
          handleBlockedSlotsChange
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'bookings'
          },
          handleBookingsChange
        );

      channel.subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('✅ Real-time slot updates subscribed');
          isSubscribedRef.current = true;
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Real-time slot updates subscription error');
          isSubscribedRef.current = false;
        } else if (status === 'TIMED_OUT') {
          console.warn('⏰ Real-time slot updates subscription timed out');
          isSubscribedRef.current = false;
        }
      });

      channelRef.current = channel;
    } catch (error) {
      console.error('Error setting up real-time slot updates:', error);
    }
  }, [handleBlockedSlotsChange, handleBookingsChange]);

  const unsubscribe = useCallback(() => {
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
      channelRef.current = null;
      isSubscribedRef.current = false;
      console.log('🔌 Real-time slot updates unsubscribed');
    }
  }, []);

  const reconnect = useCallback(() => {
    unsubscribe();
    setTimeout(() => {
      subscribe();
    }, 1000);
  }, [unsubscribe, subscribe]);

  useEffect(() => {
    subscribe();

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, [subscribe, unsubscribe]);

  // Handle connection state changes
  useEffect(() => {
    const handleOnline = () => {
      console.log('🌐 Connection restored, reconnecting real-time updates...');
      reconnect();
    };

    const handleOffline = () => {
      console.log('📴 Connection lost, real-time updates paused');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [reconnect]);

  return {
    subscribe,
    unsubscribe,
    reconnect,
    isSubscribed: isSubscribedRef.current
  };
};

export default useRealTimeSlotUpdates;
