
import { useState, useEffect, useCallback } from 'react';
import { LocationState, LocationData, Coordinates, IPLocationResponse } from '@/types/location';
import { locationStorage } from '@/utils/locationStorage';
import { geocoding } from '@/utils/geocoding';
import { toast } from '@/hooks/use-toast';

export function useEnhancedLocation() {
  const [state, setState] = useState<LocationState>({
    data: null,
    isLoading: true,
    error: null,
    hasPermission: null,
    isRefreshing: false
  });

  // Check permission status
  const checkPermissionStatus = useCallback(async () => {
    if (!navigator.permissions) {
      return 'prompt';
    }

    try {
      const result = await navigator.permissions.query({ name: 'geolocation' });
      return result.state;
    } catch {
      return 'prompt';
    }
  }, []);

  // Get location via IP geolocation
  const getIPLocation = useCallback(async (): Promise<LocationData | null> => {
    try {
      const response = await fetch('https://ipapi.co/json/');
      if (!response.ok) throw new Error('IP location failed');
      
      const ipData: IPLocationResponse = await response.json();
      
      const coordinates: Coordinates = {
        latitude: ipData.latitude,
        longitude: ipData.longitude,
        accuracy: 10000 // IP location is less accurate
      };

      const address = await geocoding.reverseGeocode(coordinates);

      return {
        coordinates,
        address,
        source: 'ip',
        timestamp: Date.now(),
        accuracy: 10000
      };
    } catch (error) {
      console.error('IP location failed:', error);
      return null;
    }
  }, []);

  // Get high-accuracy GPS location
  const getGPSLocation = useCallback(async (): Promise<LocationData | null> => {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve(null);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const coordinates: Coordinates = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy
            };

            const address = await geocoding.reverseGeocode(coordinates);

            const locationData: LocationData = {
              coordinates,
              address,
              source: 'gps',
              timestamp: Date.now(),
              accuracy: position.coords.accuracy
            };

            resolve(locationData);
          } catch (error) {
            console.error('GPS location processing failed:', error);
            resolve(null);
          }
        },
        (error) => {
          console.error('GPS location failed:', error);
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  }, []);

  // Multi-layered location detection
  const detectLocation = useCallback(async (force = false) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Try cached location first (if not forcing refresh)
      if (!force) {
        const cached = locationStorage.load();
        if (cached) {
          setState(prev => ({
            ...prev,
            data: cached,
            isLoading: false,
            hasPermission: true
          }));
          return cached;
        }
      }

      // Check permission status
      const permissionStatus = await checkPermissionStatus();
      setState(prev => ({ ...prev, hasPermission: permissionStatus === 'granted' }));

      let locationData: LocationData | null = null;

      // Try GPS first
      if (permissionStatus === 'granted' || permissionStatus === 'prompt') {
        locationData = await getGPSLocation();
        
        if (locationData) {
          toast({
            title: "Location detected",
            description: `Using GPS location: ${locationData.address.area || locationData.address.city || 'Current location'}`,
          });
        }
      }

      // Fallback to IP location
      if (!locationData) {
        locationData = await getIPLocation();
        
        if (locationData) {
          toast({
            title: "Location detected",
            description: `Using approximate location: ${locationData.address.city || 'Current area'}`,
          });
        }
      }

      if (locationData) {
        locationStorage.save(locationData);
        setState(prev => ({
          ...prev,
          data: locationData,
          isLoading: false,
          error: null
        }));
        return locationData;
      } else {
        throw new Error('Unable to detect location');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Location detection failed';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));
      
      toast({
        title: "Location detection failed",
        description: "Please enable location access or enter your location manually",
        variant: "destructive"
      });
      
      return null;
    }
  }, [checkPermissionStatus, getGPSLocation, getIPLocation]);

  // Refresh location
  const refreshLocation = useCallback(async () => {
    setState(prev => ({ ...prev, isRefreshing: true }));
    await detectLocation(true);
    setState(prev => ({ ...prev, isRefreshing: false }));
  }, [detectLocation]);

  // Set manual location
  const setManualLocation = useCallback(async (address: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const coordinates = await geocoding.forwardGeocode(address);
      
      if (!coordinates) {
        throw new Error('Address not found');
      }

      const resolvedAddress = await geocoding.reverseGeocode(coordinates);
      
      const locationData: LocationData = {
        coordinates,
        address: resolvedAddress,
        source: 'manual',
        timestamp: Date.now()
      };

      locationStorage.save(locationData);
      setState(prev => ({
        ...prev,
        data: locationData,
        isLoading: false,
        error: null
      }));

      toast({
        title: "Location set",
        description: `Location set to: ${resolvedAddress.area || resolvedAddress.city || address}`,
      });

      return locationData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to set location';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));
      
      toast({
        title: "Failed to set location",
        description: errorMessage,
        variant: "destructive"
      });
      
      return null;
    }
  }, []);

  // Clear location
  const clearLocation = useCallback(() => {
    locationStorage.clear();
    setState({
      data: null,
      isLoading: false,
      error: null,
      hasPermission: null,
      isRefreshing: false
    });
  }, []);

  // Auto-detect on mount
  useEffect(() => {
    detectLocation();
  }, []);

  return {
    ...state,
    detectLocation,
    refreshLocation,
    setManualLocation,
    clearLocation
  };
}

// Export distance calculation function for compatibility
export function calculateDistance(
  lat1?: number | null,
  lon1?: number | null,
  lat2?: number | null,
  lon2?: number | null
): number | null {
  if (!lat1 || !lon1 || !lat2 || !lon2) return null;
  
  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2); 
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); 
  const distance = R * c; // Distance in km
  
  return distance;
}
