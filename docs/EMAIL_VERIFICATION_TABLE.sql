-- Create email verification tokens table for WhatsApp users
-- This table stores verification tokens for email verification after WhatsApp registration

CREATE TABLE IF NOT EXISTS email_verification_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  token TEXT NOT NULL UNIQUE,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token ON email_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);

-- Create RLS policies
ALTER TABLE email_verification_tokens ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own tokens (for debugging/admin purposes)
CREATE POLICY "Users can view own verification tokens" ON email_verification_tokens
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Service role can manage all tokens (for Edge Functions)
CREATE POLICY "Service role can manage verification tokens" ON email_verification_tokens
  FOR ALL USING (auth.role() = 'service_role');

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_email_verification_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_email_verification_tokens_updated_at
  BEFORE UPDATE ON email_verification_tokens
  FOR EACH ROW
  EXECUTE FUNCTION update_email_verification_tokens_updated_at();

-- Clean up expired tokens (optional - can be run periodically)
-- DELETE FROM email_verification_tokens WHERE expires_at < NOW() AND used = FALSE;

COMMENT ON TABLE email_verification_tokens IS 'Stores email verification tokens for WhatsApp users who need to verify their email addresses';
COMMENT ON COLUMN email_verification_tokens.user_id IS 'References the user who needs to verify their email';
COMMENT ON COLUMN email_verification_tokens.email IS 'The email address to be verified';
COMMENT ON COLUMN email_verification_tokens.token IS 'Unique verification token sent via email';
COMMENT ON COLUMN email_verification_tokens.used IS 'Whether the token has been used for verification';
COMMENT ON COLUMN email_verification_tokens.expires_at IS 'When the token expires (typically 24 hours)';
