# API Reference Documentation

This document provides comprehensive API documentation for Grid2play's backend services, including Supabase Edge Functions, database operations, and third-party integrations.

## Overview

Grid2play uses a combination of:
- **Supabase Client SDK** for direct database operations
- **Supabase Edge Functions** for serverless business logic
- **Third-party APIs** for payments, emails, and location services

## Authentication

All API calls require authentication via Supabase Auth. Include the JWT token in the Authorization header:

```javascript
const { data, error } = await supabase.auth.getSession();
const token = data.session?.access_token;

// Use token in API calls
headers: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}
```

## Core Database Operations

### Venue Management

#### Get All Venues
```javascript
const { data: venues, error } = await supabase
  .from('venues')
  .select(`
    id,
    name,
    location,
    description,
    rating,
    image_url,
    opening_hours,
    courts (
      id,
      name,
      hourly_rate,
      sports (name, id)
    )
  `)
  .eq('is_active', true)
  .order('name');
```

#### Get Venue Details
```javascript
const { data: venue, error } = await supabase
  .from('venues')
  .select(`
    *,
    courts (
      id,
      name,
      description,
      hourly_rate,
      sports (id, name, icon_url)
    )
  `)
  .eq('id', venueId)
  .single();
```

### Booking Operations

#### Create Booking
```javascript
const { data: booking, error } = await supabase
  .rpc('create_booking_with_lock', {
    p_court_id: courtId,
    p_user_id: userId,
    p_booking_date: bookingDate,
    p_start_time: startTime,
    p_end_time: endTime,
    p_total_price: totalPrice,
    p_guest_name: guestName,
    p_guest_phone: guestPhone
  });
```

#### Get Available Slots
```javascript
const { data: slots, error } = await supabase
  .rpc('get_available_slots', {
    court_id: courtId,
    booking_date: date
  });
```

#### Get User Bookings
```javascript
const { data: bookings, error } = await supabase
  .from('bookings')
  .select(`
    id,
    booking_date,
    start_time,
    end_time,
    total_price,
    status,
    booking_reference,
    court:courts (
      name,
      venue:venues (name, location),
      sport:sports (name)
    )
  `)
  .eq('user_id', userId)
  .gte('booking_date', today)
  .order('booking_date', { ascending: true });
```

#### Cancel Booking
```javascript
const { data, error } = await supabase
  .from('bookings')
  .update({
    status: 'cancelled',
    cancellation_reason: reason,
    updated_at: new Date().toISOString()
  })
  .eq('id', bookingId)
  .eq('user_id', userId);
```

### Tournament Management

#### Create Tournament
```javascript
const { data: tournament, error } = await supabase
  .from('tournaments')
  .insert({
    name,
    description,
    sport_id: sportId,
    venue_id: venueId,
    start_date: startDate,
    end_date: endDate,
    registration_deadline: registrationDeadline,
    max_participants: maxParticipants,
    entry_fee: entryFee,
    prize_pool: prizePool,
    tournament_type: tournamentType,
    rules,
    contact_info: contactInfo,
    created_by: userId
  })
  .select()
  .single();
```

#### Register for Tournament
```javascript
const { data: registration, error } = await supabase
  .from('tournament_registrations')
  .insert({
    tournament_id: tournamentId,
    user_id: userId,
    team_name: teamName,
    team_members: teamMembers
  })
  .select()
  .single();
```

#### Get Tournament Fixtures
```javascript
const { data: fixtures, error } = await supabase
  .from('tournament_fixtures')
  .select(`
    *,
    venue:venues(name),
    court:courts(name)
  `)
  .eq('tournament_id', tournamentId)
  .order('round')
  .order('match_number');
```

## Supabase Edge Functions

### Email Verification Function

**Endpoint**: `/functions/v1/send-verification-email`

```javascript
const { data, error } = await supabase.functions.invoke('send-verification-email', {
  body: {
    email: userEmail,
    name: userName,
    userId: userId
  }
});
```

**Response**:
```json
{
  "success": true,
  "message": "Verification email sent successfully",
  "tokenId": "uuid"
}
```

### Booking Confirmation Email

**Endpoint**: `/functions/v1/send-booking-confirmation`

```javascript
const { data, error } = await supabase.functions.invoke('send-booking-confirmation', {
  body: {
    bookingId: bookingId,
    userEmail: userEmail,
    bookingDetails: {
      bookingReference,
      courtName,
      venueName,
      bookingDate,
      startTime,
      endTime,
      venueAddress,
      venuePhone
    }
  }
});
```

### Chat Assistant Function

**Endpoint**: `/functions/v1/chat-assistant`

```javascript
const { data, error } = await supabase.functions.invoke('chat-assistant', {
  body: {
    message: userMessage,
    userId: userId,
    context: conversationContext
  }
});
```

**Response**:
```json
{
  "response": "AI assistant response",
  "context": "updated conversation context",
  "actions": ["suggested_actions"]
}
```

## Real-time Subscriptions

### Booking Updates
```javascript
const subscription = supabase
  .channel('booking-updates')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'bookings',
    filter: `user_id=eq.${userId}`
  }, (payload) => {
    console.log('Booking update:', payload);
    // Handle booking changes
  })
  .subscribe();
```

### Team Chat
```javascript
const chatSubscription = supabase
  .channel(`team-chat-${teamId}`)
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'team_chats',
    filter: `team_id=eq.${teamId}`
  }, (payload) => {
    console.log('New message:', payload.new);
    // Handle new chat message
  })
  .subscribe();
```

### Notification System
```javascript
const notificationSubscription = supabase
  .channel(`notifications-${userId}`)
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'notifications',
    filter: `user_id=eq.${userId}`
  }, (payload) => {
    console.log('New notification:', payload.new);
    // Show notification to user
  })
  .subscribe();
```

## Third-Party Integrations

### Razorpay Payment Integration

#### Create Payment Order
```javascript
const createPaymentOrder = async (amount, bookingId) => {
  const options = {
    amount: amount * 100, // Amount in paise
    currency: 'INR',
    receipt: `booking_${bookingId}`,
    notes: {
      booking_id: bookingId,
      user_id: userId
    }
  };

  const order = await razorpay.orders.create(options);
  return order;
};
```

#### Handle Payment Success
```javascript
const handlePaymentSuccess = async (paymentData) => {
  const { data, error } = await supabase
    .from('bookings')
    .update({
      payment_reference: paymentData.razorpay_payment_id,
      payment_status: 'completed',
      status: 'confirmed'
    })
    .eq('id', bookingId);
};
```

### MSG91 Email Service

#### Send Custom Email
```javascript
const sendCustomEmail = async (templateId, variables, recipients) => {
  const { data, error } = await supabase.functions.invoke('send-msg91-email', {
    body: {
      template_id: templateId,
      variables: variables,
      recipients: recipients
    }
  });
  
  return { data, error };
};
```

## Error Handling

### Standard Error Response Format
```json
{
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": "Additional error details"
  }
}
```

### Common Error Codes
- `BOOKING_CONFLICT`: Slot already booked
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `VENUE_INACTIVE`: Venue is not active
- `PAYMENT_FAILED`: Payment processing failed
- `TOURNAMENT_FULL`: Tournament registration full
- `INVALID_TIME_SLOT`: Invalid booking time

## Rate Limiting

API calls are rate-limited based on user role:
- **Regular Users**: 100 requests per minute
- **Admins**: 500 requests per minute
- **Super Admins**: 1000 requests per minute

## Webhooks

### Payment Webhook
**Endpoint**: `/functions/v1/payment-webhook`

Handles Razorpay payment notifications:
```json
{
  "event": "payment.captured",
  "payload": {
    "payment": {
      "entity": {
        "id": "pay_xxxxx",
        "amount": 50000,
        "status": "captured",
        "notes": {
          "booking_id": "uuid"
        }
      }
    }
  }
}
```

### Tournament Update Webhook
**Endpoint**: `/functions/v1/tournament-webhook`

Handles tournament status updates and notifications.

## Security Considerations

1. **Row Level Security**: All database operations respect RLS policies
2. **JWT Validation**: All API calls validate JWT tokens
3. **Input Sanitization**: All inputs are validated and sanitized
4. **Rate Limiting**: API calls are rate-limited by user role
5. **Audit Logging**: All critical operations are logged for audit trails

## Performance Optimization

1. **Database Indexes**: Optimized indexes for common queries
2. **Connection Pooling**: Efficient database connection management
3. **Caching**: Strategic caching of frequently accessed data
4. **Real-time Optimization**: Efficient real-time subscription management

For more detailed implementation examples, see the source code in the `/src` directory.
