-- Clean up duplicate profiles created by Whats<PERSON><PERSON> registration
-- This script will merge duplicate profiles into single profiles

-- STEP 1: Identify duplicate profiles for your test user
-- Primary profile: 3c4d5217-8c54-416e-9960-f00a7f4ceaf4 (has phone)
-- Secondary profile: 84a226dd-9bf7-4477-802d-1ca693657703 (has temp email)

-- STEP 2: Update the primary profile to include the temp email
UPDATE profiles 
SET 
  email = '<EMAIL>',
  updated_at = NOW()
WHERE id = '3c4d5217-8c54-416e-9960-f00a7f4ceaf4';

-- STEP 3: Delete the secondary profile (temp email only profile)
DELETE FROM profiles 
WHERE id = '84a226dd-9bf7-4477-802d-1ca693657703';

-- STEP 4: Update auth.users to use the temp email for the primary user
-- This ensures the user can authenticate with the temp email
-- Note: You may need to do this via Supabase Dashboard -> Authentication -> Users
-- Or use the admin API to update the user's email

-- STEP 5: Verify the cleanup
SELECT 
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified,
  created_at
FROM profiles 
WHERE phone = '+918448609110' OR email LIKE '%3c4d5217-8c54-416e-9960-f00a7f4ceaf4%';

-- Expected result: Only ONE profile with:
-- id: 3c4d5217-8c54-416e-9960-f00a7f4ceaf4
-- phone: +918448609110
-- email: <EMAIL>
-- phone_verified: true
-- email_verified: false

-- STEP 6: General cleanup for all duplicate profiles (OPTIONAL - BE CAREFUL)
-- This will find and clean up all duplicate profiles in your system

-- Find all phone numbers with multiple profiles
/*
SELECT phone, COUNT(*) as profile_count, array_agg(id) as profile_ids
FROM profiles 
WHERE phone IS NOT NULL 
GROUP BY phone 
HAVING COUNT(*) > 1;
*/

-- For each duplicate set, you would need to:
-- 1. Identify the primary profile (usually the one with phone_verified = true)
-- 2. Merge data from secondary profiles into the primary
-- 3. Delete secondary profiles
-- 4. Update auth.users if needed

-- Example cleanup function (USE WITH EXTREME CAUTION)
/*
DO $$
DECLARE
    phone_record RECORD;
    primary_profile_id UUID;
    temp_email TEXT;
BEGIN
    -- Loop through each phone number with duplicates
    FOR phone_record IN 
        SELECT phone, array_agg(id ORDER BY phone_verified DESC, created_at ASC) as profile_ids
        FROM profiles 
        WHERE phone IS NOT NULL 
        GROUP BY phone 
        HAVING COUNT(*) > 1
    LOOP
        -- Get the primary profile (first in the ordered array)
        primary_profile_id := phone_record.profile_ids[1];
        
        -- Generate temp email for primary profile if it doesn't have one
        SELECT email INTO temp_email FROM profiles WHERE id = primary_profile_id;
        
        IF temp_email IS NULL THEN
            temp_email := primary_profile_id || '@temp.grid2play.com';
            
            -- Update primary profile with temp email
            UPDATE profiles 
            SET email = temp_email, updated_at = NOW()
            WHERE id = primary_profile_id;
        END IF;
        
        -- Delete secondary profiles (skip the first one which is primary)
        DELETE FROM profiles 
        WHERE id = ANY(phone_record.profile_ids[2:]);
        
        RAISE NOTICE 'Cleaned up duplicates for phone %, kept profile %', phone_record.phone, primary_profile_id;
    END LOOP;
END $$;
*/

-- VERIFICATION QUERIES
-- Run these after cleanup to verify everything is correct

-- 1. Check for remaining duplicates
SELECT phone, COUNT(*) as count
FROM profiles 
WHERE phone IS NOT NULL 
GROUP BY phone 
HAVING COUNT(*) > 1;

-- 2. Check profiles without phone or email
SELECT COUNT(*) as profiles_without_phone_or_email
FROM profiles 
WHERE phone IS NULL AND email IS NULL;

-- 3. Check profiles with temp emails
SELECT COUNT(*) as profiles_with_temp_email
FROM profiles 
WHERE email LIKE '%@temp.grid2play.com';

-- 4. Verify your test user profile
SELECT * FROM profiles WHERE phone = '+918448609110';

COMMENT ON SCRIPT IS 'Cleanup script for duplicate profiles created by WhatsApp registration. Run the specific steps for your test user first, then optionally run the general cleanup.';
