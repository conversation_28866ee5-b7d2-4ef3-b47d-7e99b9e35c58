# 📱 MSG91 Service Integration Guide - Grid2Play Authentication System

## 📋 **Table of Contents**
1. [Overview](#overview)
2. [Authentication Flows](#authentication-flows)
3. [Technical Implementation](#technical-implementation)
4. [MSG91 Configuration](#msg91-configuration)
5. [API Reference](#api-reference)
6. [Troubleshooting Guide](#troubleshooting-guide)
7. [Security Considerations](#security-considerations)

---

## 🎯 **Overview**

MSG91 is integrated across Grid2Play's authentication system to provide:
- **WhatsApp OTP** for user registration and password reset
- **Email notifications** for booking confirmations and settlements
- **Multi-channel communication** ensuring reliable message delivery

### **Key Benefits**
- ✅ **Higher Conversion**: WhatsApp registration vs traditional email
- ✅ **Mobile-First**: Perfect for 90% mobile user base
- ✅ **Cost-Effective**: WhatsApp OTP vs SMS costs
- ✅ **Reliable Delivery**: Multiple communication channels
- ✅ **Template Management**: Centralized message templates

### **Integration Architecture**
```
Frontend Components → Supabase Edge Functions → MSG91 APIs → User Devices
                   ↓
              Database Functions → Rate Limiting & Validation
```

---

## 🔐 **Authentication Flows**

### **1. WhatsApp-Based User Registration (Signup)**

**Flow**: `Register.tsx` → `whatsappAuthService` → `send-whatsapp-otp` → MSG91 WhatsApp API

**Steps**:
1. User enters phone number and details
2. System generates 6-digit OTP
3. OTP sent via WhatsApp using MSG91
4. User verifies OTP
5. Account created with phone verification

**Components**:
- **Frontend**: `Register.tsx`, `whatsappAuthService.ts`
- **Edge Functions**: `send-whatsapp-otp`, `verify-whatsapp-otp`
- **Database**: `pending_whatsapp_users`, `profiles`, `user_roles`

### **2. Email + Password Login**

**Flow**: `Login.tsx` → `useSecureAuth` → Supabase Auth

**Steps**:
1. User enters email and password
2. Direct Supabase authentication
3. No MSG91 involvement (unless forgot password)

**Components**:
- **Frontend**: `Login.tsx`, `useSecureAuth.ts`
- **Backend**: Supabase Auth only

### **3. Phone + Password Login (Signin)**

**Flow**: `Login.tsx` → `whatsappAuthService.signInWithPhone` → `phone-login-simple`

**Steps**:
1. User enters phone number and password
2. System validates credentials
3. Direct authentication (no OTP required)

**Components**:
- **Frontend**: `Login.tsx`, `whatsappAuthService.ts`
- **Edge Functions**: `phone-login-simple`
- **Database**: `profiles` lookup

### **4. WhatsApp OTP-Based Forgot Password**

**Flow**: `ForgotPasswordModal.tsx` → `forgot-password-whatsapp` → MSG91 WhatsApp API

**Steps**:
1. User enters phone number
2. System generates password reset OTP
3. OTP sent via WhatsApp using MSG91
4. User verifies OTP and sets new password

**Components**:
- **Frontend**: `ForgotPasswordModal.tsx`
- **Edge Functions**: `forgot-password-whatsapp`
- **Database**: `pending_whatsapp_users`, `profiles`

---

## 🔧 **Technical Implementation**

### **WhatsApp OTP Registration**

#### **Frontend Service** (`whatsappAuthService.ts`)
```typescript
async sendWhatsAppOTP(userData: WhatsAppUserData): Promise<WhatsAppAuthResult> {
  const { data, error } = await supabase.functions.invoke('send-whatsapp-otp', {
    body: {
      phone: formattedPhone,
      full_name: userData.full_name.trim(),
      password: userData.password
    }
  });
}
```

#### **Edge Function** (`send-whatsapp-otp`)
```typescript
// MSG91 WhatsApp API Payload
const msg91Payload = {
  integrated_number: "************", // Your registered WhatsApp number
  content_type: "template",
  payload: {
    messaging_product: "whatsapp",
    type: "template",
    template: {
      name: "grid2play_otp",
      language: { code: "en", policy: "deterministic" },
      namespace: "b2b10581_1cc2_41dd_9ff1_af56d017433d",
      to_and_components: [{
        to: [cleanPhone],
        components: {
          body_1: { type: "text", value: otp },
          button_1: { subtype: "url", type: "text", value: otp }
        }
      }]
    }
  }
}
```

#### **Database Functions**
```sql
-- Rate limiting check
SELECT check_otp_rate_limit(phone_number text) RETURNS boolean;

-- OTP validation
SELECT validate_whatsapp_otp(phone_number text, otp_input text) 
RETURNS TABLE(is_valid boolean, user_data jsonb, error_message text);

-- Password reset OTP validation
SELECT validate_password_reset_otp(phone_number text, otp_input text)
RETURNS TABLE(is_valid boolean, user_data jsonb, error_message text);
```

### **Email Notifications**

#### **Booking Confirmation Email**
```typescript
// Template: booking_confirmation_grid2play
const templateVariables = {
  userName: booking.recipient_name,
  bookingReference: booking.booking_reference,
  courtName: booking.court_name,
  venueName: booking.venue_name,
  bookingDate: booking.formatted_date,
  startTime: booking.formatted_start_time,
  endTime: booking.formatted_end_time,
  // ... more variables
};
```

#### **Settlement Notification Email**
```typescript
// Template: settlement_notification_grid2play2
const templateVariables = {
  venueAdminName: settlement.venue_admin_name,
  settlementReference: settlement.reference,
  settlementPeriod: settlement.period,
  totalBookings: settlement.total_bookings,
  grossRevenue: settlement.gross_revenue,
  platformFees: settlement.platform_fees,
  netRevenue: settlement.net_revenue
};
```

---

## ⚙️ **MSG91 Configuration**

### **Environment Variables**

#### **Supabase Edge Functions**
```bash
# Required for all MSG91 services
MSG91_AUTH_KEY=your_msg91_auth_key

# WhatsApp Configuration
MSG91_INTEGRATED_NUMBER=************  # Your registered WhatsApp business number

# Email Configuration  
MSG91_DOMAIN=grid2play.com  # Your verified domain
```

#### **Frontend Configuration**
```typescript
// No direct MSG91 credentials in frontend
// All API calls go through Supabase Edge Functions
```

### **MSG91 Template Configuration**

#### **WhatsApp Templates**
```yaml
Template Name: grid2play_otp
Language: English
Namespace: b2b10581_1cc2_41dd_9ff1_af56d017433d
Components:
  - Body: "Your Grid2Play OTP is {{1}}. Valid for 5 minutes."
  - Button: URL button with OTP parameter
```

#### **Email Templates**
```yaml
Templates:
  - authentication_grid2play: Email verification
  - resend_grid2play: Resend verification
  - booking_confirmation_grid2play: Booking confirmations
  - settlement_notification_grid2play2: Settlement notifications
```

### **API Endpoints**

#### **WhatsApp API**
```
POST https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/
Headers:
  - Content-Type: application/json
  - authkey: {MSG91_AUTH_KEY}
```

#### **Email API**
```
POST https://control.msg91.com/api/v5/email/send
Headers:
  - Content-Type: application/json
  - authkey: {MSG91_AUTH_KEY}
```

---

## 📚 **API Reference**

### **Edge Functions**

#### **`send-whatsapp-otp`**
**Purpose**: Send WhatsApp OTP for registration
**Parameters**:
```typescript
{
  phone: string,        // E.164 format: +************
  full_name: string,    // User's full name
  password: string      // User's password (hashed in production)
}
```
**Response**:
```typescript
{
  success: boolean,
  message?: string,
  phone?: string,
  expires_in?: number,  // 300 seconds (5 minutes)
  error?: string
}
```

#### **`verify-whatsapp-otp`**
**Purpose**: Verify WhatsApp OTP and create user account
**Parameters**:
```typescript
{
  phone: string,        // E.164 format
  otp: string          // 6-digit OTP
}
```
**Response**:
```typescript
{
  success: boolean,
  message?: string,
  user?: {
    id: string,
    phone: string,
    full_name: string,
    phone_verified: boolean
  },
  error?: string
}
```

#### **`forgot-password-whatsapp`**
**Purpose**: WhatsApp OTP-based password reset
**Parameters**:
```typescript
{
  phone: string,           // E.164 format
  action: 'send-otp' | 'verify-otp' | 'reset-password',
  otp?: string,           // Required for verify-otp and reset-password
  newPassword?: string    // Required for reset-password
}
```
**Response**:
```typescript
{
  success: boolean,
  message?: string,
  error?: string
}
```

#### **`send-msg91-email`**
**Purpose**: Send template-based emails
**Parameters**:
```typescript
{
  to: string,                    // Recipient email
  name: string,                  // Recipient name
  templateId: string,            // MSG91 template ID
  variables: Record<string, any> // Template variables
}
```

### **Database Functions**

#### **`check_otp_rate_limit(phone_number text)`**
**Purpose**: Check if phone number has exceeded OTP rate limit
**Returns**: `boolean` (true if allowed, false if rate limited)
**Rate Limit**: 3 OTPs per phone per hour

#### **`validate_whatsapp_otp(phone_number text, otp_input text)`**
**Purpose**: Validate WhatsApp OTP for registration
**Returns**: 
```sql
TABLE(
  is_valid boolean,
  user_data jsonb,
  error_message text
)
```

#### **`validate_password_reset_otp(phone_number text, otp_input text)`**
**Purpose**: Validate WhatsApp OTP for password reset
**Returns**: Same as `validate_whatsapp_otp`

---

## 🚨 **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **1. WhatsApp OTP Not Delivered**
**Symptoms**: User doesn't receive WhatsApp OTP
**Causes & Solutions**:
```
❌ Wrong sender number → ✅ Use registered number: ************
❌ Invalid template → ✅ Verify template name: grid2play_otp
❌ Missing namespace → ✅ Include: b2b10581_1cc2_41dd_9ff1_af56d017433d
❌ Wrong API endpoint → ✅ Use: /whatsapp-outbound-message/bulk/
```

#### **2. MSG91 API Parameter Errors**
**Error**: `"(#100) The parameter messaging_product is required"`
**Solution**: Ensure payload includes `messaging_product: "whatsapp"`

**Error**: `"WhatsApp not integrated:{phone_number}"`
**Solution**: Use correct `integrated_number` in payload

#### **3. Rate Limiting Issues**
**Error**: `"Too many OTP requests"`
**Solution**: 
- Check `pending_whatsapp_users` table for recent attempts
- Wait 1 hour or clear rate limit manually
- Verify `check_otp_rate_limit` function

#### **4. OTP Validation Failures**
**Error**: `"Invalid OTP"`
**Debugging**:
```sql
-- Check pending OTP
SELECT phone, otp_code, otp_expires_at, attempts 
FROM pending_whatsapp_users 
WHERE phone = '+************';

-- Check expiry
SELECT otp_expires_at > NOW() as is_valid 
FROM pending_whatsapp_users 
WHERE phone = '+************';
```

### **Debugging Tools**

#### **Edge Function Logs**
```bash
# View function logs in Supabase Dashboard
Functions → {function_name} → Logs

# Look for these log messages:
- "Generated OTP for phone: {phone}"
- "Sending WhatsApp OTP via MSG91"
- "WhatsApp OTP sent successfully"
```

#### **Database Monitoring**
```sql
-- Monitor OTP attempts
SELECT phone, attempts, last_otp_sent_at, otp_expires_at
FROM pending_whatsapp_users 
ORDER BY last_otp_sent_at DESC;

-- Check successful registrations
SELECT phone, phone_verified, created_at
FROM profiles 
WHERE phone_verified = true 
ORDER BY created_at DESC;
```

#### **MSG91 Dashboard**
- Check delivery reports in MSG91 dashboard
- Verify template approval status
- Monitor API usage and credits

---

## 🔒 **Security Considerations**

### **Rate Limiting**
- **OTP Requests**: 3 per phone per hour
- **OTP Attempts**: 3 attempts per OTP
- **OTP Expiry**: 5 minutes

### **Data Protection**
- **Phone Numbers**: Stored in E.164 format
- **OTP Codes**: 6-digit random generation
- **Passwords**: Hashed before storage
- **Cleanup**: Expired OTPs automatically removed

### **API Security**
- **Environment Variables**: Stored in Supabase secrets
- **CORS**: Configured for specific origins
- **Authentication**: Service role for Edge Functions
- **Input Validation**: Phone format and OTP validation

### **Best Practices**
1. **Never expose MSG91 credentials** in frontend code
2. **Use service role** for database operations in Edge Functions
3. **Implement proper error handling** without exposing sensitive data
4. **Monitor rate limits** to prevent abuse
5. **Regular cleanup** of expired OTP records

---

## 📊 **Monitoring & Analytics**

### **Key Metrics**
- **OTP Delivery Rate**: Track successful WhatsApp deliveries
- **Conversion Rate**: Registration completion after OTP
- **Error Rates**: Failed API calls and validations
- **Response Times**: MSG91 API performance

### **Health Checks**
```typescript
// Test MSG91 integration
const testResult = await supabase.functions.invoke('get-msg91-config');
console.log('MSG91 Config:', testResult.data);
```

---

---

## 🛠️ **Implementation Examples**

### **Frontend Integration Example**

#### **WhatsApp Registration Component**
```typescript
// Register.tsx - WhatsApp OTP Registration
import { whatsappAuthService } from '@/services/whatsappAuthService';

const handleWhatsAppRegistration = async () => {
  // Step 1: Send OTP
  const otpResult = await whatsappAuthService.sendWhatsAppOTP({
    phone: '+************',
    full_name: 'John Doe',
    password: 'securePassword123'
  });

  if (otpResult.success) {
    setStep('otp-verification');
  }

  // Step 2: Verify OTP
  const verifyResult = await whatsappAuthService.verifyWhatsAppOTP({
    phone: '+************',
    otp: '123456'
  });

  if (verifyResult.success) {
    // User registered successfully
    router.push('/dashboard');
  }
};
```

#### **Forgot Password Modal**
```typescript
// ForgotPasswordModal.tsx - WhatsApp Password Reset
const handleForgotPassword = async () => {
  // Step 1: Send reset OTP
  const { data } = await supabase.functions.invoke('forgot-password-whatsapp', {
    body: { phone: '+************', action: 'send-otp' }
  });

  // Step 2: Verify OTP
  const verifyData = await supabase.functions.invoke('forgot-password-whatsapp', {
    body: { phone: '+************', otp: '123456', action: 'verify-otp' }
  });

  // Step 3: Reset password
  const resetData = await supabase.functions.invoke('forgot-password-whatsapp', {
    body: {
      phone: '+************',
      otp: '123456',
      newPassword: 'newPassword123',
      action: 'reset-password'
    }
  });
};
```

### **Backend Edge Function Example**

#### **Custom WhatsApp OTP Function**
```typescript
// supabase/functions/custom-whatsapp-otp/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

serve(async (req) => {
  const { phone, action, otp } = await req.json();

  const supabaseAdmin = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  if (action === 'send') {
    // Generate and send OTP
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();

    // MSG91 WhatsApp API call
    const msg91Response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': Deno.env.get('MSG91_AUTH_KEY')
      },
      body: JSON.stringify({
        integrated_number: "************",
        content_type: "template",
        payload: {
          messaging_product: "whatsapp",
          type: "template",
          template: {
            name: "grid2play_otp",
            language: { code: "en", policy: "deterministic" },
            namespace: "b2b10581_1cc2_41dd_9ff1_af56d017433d",
            to_and_components: [{
              to: [phone],
              components: {
                body_1: { type: "text", value: otpCode },
                button_1: { subtype: "url", type: "text", value: otpCode }
              }
            }]
          }
        }
      })
    });

    return new Response(JSON.stringify({ success: true }));
  }
});
```

### **Database Schema Example**

#### **WhatsApp OTP Tables**
```sql
-- Pending WhatsApp users table
CREATE TABLE pending_whatsapp_users (
  phone TEXT PRIMARY KEY,
  full_name TEXT NOT NULL,
  password_hash TEXT NOT NULL,
  otp_code TEXT NOT NULL,
  otp_expires_at TIMESTAMPTZ NOT NULL,
  attempts INTEGER DEFAULT 0,
  purpose TEXT DEFAULT 'registration', -- 'registration' or 'password_reset'
  last_otp_sent_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT unique_phone UNIQUE (phone)
);

-- Rate limiting function
CREATE OR REPLACE FUNCTION check_otp_rate_limit(phone_number text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  recent_count integer;
BEGIN
  -- Count OTPs sent in the last hour
  SELECT COUNT(*) INTO recent_count
  FROM pending_whatsapp_users
  WHERE phone = phone_number
    AND last_otp_sent_at > NOW() - INTERVAL '1 hour';

  -- Allow if less than 3 attempts in the last hour
  RETURN recent_count < 3;
END;
$$;
```

---

## 📈 **Performance Optimization**

### **Caching Strategies**
```typescript
// Cache MSG91 config to avoid repeated API calls
class MSG91Service {
  private static configCache: MSG91Config | null = null;
  private static cacheExpiry: number = 0;

  private static async getConfig(): Promise<MSG91Config> {
    const now = Date.now();
    if (this.configCache && now < this.cacheExpiry) {
      return this.configCache;
    }

    const config = await this.fetchConfig();
    this.configCache = config;
    this.cacheExpiry = now + (5 * 60 * 1000); // 5 minutes
    return config;
  }
}
```

### **Batch Processing**
```typescript
// Batch email notifications
const batchSendEmails = async (recipients: EmailRecipient[]) => {
  const batches = chunk(recipients, 100); // Process in batches of 100

  for (const batch of batches) {
    await Promise.all(
      batch.map(recipient =>
        supabase.functions.invoke('send-msg91-email', {
          body: recipient
        })
      )
    );

    // Rate limiting between batches
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
};
```

### **Error Handling & Retries**
```typescript
// Retry mechanism for MSG91 API calls
const retryMSG91Call = async (apiCall: () => Promise<any>, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      if (attempt === maxRetries) throw error;

      // Exponential backoff
      const delay = Math.pow(2, attempt) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

---

## 🔄 **Migration Guide**

### **From SMS to WhatsApp**
```typescript
// Before: SMS OTP
const sendSMSOTP = async (phone: string, otp: string) => {
  await fetch('https://api.msg91.com/api/v5/otp', {
    method: 'POST',
    body: { mobile: phone, otp: otp }
  });
};

// After: WhatsApp OTP
const sendWhatsAppOTP = async (phone: string, otp: string) => {
  await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
    method: 'POST',
    body: {
      integrated_number: "************",
      payload: {
        messaging_product: "whatsapp",
        template: { name: "grid2play_otp" }
      }
    }
  });
};
```

### **Environment Migration**
```bash
# Old environment variables
SMS_AUTH_KEY=old_sms_key
SMS_TEMPLATE_ID=old_template

# New environment variables
MSG91_AUTH_KEY=unified_auth_key
MSG91_INTEGRATED_NUMBER=************
MSG91_DOMAIN=grid2play.com
```

---

## 📋 **Deployment Checklist**

### **Pre-Deployment**
- [ ] MSG91 account setup and verification
- [ ] WhatsApp Business API approval
- [ ] Template creation and approval
- [ ] Domain verification for email
- [ ] Environment variables configured

### **Deployment Steps**
1. **Database Setup**
   - [ ] Create `pending_whatsapp_users` table
   - [ ] Deploy database functions
   - [ ] Set up RLS policies

2. **Edge Functions**
   - [ ] Deploy `send-whatsapp-otp`
   - [ ] Deploy `verify-whatsapp-otp`
   - [ ] Deploy `forgot-password-whatsapp`
   - [ ] Deploy `send-msg91-email`

3. **Frontend Integration**
   - [ ] Update authentication components
   - [ ] Test WhatsApp registration flow
   - [ ] Test forgot password flow
   - [ ] Test email notifications

### **Post-Deployment Testing**
- [ ] WhatsApp OTP delivery
- [ ] Email template rendering
- [ ] Rate limiting functionality
- [ ] Error handling scenarios
- [ ] Performance monitoring

---

## 📞 **Support & Maintenance**

### **Monitoring Dashboard**
```sql
-- Daily MSG91 usage report
SELECT
  DATE(created_at) as date,
  COUNT(*) as total_otps,
  COUNT(CASE WHEN attempts > 0 THEN 1 END) as verified_otps,
  ROUND(COUNT(CASE WHEN attempts > 0 THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM pending_whatsapp_users
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

### **Health Check Endpoint**
```typescript
// supabase/functions/msg91-health-check/index.ts
serve(async (req) => {
  const checks = {
    msg91_config: await checkMSG91Config(),
    whatsapp_template: await checkWhatsAppTemplate(),
    email_template: await checkEmailTemplate(),
    database_functions: await checkDatabaseFunctions()
  };

  const allHealthy = Object.values(checks).every(check => check.healthy);

  return new Response(JSON.stringify({
    status: allHealthy ? 'healthy' : 'unhealthy',
    checks: checks,
    timestamp: new Date().toISOString()
  }), {
    status: allHealthy ? 200 : 503
  });
});
```

### **Maintenance Tasks**
```sql
-- Weekly cleanup of expired OTPs
DELETE FROM pending_whatsapp_users
WHERE otp_expires_at < NOW() - INTERVAL '24 hours';

-- Monthly usage analysis
SELECT
  EXTRACT(MONTH FROM created_at) as month,
  COUNT(*) as total_registrations,
  COUNT(CASE WHEN phone_verified = true THEN 1 END) as successful_registrations
FROM profiles
WHERE created_at >= NOW() - INTERVAL '3 months'
GROUP BY EXTRACT(MONTH FROM created_at);
```

---

**This comprehensive documentation covers all aspects of MSG91 integration in Grid2Play. For additional support, refer to MSG91's official documentation and Grid2Play's internal development guidelines.**
