# Deployment & Development Guide

This guide covers the complete setup, development workflow, and deployment process for Grid2play.

## Prerequisites

### Required Software
- **Node.js** 18+ and npm (install via [nvm](https://github.com/nvm-sh/nvm))
- **Git** for version control
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter

### Required Accounts & Services
- **Supabase** account and project
- **MSG91** account for email services
- **Razorpay** account for payment processing
- **Lovable** account for AI-assisted development

## Local Development Setup

### 1. <PERSON>lone and Install
```bash
# Clone the repository
git clone <repository-url>
cd grid2play

# Install dependencies
npm install

# Copy environment template
cp .env.example .env.local
```

### 2. Environment Configuration

Create `.env.local` with the following variables:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Razorpay Configuration
VITE_RAZORPAY_KEY_ID=your-razorpay-key-id

# MSG91 Configuration (for Edge Functions)
MSG91_AUTH_KEY=your-msg91-auth-key
MSG91_SENDER_ID=your-sender-id

# Optional: Development flags
VITE_LOG_LEVEL=DEBUG
VITE_ENABLE_ANALYTICS=false
```

### 3. Supabase Setup

#### Database Setup
```bash
# Install Supabase CLI
npm install -g @supabase/cli

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref your-project-ref

# Run database migrations
supabase db reset

# Apply custom functions
psql -h db.your-project.supabase.co -U postgres -d postgres -f supabase/run-functions.sql
```

#### Edge Functions Setup
```bash
# Deploy Edge Functions
supabase functions deploy send-verification-email
supabase functions deploy send-booking-confirmation
supabase functions deploy chat-assistant
supabase functions deploy payment-webhook

# Set function secrets
supabase secrets set MSG91_AUTH_KEY=your-msg91-key
supabase secrets set MSG91_SENDER_ID=your-sender-id
supabase secrets set RAZORPAY_KEY_SECRET=your-razorpay-secret
```

### 4. Start Development Server
```bash
# Start the development server
npm run dev

# The application will be available at http://localhost:8080
```

## Development Workflow

### 1. Feature Development Process

#### Branch Strategy
```bash
# Create feature branch
git checkout -b feature/booking-improvements

# Make changes and commit
git add .
git commit -m "feat: improve booking conflict detection"

# Push and create PR
git push origin feature/booking-improvements
```

#### Code Quality Checks
```bash
# Run linting
npm run lint

# Run type checking
npx tsc --noEmit

# Run tests (when available)
npm test

# Build for production
npm run build
```

### 2. Database Changes

#### Schema Modifications
```sql
-- Create migration file: supabase/migrations/YYYYMMDD_description.sql
ALTER TABLE bookings ADD COLUMN booking_reference TEXT UNIQUE;

-- Update types
supabase gen types typescript --local > src/integrations/supabase/types.ts
```

#### Testing Database Changes
```bash
# Reset local database
supabase db reset

# Test migrations
supabase db push

# Verify changes
supabase db diff
```

### 3. Component Development

#### Creating New Components
```typescript
// src/components/NewFeature.tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface NewFeatureProps {
  title: string;
  data: any[];
}

export const NewFeature: React.FC<NewFeatureProps> = ({ title, data }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Component content */}
      </CardContent>
    </Card>
  );
};
```

#### Adding to Route Structure
```typescript
// src/App.tsx
import NewFeature from './components/NewFeature';

// Add route
<Route path="/new-feature" element={<NewFeature />} />
```

### 4. API Integration

#### Adding New Supabase Operations
```typescript
// src/services/newFeatureService.ts
import { supabase } from '@/integrations/supabase/client';

export const newFeatureService = {
  async getData(filters: any) {
    const { data, error } = await supabase
      .from('table_name')
      .select('*')
      .match(filters);
    
    if (error) throw error;
    return data;
  },

  async createRecord(record: any) {
    const { data, error } = await supabase
      .from('table_name')
      .insert(record)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
};
```

#### Creating Custom Hooks
```typescript
// src/hooks/useNewFeature.ts
import { useState, useEffect } from 'react';
import { newFeatureService } from '@/services/newFeatureService';

export const useNewFeature = (filters: any) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await newFeatureService.getData(filters);
        setData(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]);

  return { data, loading, error };
};
```

## Testing Strategy

### 1. Manual Testing Checklist

#### User Flow Testing
- [ ] User registration and email verification
- [ ] Login and authentication
- [ ] Venue discovery and filtering
- [ ] Booking creation and payment
- [ ] Booking management (view, cancel)
- [ ] Tournament registration and participation
- [ ] Admin dashboard functionality
- [ ] Mobile responsiveness

#### Cross-Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

### 2. Performance Testing
```bash
# Build and analyze bundle
npm run build
npx vite-bundle-analyzer dist

# Lighthouse audit
npx lighthouse http://localhost:8080 --output html --output-path ./lighthouse-report.html
```

## Deployment Process

### 1. Lovable Deployment (Recommended)

#### Automatic Deployment
1. Push changes to the main branch
2. Lovable automatically detects changes
3. Builds and deploys to production
4. Updates are live within minutes

#### Manual Deployment via Lovable
1. Open [Lovable Project](https://lovable.dev/projects/23afaaf0-a670-4c15-9768-765483b4c88d)
2. Click "Share" → "Publish"
3. Configure deployment settings
4. Deploy to production

### 2. Custom Domain Setup

#### DNS Configuration
```bash
# Add CNAME record
CNAME www your-app.lovable.app

# Add A record for apex domain
A @ ***********
```

#### SSL Certificate
- Automatic SSL via Lovable
- Custom certificates supported for enterprise plans

### 3. Environment Variables for Production

#### Supabase Production Setup
```env
VITE_SUPABASE_URL=https://your-prod-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-prod-anon-key
VITE_RAZORPAY_KEY_ID=your-prod-razorpay-key
```

#### Edge Function Secrets
```bash
# Set production secrets
supabase secrets set --project-ref your-prod-ref MSG91_AUTH_KEY=prod-key
supabase secrets set --project-ref your-prod-ref RAZORPAY_KEY_SECRET=prod-secret
```

## Monitoring & Maintenance

### 1. Application Monitoring

#### Supabase Dashboard
- Monitor database performance
- Track API usage and errors
- Review authentication metrics
- Monitor Edge Function performance

#### Error Tracking
```typescript
// Add error boundary for production
class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log to monitoring service
    console.error('Application error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

### 2. Performance Monitoring
```typescript
// Performance tracking
const trackPageLoad = () => {
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
  
  // Send to analytics
  console.log('Page load time:', loadTime);
};
```

### 3. Database Maintenance

#### Regular Tasks
```sql
-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM bookings WHERE user_id = 'uuid';

-- Update table statistics
ANALYZE bookings;

-- Monitor index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

#### Backup Strategy
- Supabase automatic daily backups
- Point-in-time recovery available
- Export critical data regularly

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Vite cache
rm -rf .vite
npm run dev
```

#### Database Connection Issues
```bash
# Check Supabase status
curl https://status.supabase.com/api/v2/status.json

# Test database connection
supabase db ping
```

#### Edge Function Errors
```bash
# Check function logs
supabase functions logs send-verification-email

# Test function locally
supabase functions serve
curl -X POST http://localhost:54321/functions/v1/send-verification-email
```

### Performance Issues
1. **Slow queries**: Add database indexes
2. **Large bundle size**: Implement code splitting
3. **Memory leaks**: Check for unsubscribed listeners
4. **Slow API calls**: Implement caching strategies

## Security Checklist

- [ ] Environment variables secured
- [ ] RLS policies implemented
- [ ] Input validation in place
- [ ] Authentication required for protected routes
- [ ] HTTPS enforced in production
- [ ] API rate limiting configured
- [ ] Sensitive data encrypted
- [ ] Regular security audits performed

This guide ensures a smooth development experience and reliable production deployment for Grid2play.
