-- EMERGENCY: Fix your current user profile
-- User ID: 95ca34a0-1e40-4bd6-85d8-5719f3e6c28a
-- Phone: +918448609110

-- Step 1: Check current profile state
SELECT 
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified,
  created_at
FROM profiles 
WHERE id = '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a';

-- Step 2: Check if phone profile exists
SELECT 
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified
FROM profiles 
WHERE phone = '+918448609110';

-- Step 3: Check auth.users table
SELECT id, email, phone, email_confirmed_at, phone_confirmed_at
FROM auth.users 
WHERE id = '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a';

-- Step 4: EMERGENCY FIX - Create/Update profile if missing
-- This will ensure your user can login with both phone methods

INSERT INTO profiles (
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified,
  xp,
  level,
  wins,
  losses,
  draws,
  created_at,
  updated_at
) VALUES (
  '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a',
  'Whatsapp ani',
  '+918448609110',
  '<EMAIL>',
  true,  -- phone_verified = true
  false, -- email_verified = false
  0,     -- xp
  1,     -- level
  0,     -- wins
  0,     -- losses
  0,     -- draws
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  phone = EXCLUDED.phone,
  email = EXCLUDED.email,
  phone_verified = EXCLUDED.phone_verified,
  full_name = EXCLUDED.full_name,
  updated_at = NOW();

-- Step 5: Ensure user role exists
INSERT INTO user_roles (user_id, role)
VALUES ('95ca34a0-1e40-4bd6-85d8-5719f3e6c28a', 'user')
ON CONFLICT (user_id, role) DO NOTHING;

-- Step 6: Verify the fix
SELECT 
  'Profile fixed' as status,
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified
FROM profiles 
WHERE id = '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a';

-- Step 7: Check user role
SELECT 'User role' as status, user_id, role
FROM user_roles 
WHERE user_id = '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a';

-- Step 8: Test queries that should now work
SELECT 'Phone lookup test' as test, COUNT(*) as found
FROM profiles 
WHERE phone = '+918448609110' AND phone_verified = true;

-- Expected results after running this script:
-- 1. Profile exists with phone +918448609110
-- 2. phone_verified = true
-- 3. email = temp email for authentication
-- 4. User role exists
-- 5. Phone login should work
-- 6. WhatsApp OTP login should work
-- 7. Email verification should work (after creating the tokens table)
