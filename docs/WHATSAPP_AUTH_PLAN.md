# WhatsApp OTP Authentication Implementation Plan

## 🔍 Current Authentication System Analysis

### ✅ What's Already Implemented:

1. **Supabase Auth Foundation**: Email/password authentication with session management
2. **Custom Email Verification**: Using MSG91 for beautiful email templates  
3. **Pending Users System**: `pending_users` table for pre-verification storage
4. **Security Features**: Rate limiting, input validation, secure error handling
5. **Database Schema**: `profiles` table with `phone_verified` and `email_verified` columns
6. **Two-Stage Registration**: Email verification → Account creation workflow

### 🔧 Current Registration Flow:

1. User fills registration form (name, email, phone, password)
2. Data stored in `pending_users` table with verification token
3. MSG91 sends email with verification link
4. User clicks link → `create-verified-user` edge function creates Supabase user
5. User can login with email/password

---

## 🎯 WhatsApp OTP Integration Plan

### Phase 1: Database Setup

Create `pending_whatsapp_users` table for OTP management:

```sql
CREATE TABLE pending_whatsapp_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone TEXT NOT NULL,
  full_name TEXT,
  password_hash TEXT NOT NULL,
  otp_code TEXT NOT NULL,
  otp_expires_at TIMESTAMPTZ NOT NULL,
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(phone)
);
```

Add OTP tracking and rate limiting:
- OTP expiry (5 minutes)
- Maximum 3 attempts per phone
- Rate limiting (max 3 OTPs per phone per hour)

### Phase 2: MSG91 WhatsApp Integration

#### 2.1 Environment Variables
Add to Supabase secrets:
```
MSG91_WHATSAPP_AUTH_KEY=your_whatsapp_auth_key
MSG91_WHATSAPP_TEMPLATE_ID=your_otp_template_id
```

#### 2.2 Edge Functions
Create new edge functions:

**`send-whatsapp-otp`**:
- Generate 6-digit OTP
- Store in `pending_whatsapp_users` table
- Send via MSG91 WhatsApp API
- Implement rate limiting

**`verify-whatsapp-otp`**:
- Validate OTP and expiry
- Create Supabase user on success
- Update `profiles` with `phone_verified: true`
- Clean up pending data

### Phase 3: Frontend Components

#### 3.1 Registration Page Enhancement
Add phone number selection tab:
- "Sign up with Email" (existing)
- "Sign up with Phone/WhatsApp" (new)

#### 3.2 WhatsApp Registration Flow
Create new components:
- `WhatsAppRegisterForm`: Phone number + name + password
- `WhatsAppOTPVerification`: 6-digit OTP input with timer
- Country code selector with flag icons
- Phone number formatting and validation

#### 3.3 User Experience Flow
1. **Registration Choice**: User selects "Sign up with Phone/WhatsApp"
2. **Phone Entry**: User enters phone number with country code
3. **OTP Delivery**: System sends 6-digit OTP via WhatsApp using MSG91
4. **OTP Verification**: User enters OTP → system validates and creates account
5. **Account Creation**: Create Supabase user with `phone_verified: true`
6. **Login**: User can login with phone + password (optional email linking)

### Phase 4: Service Integration

#### 4.1 WhatsApp Auth Service
Create `whatsappAuthService.ts` similar to `customAuthService.ts`:
- `signUpWithWhatsApp()`
- `sendWhatsAppOTP()`
- `verifyWhatsAppOTP()`
- `resendWhatsAppOTP()`

#### 4.2 Auth Context Updates
Update `AuthContext.tsx` to support:
- Phone-based registration
- Hybrid auth flow selection
- Phone number formatting utilities

### Phase 5: Security & Validation

#### 5.1 Phone Number Handling
- International format validation
- Country code detection
- Phone number sanitization
- Duplicate phone prevention

#### 5.2 OTP Security
- Secure 6-digit OTP generation
- Time-based expiry (5 minutes)
- Attempt limiting (3 tries)
- Rate limiting (3 OTPs/hour per phone)

#### 5.3 Fallback Strategy
- WhatsApp delivery failure → Email fallback
- Clear error messaging
- Retry mechanisms

---

## 🔧 Technical Implementation Details

### MSG91 WhatsApp API Integration

MSG91 provides WhatsApp Business API integration. Key endpoints:

1. **Send OTP**: `https://control.msg91.com/api/v5/whatsapp/send`
2. **Template Management**: Use MSG91 dashboard for OTP templates

### Database Schema Changes

Extend existing schema with WhatsApp-specific tables while maintaining backward compatibility with email authentication.

### Frontend Architecture

Maintain existing email auth flow while adding parallel WhatsApp flow:
- Tabbed registration interface
- Shared validation utilities
- Unified user experience

### Security Considerations

- OTP brute force protection
- Phone number verification
- Rate limiting implementation
- Secure token generation
- Session management consistency

---

## 🚀 Implementation Priority

1. **High Priority**: Database setup and MSG91 WhatsApp integration
2. **Medium Priority**: Frontend components and user experience
3. **Low Priority**: Advanced features like email linking

This hybrid system will provide cost-effective phone verification using WhatsApp while maintaining your existing email authentication infrastructure.

---

## 📋 Next Steps

1. **Discuss and approve this plan**
2. **Set up MSG91 WhatsApp Business API credentials**
3. **Create database schema for WhatsApp OTP**
4. **Implement backend edge functions**
5. **Build frontend components**
6. **Test and deploy**

Would you like to proceed with implementing this WhatsApp OTP authentication system?
