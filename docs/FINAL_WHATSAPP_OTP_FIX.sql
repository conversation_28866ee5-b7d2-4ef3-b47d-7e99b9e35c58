-- FINAL FIX: WhatsApp OTP Login Issue
-- Problem: Duplicate profiles causing lookup failures
-- Solution: Clean up duplicates and ensure single profile

-- Step 1: Check current state
SELECT 
  'Current profiles for phone +918448609110' as status,
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified,
  created_at
FROM profiles 
WHERE phone = '+918448609110'
ORDER BY created_at DESC;

-- Step 2: Delete the old duplicate profile (temp email)
DELETE FROM user_roles 
WHERE user_id = '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a';

DELETE FROM profiles 
WHERE id = '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a';

-- Step 3: Ensure the correct profile exists and is properly configured
UPDATE profiles 
SET 
  phone_verified = true,
  updated_at = NOW()
WHERE id = 'ded9a052-2386-4c2b-b7f9-98f9eb7d218f';

-- Step 4: Ensure user role exists for the correct profile
INSERT INTO user_roles (user_id, role)
VALUES ('ded9a052-2386-4c2b-b7f9-98f9eb7d218f', 'user')
ON CONFLICT (user_id, role) DO NOTHING;

-- Step 5: Verify the fix
SELECT 
  'Final verification' as status,
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified
FROM profiles 
WHERE phone = '+918448609110';

-- Step 6: Check user role
SELECT 
  'User role verification' as status,
  user_id, 
  role
FROM user_roles 
WHERE user_id = 'ded9a052-2386-4c2b-b7f9-98f9eb7d218f';

-- Step 7: Test query that WhatsApp OTP login uses
SELECT 
  'WhatsApp OTP lookup test' as test,
  COUNT(*) as found,
  array_agg(id) as profile_ids
FROM profiles 
WHERE phone = '+918448609110' AND phone_verified = true;

-- Expected results:
-- 1. Only ONE profile with id: ded9a052-2386-4c2b-b7f9-98f9eb7d218f
-- 2. phone_verified = true
-- 3. email = <EMAIL> (real email)
-- 4. User role exists
-- 5. WhatsApp OTP lookup test shows found = 1

-- Step 8: Clean up any other potential duplicates
DELETE FROM profiles 
WHERE phone = '+918448609110' 
AND id != 'ded9a052-2386-4c2b-b7f9-98f9eb7d218f';

-- Final verification
SELECT 
  'FINAL CHECK - Should show exactly 1 profile' as final_status,
  COUNT(*) as total_profiles,
  array_agg(id) as all_profile_ids
FROM profiles 
WHERE phone = '+918448609110';
