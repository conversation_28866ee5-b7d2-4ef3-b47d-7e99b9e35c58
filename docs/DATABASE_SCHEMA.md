# Database Schema Documentation

This document provides a comprehensive overview of the Grid2play database schema, including table relationships, indexes, and business logic constraints.

## Overview

Grid2play uses PostgreSQL via Supabase with Row Level Security (RLS) enabled on all tables. The schema is designed to support:
- Multi-tenant venue management
- Real-time booking conflict prevention
- Tournament and challenge systems
- Role-based access control
- Audit trails and analytics

## Core Tables

### Users & Authentication

#### `profiles`
User profile information extending Supabase auth.users
```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  full_name TEXT,
  phone TEXT,
  avatar_url TEXT,
  email_verified BOOLEAN DEFAULT FALSE,
  phone_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `user_roles`
Role-based access control system
```sql
CREATE TABLE user_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  role TEXT CHECK (role IN ('user', 'admin', 'super_admin')),
  venue_id UUID REFERENCES venues(id), -- NULL for super_admin
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Venue Management

#### `venues`
Sports facilities and their metadata
```sql
CREATE TABLE venues (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  description TEXT,
  capacity INTEGER,
  contact_number TEXT,
  opening_hours JSONB,
  image_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),
  rating DECIMAL(3,2) DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `sports`
Available sports types
```sql
CREATE TABLE sports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  icon_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `courts`
Individual playing areas within venues
```sql
CREATE TABLE courts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  venue_id UUID REFERENCES venues(id) ON DELETE CASCADE,
  sport_id UUID REFERENCES sports(id),
  name TEXT NOT NULL,
  description TEXT,
  hourly_rate DECIMAL(10,2) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Booking System

#### `bookings`
Core booking records with conflict prevention
```sql
CREATE TABLE bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  court_id UUID REFERENCES courts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id),
  booking_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  status TEXT DEFAULT 'confirmed' CHECK (status IN ('pending', 'confirmed', 'cancelled')),
  booking_reference TEXT UNIQUE,
  payment_reference TEXT,
  payment_status TEXT DEFAULT 'pending',
  payment_method TEXT,
  guest_name TEXT,
  guest_phone TEXT,
  booked_by_admin_id UUID REFERENCES profiles(id),
  cancellation_reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `blocked_slots`
Administrative time blocking
```sql
CREATE TABLE blocked_slots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  court_id UUID REFERENCES courts(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  reason TEXT,
  blocked_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `admin_bookings`
Admin-created bookings with additional metadata
```sql
CREATE TABLE admin_bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
  admin_id UUID REFERENCES profiles(id),
  customer_name TEXT NOT NULL,
  customer_phone TEXT,
  payment_method TEXT NOT NULL,
  payment_status TEXT,
  amount_collected DECIMAL(10,2),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Tournament System

#### `tournaments`
Tournament metadata and configuration
```sql
CREATE TABLE tournaments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  sport_id UUID REFERENCES sports(id),
  venue_id UUID REFERENCES venues(id),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  registration_deadline DATE NOT NULL,
  max_participants INTEGER NOT NULL,
  entry_fee DECIMAL(10,2) DEFAULT 0,
  prize_pool DECIMAL(10,2) DEFAULT 0,
  tournament_type TEXT DEFAULT 'knockout' CHECK (tournament_type IN ('knockout', 'round_robin', 'league')),
  status TEXT DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'registration_open', 'ongoing', 'completed', 'cancelled')),
  rules TEXT,
  contact_info TEXT,
  created_by UUID REFERENCES profiles(id),
  is_approved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `tournament_registrations`
Player/team tournament registrations
```sql
CREATE TABLE tournament_registrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID REFERENCES tournaments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id),
  team_name TEXT,
  team_members JSONB,
  registration_date TIMESTAMPTZ DEFAULT NOW(),
  payment_status TEXT DEFAULT 'pending',
  is_approved BOOLEAN DEFAULT FALSE
);
```

#### `tournament_fixtures`
Match scheduling and results
```sql
CREATE TABLE tournament_fixtures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID REFERENCES tournaments(id) ON DELETE CASCADE,
  round INTEGER NOT NULL,
  match_number INTEGER NOT NULL,
  team_a_id UUID,
  team_b_id UUID,
  team_a_name TEXT,
  team_b_name TEXT,
  venue_id UUID REFERENCES venues(id),
  court_id UUID REFERENCES courts(id),
  scheduled_date DATE,
  scheduled_time TIME,
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'completed', 'cancelled')),
  winner_id UUID,
  team_a_score INTEGER,
  team_b_score INTEGER,
  match_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Challenge System

#### `teams`
Team management for challenges
```sql
CREATE TABLE teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  sport_id UUID REFERENCES sports(id),
  captain_id UUID REFERENCES profiles(id),
  max_members INTEGER DEFAULT 10,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### `team_members`
Team membership tracking
```sql
CREATE TABLE team_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id),
  role TEXT DEFAULT 'member' CHECK (role IN ('captain', 'member')),
  joined_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Key Indexes and Performance Optimizations

### Booking Performance
```sql
-- Critical for booking conflict checks
CREATE INDEX idx_bookings_court_date_time ON bookings (court_id, booking_date, start_time, end_time);

-- User booking queries
CREATE INDEX idx_bookings_user_date ON bookings (user_id, booking_date);

-- Admin booking management
CREATE INDEX idx_bookings_status_date ON bookings (status, booking_date);
```

### Real-time Performance
```sql
-- Team chat performance
CREATE INDEX idx_team_chats_team_id ON team_chats(team_id);

-- Help request management
CREATE INDEX idx_help_requests_status ON help_requests(status);
CREATE INDEX idx_help_requests_user_id ON help_requests(user_id);
```

## Business Logic Constraints

### Booking Conflict Prevention
```sql
-- Trigger function to prevent overlapping bookings
CREATE OR REPLACE FUNCTION check_booking_conflicts()
RETURNS TRIGGER AS $$
DECLARE
    conflicts INTEGER;
BEGIN
    SELECT COUNT(*) INTO conflicts
    FROM bookings
    WHERE booking_date = NEW.booking_date
    AND court_id = NEW.court_id
    AND status IN ('confirmed', 'pending')
    AND ((start_time < NEW.end_time AND end_time > NEW.start_time))
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid);

    IF conflicts > 0 THEN
        RAISE EXCEPTION 'Booking conflicts with an existing reservation';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### Row Level Security (RLS) Policies

#### User Data Protection
```sql
-- Users can only see their own profile data
CREATE POLICY "Users can view own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

-- Users can only update their own profile
CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id);
```

#### Booking Access Control
```sql
-- Users can view their own bookings
CREATE POLICY "Users can view own bookings" ON bookings
FOR SELECT USING (auth.uid() = user_id);

-- Admins can view bookings for their venues
CREATE POLICY "Admins can view venue bookings" ON bookings
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN courts c ON c.venue_id = ur.venue_id
    WHERE ur.user_id = auth.uid()
    AND ur.role IN ('admin', 'super_admin')
    AND c.id = court_id
  )
);
```

## Data Relationships

### Primary Relationships
- `venues` → `courts` (1:many)
- `courts` → `bookings` (1:many)
- `sports` → `courts` (1:many)
- `users` → `bookings` (1:many)
- `tournaments` → `tournament_registrations` (1:many)
- `teams` → `team_members` (1:many)

### Cross-System Relationships
- `tournaments` → `venues` (many:1)
- `tournaments` → `sports` (many:1)
- `tournament_fixtures` → `courts` (many:1)
- `teams` → `sports` (many:1)

This schema design ensures data integrity, performance, and security while supporting the complex business requirements of a multi-tenant sports booking platform.
