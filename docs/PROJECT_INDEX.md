# Grid2play Project Index

This document serves as the central index for the Grid2play codebase, providing quick navigation to all major components, features, and documentation.

## 📋 Quick Navigation

- [🏗️ Architecture Overview](#architecture-overview)
- [📁 Directory Structure](#directory-structure)
- [🔧 Core Features](#core-features)
- [📚 Documentation](#documentation)
- [🛠️ Development Tools](#development-tools)
- [🔍 Key Files Reference](#key-files-reference)

## 🏗️ Architecture Overview

### Technology Stack
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: Tailwind CSS + shadcn/ui
- **Backend**: Supabase (PostgreSQL + Auth + Realtime + Edge Functions)
- **State Management**: TanStack Query + React Context
- **Payment**: Razorpay Integration
- **Email**: MSG91 Integration
- **Deployment**: Lovable Platform

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React App     │    │   Supabase      │    │  External APIs  │
│                 │    │                 │    │                 │
│ • Components    │◄──►│ • PostgreSQL    │◄──►│ • MSG91 Email   │
│ • Pages         │    │ • Auth          │    │ • Razor<PERSON>y      │
│ • Hooks         │    │ • Realtime      │    │ • Geolocation   │
│ • Context       │    │ • Edge Functions│    │ • AI Assistant  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Directory Structure

### Root Level
```
grid2play/
├── docs/                    # Documentation files
├── public/                  # Static assets
├── src/                     # Source code
├── supabase/               # Database functions and config
├── package.json            # Dependencies and scripts
├── vite.config.ts          # Build configuration
├── tailwind.config.ts      # Styling configuration
└── tsconfig.json           # TypeScript configuration
```

### Source Code Structure
```
src/
├── components/             # React components
│   ├── ui/                # Base UI components (shadcn/ui)
│   ├── admin/             # Admin-specific components
│   ├── challenge/         # Challenge system components
│   ├── tournament/        # Tournament components
│   └── [feature-components] # Feature-specific components
├── pages/                 # Route-level components
│   ├── admin/            # Admin dashboard pages
│   ├── challenge/        # Challenge pages
│   └── tournament/       # Tournament pages
├── context/              # React context providers
├── hooks/                # Custom React hooks
├── services/             # External service integrations
├── utils/                # Utility functions
├── types/                # TypeScript type definitions
├── integrations/         # Third-party integrations
│   └── supabase/        # Supabase client and types
├── App.tsx              # Main application component
└── main.tsx             # Application entry point
```

## 🔧 Core Features

### 1. User Management & Authentication
**Location**: `src/context/AuthContext.tsx`, `src/services/customAuthService.ts`
- Multi-role authentication (user/admin/super_admin)
- Email verification with MSG91
- Session management and security
- Profile management

### 2. Venue & Court Management
**Location**: `src/pages/admin/VenueManagement.tsx`, `src/components/admin/`
- Venue creation and editing
- Court configuration
- Sports assignment
- Location and capacity management
- Image gallery management

### 3. Booking System
**Location**: `src/components/BookSlotModal.tsx`, `src/pages/Bookings.tsx`
- Real-time slot availability
- Conflict prevention
- Payment integration (Razorpay)
- Booking confirmation emails
- Cancellation management

**Key Files**:
- `src/components/BookSlotModal.tsx` - Main booking interface
- `src/components/AvailabilityWidget.tsx` - Slot availability display
- `supabase/functions/prevent-double-bookings.sql` - Conflict prevention

### 4. Tournament Management
**Location**: `src/pages/tournament/`, `src/components/tournament/`
- Tournament creation and hosting
- Registration management
- Fixture generation
- Result tracking
- Prize distribution

**Key Files**:
- `src/pages/tournament/TournamentDashboard.tsx` - Tournament listing
- `src/pages/tournament/HostTournamentPage.tsx` - Tournament creation
- `src/types/tournament.ts` - Tournament type definitions

### 5. Challenge System
**Location**: `src/pages/challenge/`, `src/components/challenge/`
- Team creation and management
- Challenge participation
- Leaderboards and rankings
- Team communication

### 6. Admin Dashboard
**Location**: `src/pages/admin/`
- Analytics and reporting
- Booking management
- User management
- Venue administration
- Mobile-optimized admin interface

**Key Files**:
- `src/pages/admin/AdminHome.tsx` - Main admin dashboard
- `src/pages/admin/AnalyticsDashboard.tsx` - Business analytics
- `src/pages/admin/BookingManagement.tsx` - Booking administration

### 7. Real-time Features
**Location**: `src/integrations/supabase/realtime.ts`
- Live booking updates
- Team chat functionality
- Notification system
- Real-time availability updates

### 8. Mobile Experience
**Location**: `src/components/ui/BottomNav.tsx`, mobile-specific pages
- Mobile-first design
- Bottom navigation
- Touch-optimized interfaces
- Progressive Web App features

## 📚 Documentation

### Core Documentation
- [README.md](../README.md) - Project overview and setup
- [DATABASE_SCHEMA.md](./DATABASE_SCHEMA.md) - Database structure and relationships
- [API_REFERENCE.md](./API_REFERENCE.md) - API endpoints and usage
- [COMPONENT_ARCHITECTURE.md](./COMPONENT_ARCHITECTURE.md) - Component design patterns
- [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) - Setup and deployment instructions

### Feature-Specific Documentation
- [MSG91_INTEGRATION.md](./MSG91_INTEGRATION.md) - Email service integration
- [BOOKING_EMAIL_INTEGRATION.md](./BOOKING_EMAIL_INTEGRATION.md) - Booking confirmation emails

### Database Documentation
- `supabase/run-functions.sql` - Database setup and functions
- `supabase/functions/` - Custom database functions
- `src/integrations/supabase/types.ts` - Generated TypeScript types

## 🛠️ Development Tools

### Build & Development
- **Vite**: Fast build tool and dev server
- **TypeScript**: Type safety and developer experience
- **ESLint**: Code linting and quality
- **Prettier**: Code formatting (via Lovable)

### UI Development
- **Tailwind CSS**: Utility-first styling
- **shadcn/ui**: Component library
- **Framer Motion**: Animations and transitions
- **Lucide React**: Icon library

### State Management
- **TanStack Query**: Server state management
- **React Context**: Global application state
- **Supabase Realtime**: Live data synchronization

## 🔍 Key Files Reference

### Configuration Files
```
├── vite.config.ts          # Build configuration
├── tailwind.config.ts      # Styling configuration
├── tsconfig.json           # TypeScript configuration
├── package.json            # Dependencies and scripts
└── components.json         # shadcn/ui configuration
```

### Core Application Files
```
src/
├── App.tsx                 # Main app component with routing
├── main.tsx               # Application entry point
├── index.css              # Global styles
└── vite-env.d.ts          # Vite type definitions
```

### Database & Backend
```
supabase/
├── config.toml            # Supabase configuration
├── run-functions.sql      # Database setup script
└── functions/             # Custom database functions
    ├── get_available_slots.sql
    ├── prevent-double-bookings.sql
    └── admin-dashboard-functions.sql
```

### Integration Files
```
src/integrations/supabase/
├── client.ts              # Supabase client configuration
├── types.ts               # Generated database types
├── realtime.ts            # Real-time subscriptions
└── custom-types.ts        # Custom type definitions
```

### Service Files
```
src/services/
├── customAuthService.ts   # Authentication service
├── msg91EmailService.ts   # Email service integration
└── [other-services]       # Additional service integrations
```

### Utility Files
```
src/utils/
├── adminSecurity.ts       # Admin security utilities
├── dateUtils.ts           # Date manipulation helpers
├── logger.ts              # Logging utilities
├── protectedSupabase.ts   # Protected database operations
└── [other-utils]          # Additional utilities
```

## 🚀 Getting Started Checklist

### For New Developers
1. [ ] Read [README.md](../README.md) for project overview
2. [ ] Follow [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) for local setup
3. [ ] Review [DATABASE_SCHEMA.md](./DATABASE_SCHEMA.md) for data structure
4. [ ] Study [COMPONENT_ARCHITECTURE.md](./COMPONENT_ARCHITECTURE.md) for code patterns
5. [ ] Explore key components in `src/components/`
6. [ ] Test booking flow and admin features
7. [ ] Review API integration in [API_REFERENCE.md](./API_REFERENCE.md)

### For Contributors
1. [ ] Set up local development environment
2. [ ] Create feature branch for changes
3. [ ] Follow existing code patterns and conventions
4. [ ] Test changes thoroughly
5. [ ] Update documentation if needed
6. [ ] Submit pull request with clear description

## 📞 Support & Resources

### Development Support
- **Lovable Platform**: AI-assisted development
- **Supabase Docs**: Database and backend services
- **React Docs**: Frontend framework documentation
- **Tailwind CSS Docs**: Styling framework

### Community & Help
- Project repository issues for bug reports
- Lovable community for development assistance
- Supabase community for backend support

This index provides a comprehensive overview of the Grid2play codebase structure and serves as a starting point for understanding and contributing to the project.
