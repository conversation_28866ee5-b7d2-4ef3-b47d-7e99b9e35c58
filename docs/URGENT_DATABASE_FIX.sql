-- URGENT: Create missing email_verification_tokens table
-- Run this immediately in Supabase SQL Editor

-- Create email verification tokens table
CREATE TABLE IF NOT EXISTS email_verification_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  token TEXT NOT NULL UNIQUE,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token ON email_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);

-- Create RLS policies
ALTER TABLE email_verification_tokens ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own tokens
CREATE POLICY "Users can view own verification tokens" ON email_verification_tokens
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Service role can manage all tokens (for Edge Functions)
CREATE POLICY "Service role can manage verification tokens" ON email_verification_tokens
  FOR ALL USING (auth.role() = 'service_role');

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_email_verification_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_email_verification_tokens_updated_at
  BEFORE UPDATE ON email_verification_tokens
  FOR EACH ROW
  EXECUTE FUNCTION update_email_verification_tokens_updated_at();

-- Verify your current user profile
SELECT 
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified,
  created_at
FROM profiles 
WHERE id = '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a';

-- Check if phone profile exists
SELECT 
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified
FROM profiles 
WHERE phone = '+************';

-- If no profile found, create it manually (EMERGENCY FIX)
-- Only run this if the SELECT above returns no results
/*
INSERT INTO profiles (
  id, 
  full_name, 
  phone, 
  email, 
  phone_verified, 
  email_verified,
  created_at,
  updated_at
) VALUES (
  '95ca34a0-1e40-4bd6-85d8-5719f3e6c28a',
  'Whatsapp ani',
  '+************',
  '<EMAIL>',
  true,
  false,
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  phone = EXCLUDED.phone,
  phone_verified = EXCLUDED.phone_verified,
  updated_at = NOW();
*/

-- Verify the fix
SELECT 'email_verification_tokens table created' as status;
SELECT COUNT(*) as token_table_exists FROM information_schema.tables WHERE table_name = 'email_verification_tokens';
SELECT COUNT(*) as profile_exists FROM profiles WHERE phone = '+************';
