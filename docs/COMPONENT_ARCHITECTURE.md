# Component Architecture Documentation

This document outlines the component architecture of Grid2play, including component hierarchy, design patterns, and best practices.

## Overview

Grid2play follows a modular component architecture with clear separation of concerns:
- **UI Components**: Reusable interface elements
- **Business Components**: Feature-specific logic components
- **Layout Components**: Page structure and navigation
- **Context Providers**: Global state management
- **Custom Hooks**: Reusable business logic

## Directory Structure

```
src/
├── components/
│   ├── ui/                 # Base UI components (shadcn/ui)
│   ├── admin/              # Admin-specific components
│   ├── challenge/          # Challenge system components
│   ├── tournament/         # Tournament management components
│   └── [feature-components] # Feature-specific components
├── pages/                  # Route-level components
├── context/                # React context providers
├── hooks/                  # Custom React hooks
├── services/               # External service integrations
├── utils/                  # Utility functions
└── types/                  # TypeScript definitions
```

## Core Component Categories

### 1. UI Components (`/components/ui/`)

Base design system components built on shadcn/ui:

#### Form Components
- `Button` - Primary action buttons with variants
- `Input` - Text input fields with validation
- `Select` - Dropdown selection components
- `Checkbox` - Boolean input controls
- `RadioGroup` - Single selection from multiple options
- `DatePicker` - Date selection with calendar
- `TimePicker` - Time selection interface

#### Layout Components
- `Card` - Content containers with consistent styling
- `Dialog` - Modal dialogs and overlays
- `Sheet` - Slide-out panels for mobile
- `Tabs` - Tabbed content organization
- `Accordion` - Collapsible content sections
- `Separator` - Visual content dividers

#### Navigation Components
- `BottomNav` - Mobile bottom navigation
- `AdminBottomNav` - Admin-specific navigation
- `Header` - Top navigation and branding
- `Breadcrumb` - Hierarchical navigation

#### Feedback Components
- `Toast` - Temporary notification messages
- `Alert` - Persistent alert messages
- `Progress` - Progress indicators
- `Skeleton` - Loading state placeholders

### 2. Business Logic Components

#### Booking System
```typescript
// BookSlotModal.tsx - Main booking interface
interface BookSlotModalProps {
  isOpen: boolean;
  onClose: () => void;
  courtId: string;
  selectedDate: Date;
  selectedSlot: TimeSlot;
}

// AvailabilityWidget.tsx - Slot availability display
interface AvailabilityWidgetProps {
  courtId: string;
  selectedDate: Date;
  onSlotSelect: (slot: TimeSlot) => void;
}

// BookingDetailsModal.tsx - Booking information display
interface BookingDetailsModalProps {
  booking: Booking;
  isOpen: boolean;
  onClose: () => void;
  onCancel?: (bookingId: string) => void;
}
```

#### Venue Management
```typescript
// VenueImageCarousel.tsx - Venue image gallery
interface VenueImageCarouselProps {
  images: string[];
  venueName: string;
  className?: string;
}

// NearbyVenues.tsx - Location-based venue discovery
interface NearbyVenuesProps {
  userLocation: GeolocationCoordinates;
  maxDistance?: number;
  sportFilter?: string;
}

// VenueReviews.tsx - Review display and submission
interface VenueReviewsProps {
  venueId: string;
  canSubmitReview: boolean;
  onReviewSubmit: (review: ReviewData) => void;
}
```

#### Tournament System
```typescript
// TournamentCard.tsx - Tournament display card
interface TournamentCardProps {
  tournament: Tournament;
  onRegister: (tournamentId: string) => void;
  userRegistered: boolean;
}

// TournamentBracket.tsx - Tournament bracket visualization
interface TournamentBracketProps {
  fixtures: TournamentFixture[];
  tournamentId: string;
  canUpdateResults: boolean;
}

// TournamentRegistration.tsx - Registration form
interface TournamentRegistrationProps {
  tournament: Tournament;
  onSubmit: (registrationData: RegistrationData) => void;
}
```

### 3. Admin Components (`/components/admin/`)

#### Dashboard Components
```typescript
// AdminDashboard.tsx - Main admin interface
// BookingManagement.tsx - Booking administration
// VenueManagement.tsx - Venue administration
// UserManagement.tsx - User role management
// AnalyticsDashboard.tsx - Business analytics
```

#### Mobile Admin Components
```typescript
// AdminHome_Mobile.tsx - Mobile admin dashboard
// Bookings_Mobile.tsx - Mobile booking management
// VenueManagement_Mobile.tsx - Mobile venue admin
// AnalyticsDashboard_Mobile.tsx - Mobile analytics
```

### 4. Context Providers (`/context/`)

#### AuthContext
```typescript
interface AuthContextProps {
  user: User | null;
  session: Session | null;
  loading: boolean;
  userRole: 'user' | 'admin' | 'super_admin' | null;
  signUp: (email: string, password: string, userData: any) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  isSessionExpired: boolean;
}
```

### 5. Custom Hooks (`/hooks/`)

#### Business Logic Hooks
```typescript
// useBookingEmail.ts - Booking email functionality
export const useBookingEmail = () => {
  const sendBookingConfirmation = async (bookingData: BookingData) => {
    // Email sending logic
  };
  
  return { sendBookingConfirmation };
};

// use-enhanced-location.tsx - Location services
export const useEnhancedLocation = () => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  return { location, loading, error, getCurrentLocation, searchLocation };
};

// use-mobile.tsx - Mobile detection
export const useMobile = () => {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  return isMobile;
};
```

## Design Patterns

### 1. Compound Components
Used for complex UI components with multiple related parts:

```typescript
// Example: BookingModal compound component
const BookingModal = ({ children, ...props }) => {
  return <Dialog {...props}>{children}</Dialog>;
};

BookingModal.Header = ({ children }) => <DialogHeader>{children}</DialogHeader>;
BookingModal.Content = ({ children }) => <DialogContent>{children}</DialogContent>;
BookingModal.Footer = ({ children }) => <DialogFooter>{children}</DialogFooter>;

// Usage
<BookingModal isOpen={isOpen} onClose={onClose}>
  <BookingModal.Header>
    <h2>Book Your Slot</h2>
  </BookingModal.Header>
  <BookingModal.Content>
    <BookingForm />
  </BookingModal.Content>
  <BookingModal.Footer>
    <Button onClick={onConfirm}>Confirm Booking</Button>
  </BookingModal.Footer>
</BookingModal>
```

### 2. Render Props Pattern
For flexible component composition:

```typescript
interface DataFetcherProps<T> {
  children: (data: T | null, loading: boolean, error: string | null) => React.ReactNode;
  fetchFn: () => Promise<T>;
}

const DataFetcher = <T,>({ children, fetchFn }: DataFetcherProps<T>) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchFn()
      .then(setData)
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, [fetchFn]);

  return <>{children(data, loading, error)}</>;
};
```

### 3. Higher-Order Components (HOCs)
For cross-cutting concerns:

```typescript
// withAuth HOC for protected components
const withAuth = <P extends object>(Component: React.ComponentType<P>) => {
  return (props: P) => {
    const { user, loading } = useAuth();
    
    if (loading) return <LoadingSpinner />;
    if (!user) return <Navigate to="/login" />;
    
    return <Component {...props} />;
  };
};

// Usage
const ProtectedBookings = withAuth(Bookings);
```

### 4. Custom Hook Pattern
For reusable business logic:

```typescript
// useBookingManagement hook
export const useBookingManagement = (userId: string) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchBookings = useCallback(async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('user_id', userId);
      
      if (error) throw error;
      setBookings(data || []);
    } catch (error) {
      console.error('Error fetching bookings:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  const cancelBooking = useCallback(async (bookingId: string) => {
    // Cancellation logic
  }, []);

  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  return {
    bookings,
    loading,
    fetchBookings,
    cancelBooking
  };
};
```

## State Management Strategy

### 1. Local State (useState)
For component-specific state that doesn't need to be shared.

### 2. Context API
For global application state like authentication and theme.

### 3. TanStack Query
For server state management and caching:

```typescript
// Query for venue data
const useVenues = () => {
  return useQuery({
    queryKey: ['venues'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('venues')
        .select('*')
        .eq('is_active', true);
      
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
```

### 4. Real-time State
Using Supabase real-time subscriptions for live updates:

```typescript
const useRealtimeBookings = (userId: string) => {
  const [bookings, setBookings] = useState<Booking[]>([]);

  useEffect(() => {
    const subscription = supabase
      .channel('booking-updates')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'bookings',
        filter: `user_id=eq.${userId}`
      }, (payload) => {
        // Handle real-time updates
        setBookings(prev => updateBookingsArray(prev, payload));
      })
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [userId]);

  return bookings;
};
```

## Performance Optimization

### 1. Code Splitting
```typescript
// Lazy loading for route components
const AdminDashboard = lazy(() => import('./pages/admin/Dashboard'));
const TournamentDashboard = lazy(() => import('./pages/tournament/TournamentDashboard'));

// Usage with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <AdminDashboard />
</Suspense>
```

### 2. Memoization
```typescript
// Memoized expensive calculations
const ExpensiveComponent = memo(({ data }: { data: ComplexData }) => {
  const processedData = useMemo(() => {
    return expensiveDataProcessing(data);
  }, [data]);

  return <div>{processedData}</div>;
});
```

### 3. Virtual Scrolling
For large lists of bookings or venues:

```typescript
import { FixedSizeList as List } from 'react-window';

const VirtualizedBookingList = ({ bookings }: { bookings: Booking[] }) => (
  <List
    height={600}
    itemCount={bookings.length}
    itemSize={80}
    itemData={bookings}
  >
    {({ index, style, data }) => (
      <div style={style}>
        <BookingCard booking={data[index]} />
      </div>
    )}
  </List>
);
```

## Testing Strategy

### 1. Component Testing
```typescript
// Example component test
import { render, screen, fireEvent } from '@testing-library/react';
import { BookSlotModal } from './BookSlotModal';

describe('BookSlotModal', () => {
  it('should render booking form when open', () => {
    render(
      <BookSlotModal
        isOpen={true}
        onClose={jest.fn()}
        courtId="test-court"
        selectedDate={new Date()}
        selectedSlot={mockSlot}
      />
    );

    expect(screen.getByText('Book Your Slot')).toBeInTheDocument();
  });
});
```

### 2. Hook Testing
```typescript
import { renderHook, act } from '@testing-library/react';
import { useBookingManagement } from './useBookingManagement';

describe('useBookingManagement', () => {
  it('should fetch bookings on mount', async () => {
    const { result } = renderHook(() => useBookingManagement('user-id'));

    expect(result.current.loading).toBe(true);
    
    await act(async () => {
      // Wait for async operations
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.bookings).toBeDefined();
  });
});
```

This component architecture ensures maintainability, reusability, and scalability while providing a consistent user experience across the Grid2play platform.
