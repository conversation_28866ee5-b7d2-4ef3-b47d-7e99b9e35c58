-- Debug Phone Login Issue
-- Run this in Supabase SQL Editor to check the exact data

-- 1. Check the exact profile data
SELECT 
    id,
    full_name,
    phone,
    email,
    phone_verified,
    pg_typeof(phone_verified) as phone_verified_type,
    created_at
FROM profiles 
WHERE phone = '+918448609110';

-- 2. Check if there are any similar phone numbers
SELECT 
    id,
    full_name,
    phone,
    email,
    phone_verified,
    pg_typeof(phone_verified) as phone_verified_type
FROM profiles 
WHERE phone LIKE '%8448609110%';

-- 3. Check all profiles to see the phone_verified data types
SELECT 
    phone,
    phone_verified,
    pg_typeof(phone_verified) as phone_verified_type,
    COUNT(*) as count
FROM profiles 
GROUP BY phone, phone_verified, pg_typeof(phone_verified)
ORDER BY count DESC;

-- 4. Test the exact query the frontend is using
SELECT 
    email, 
    phone_verified, 
    phone,
    pg_typeof(phone_verified) as phone_verified_type
FROM profiles 
WHERE phone = '+918448609110';

-- 5. Check if the user exists in auth.users table
SELECT 
    u.id,
    u.email,
    u.phone,
    u.created_at,
    p.phone as profile_phone,
    p.phone_verified
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE u.phone = '+918448609110' OR p.phone = '+918448609110';
